/**
 * 106 debounceUtils.ts
 * 
 * 该模块提供了实现防抖功能的实用函数。
 * 防抖是一种编程实践，用于确保耗时的任务不会过于频繁地触发，
 * 从而有助于性能优化。
 */

/**
 * Debouncer 状态接口
 */
interface DebounceState {
  timer: ReturnType<typeof setTimeout>;
  reject: () => void;
}

/**
 * 实现防抖功能的类，具有取消待处理操作的能力。
 */
export class Debouncer {
  // 用于存储当前的定时器和 reject 方法
  private state?: DebounceState;

  /**
   * 对任务执行进行防抖处理。
   * @param delay 延迟的毫秒数
   * @returns 一个 Promise，当防抖延迟时间内没有新的调用时解决
   */
  async debounce(delay: number): Promise<void> {
    // 如果已有定时器，则清除并拒绝上一个 Promise
    if (this.state) {
      clearTimeout(this.state.timer);
      this.state.reject();
      this.state = undefined;
    }
    // 返回新的 Promise，并在 delay 后 resolve
    return new Promise<void>((resolve, reject) => {
      this.state = {
        timer: setTimeout(() => resolve(), delay),
        reject: reject,
      };
    });
  }
}

/**
 * 创建一个函数的防抖版本。
 * @param delay 延迟的毫秒数
 * @param callback 需要进行防抖处理的函数
 * @returns 一个新的经过防抖处理的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  delay: number,
  callback: T
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
  // 用于存储定时器 id
  let timeoutId: ReturnType<typeof setTimeout> | undefined;

  // 返回防抖处理后的函数
  return (...args: Parameters<T>): Promise<ReturnType<T>> => {
    // 如果已有定时器，则清除
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    // 返回 Promise，在 delay 后执行 callback
    return new Promise<ReturnType<T>>((resolve) => {
      timeoutId = setTimeout(() => {
        const result = callback(...args);
        resolve(result);
      }, delay);
    });
  };
}
