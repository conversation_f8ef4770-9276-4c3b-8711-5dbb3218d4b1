import Parser from 'web-tree-sitter';
import * as path from 'path';
import * as vscode from 'vscode';

export const languageIdToWasmLanguageMapping = {
  python: 'python',
  javascript: 'javascript',
  javascriptreact: 'javascript',
  jsx: 'javascript',
  typescript: 'typescript',
  typescriptreact: 'tsx',
  tsx: 'tsx',
  go: 'go',
  ruby: 'ruby',
  java: 'java',
  dart: 'dart',
} as any;;

export const languageCommentMarkers = {
  abap: {
    start: '"',
    end: '',
  },
  bat: {
    start: 'REM',
    end: '',
  },
  bibtex: {
    start: '%',
    end: '',
  },
  blade: {
    start: '#',
    end: '',
  },
  c: {
    start: '//',
    end: '',
  },
  clojure: {
    start: ';',
    end: '',
  },
  coffeescript: {
    start: '//',
    end: '',
  },
  cpp: {
    start: '//',
    end: '',
  },
  csharp: {
    start: '//',
    end: '',
  },
  css: {
    start: '/*',
    end: '*/',
  },
  dart: {
    start: '//',
    end: '',
  },
  dockerfile: {
    start: '#',
    end: '',
  },
  elixir: {
    start: '#',
    end: '',
  },
  erb: {
    start: '<%#',
    end: '%>',
  },
  erlang: {
    start: '%',
    end: '',
  },
  fsharp: {
    start: '//',
    end: '',
  },
  go: {
    start: '//',
    end: '',
  },
  groovy: {
    start: '//',
    end: '',
  },
  haml: {
    start: '-#',
    end: '',
  },
  handlebars: {
    start: '{{!',
    end: '}}',
  },
  haskell: {
    start: '--',
    end: '',
  },
  html: {
    start: '<!--',
    end: '-->',
  },
  ini: {
    start: ';',
    end: '',
  },
  java: {
    start: '//',
    end: '',
  },
  javascript: {
    start: '//',
    end: '',
  },
  javascriptreact: {
    start: '//',
    end: '',
  },
  jsonc: {
    start: '//',
    end: '',
  },
  jsx: {
    start: '//',
    end: '',
  },
  julia: {
    start: '#',
    end: '',
  },
  kotlin: {
    start: '//',
    end: '',
  },
  latex: {
    start: '%',
    end: '',
  },
  less: {
    start: '//',
    end: '',
  },
  lua: {
    start: '--',
    end: '',
  },
  makefile: {
    start: '#',
    end: '',
  },
  markdown: {
    start: '[]: #',
    end: '',
  },
  'objective-c': {
    start: '//',
    end: '',
  },
  'objective-cpp': {
    start: '//',
    end: '',
  },
  perl: {
    start: '#',
    end: '',
  },
  php: {
    start: '//',
    end: '',
  },
  powershell: {
    start: '#',
    end: '',
  },
  pug: {
    start: '//',
    end: '',
  },
  python: {
    start: '#',
    end: '',
  },
  ql: {
    start: '//',
    end: '',
  },
  r: {
    start: '#',
    end: '',
  },
  razor: {
    start: '<!--',
    end: '-->',
  },
  ruby: {
    start: '#',
    end: '',
  },
  rust: {
    start: '//',
    end: '',
  },
  sass: {
    start: '//',
    end: '',
  },
  scala: {
    start: '//',
    end: '',
  },
  scss: {
    start: '//',
    end: '',
  },
  shellscript: {
    start: '#',
    end: '',
  },
  slim: {
    start: '/',
    end: '',
  },
  solidity: {
    start: '//',
    end: '',
  },
  sql: {
    start: '--',
    end: '',
  },
  stylus: {
    start: '//',
    end: '',
  },
  svelte: {
    start: '<!--',
    end: '-->',
  },
  swift: {
    start: '//',
    end: '',
  },
  terraform: {
    start: '#',
    end: '',
  },
  tex: {
    start: '%',
    end: '',
  },
  typescript: {
    start: '//',
    end: '',
  },
  typescriptreact: {
    start: '//',
    end: '',
  },
  vb: {
    start: "'",
    end: '',
  },
  verilog: {
    start: '//',
    end: '',
  },
  'vue-html': {
    start: '<!--',
    end: '-->',
  },
  vue: {
    start: '//',
    end: '',
  },
  xml: {
    start: '<!--',
    end: '-->',
  },
  xsl: {
    start: '<!--',
    end: '-->',
  },
  yaml: {
    start: '#',
    end: '',
  },
} as any;
export const dontAddLanguageMarker = ['php', 'plaintext'];
export const shebangLines = {
  html: '<!DOCTYPE html>',
  python: '#!/usr/bin/env python3',
  ruby: '#!/usr/bin/env ruby',
  shellscript: '#!/bin/sh',
  yaml: '# YAML data',
} as any;


/**
 * 检查给定的语言ID是否受支持
 * @param languageId 要检查的语言ID
 * @returns 如果语言ID受支持则返回true，否则返回false
 */
export function isSupportedLanguageId(languageId :string) {
  return languageId in languageIdToWasmLanguageMapping;
}
export function languageIdToWasmLanguage(languageId :string) {
  if (!(languageId in languageIdToWasmLanguageMapping)) throw new Error(`Unrecognized language: ${languageId}`);
  return languageIdToWasmLanguageMapping[languageId];
}
export function getBlockCloseToken(language :string) {
  switch (languageIdToWasmLanguage(language)) {
    case 'python':
      return null;
    case 'javascript':
    case 'typescript':
    case 'tsx':
    case 'go':
    case 'java':
      return '}';
    case 'ruby':
      return 'end';
  }
}
/**
 * 执行内部查询并返回匹配结果。
 * @param queries 查询数组，每个查询包含一个查询字符串和一个可选的预编译查询对象。
 * @param root 查询的根节点，包含树和语言信息。
 * @returns 匹配结果数组。
 */
export function innerQuery(queries:any, root:any) {
  const matches: any = [];
  for (let index = 0; index < queries.length; index++) {
    const query = queries[index];
    if (!query[1]) {
      try {
        const lang = root.tree.getLanguage();
        query[1] = lang.query(query[0]);
      } catch (error) {
        console.error('%c [ error ]-423', 'font-size:13px; background:pink; color:#bf2c9f;', error);
      }
    }
    matches.push(...query[1].matches(root));
  }
  // for (const query of queries) {
  // }
  return matches;
}
/**
 * 根据语言和根节点查询函数。
 * @param language - 目标编程语言。
 * @param root - 查询的根节点。
 * @returns 查询结果。
 */
export function queryFunctions(language:any, root:any) {
  const activeTextEditor = vscode.window.activeTextEditor;
  if (activeTextEditor) {
    const documentLanguageId: string = activeTextEditor.document.languageId;
    if (documentLanguageId === 'vue') {
      const queries = vueFunctionsQuery[languageIdToWasmLanguage(language)];
      return innerQuery(queries, root);
    }
  }
  const queries = functionQuery[languageIdToWasmLanguage(language)];
  return innerQuery(queries, root);
}
export function queryImports(language:any, root:any) {
  const queries = importsQuery[languageIdToWasmLanguage(language)];
  return innerQuery(queries, root);
}
export function queryExports(language:any, root:any) {
  const queries = exportsQuery[languageIdToWasmLanguage(language)];
  return innerQuery(queries, root);
}
export function queryGlobalVars(language:any, root:any) {
  const queries = globalVarsQuery[languageIdToWasmLanguage(language)];
  return innerQuery(queries, root);
}
export function queryPythonIsDocstring(blockNode:any) {
  return innerQuery([docstringQuery], blockNode).length == 1;
}
export function getAncestorWithSiblingFunctions(language:any, nd:any) {
  const check = isFunctionParent[languageIdToWasmLanguage(language)];
  for (; nd.parent; ) {
    if (check(nd.parent)) return nd;
    nd = nd.parent;
  }
  return nd.parent ? nd : null;
}
/**
 * 判断节点类型是否为函数类型。
 * @param language - 语言标识符。
 * @param nd - 节点对象。
 * @returns 如果节点类型为函数类型则返回true，否则返回false。
 */
export function isFunction(language:any, nd:any) {
  return functionTypes[languageIdToWasmLanguage(language)].has(nd.type);
}
/**
 * 判断节点是否为函数定义
 * @param language - 语言类型
 * @param nd - 节点对象
 * @returns 如果是函数定义返回true，否则返回false
 */
export function isFunctionDefinition(language:any, nd:any) {
  switch (languageIdToWasmLanguage(language)) {
    case 'python':
    case 'go':
    case 'ruby':
      return isFunction(language, nd);
    case 'javascript':
    case 'typescript':
    case 'tsx':
      if (
        nd.type === 'function_declaration' ||
        nd.type === 'generator_function_declaration' ||
        nd.type === 'method_definition'
      ) {
        return true;
      }
      if (nd.type === 'lexical_declaration' || nd.type === 'variable_declaration') {
        if (nd.namedChildCount > 1) return !1;
        const declarator = nd.namedChild(0);
        if (declarator == null) return !1;
        const init = declarator.namedChild(1);
        return init !== null && isFunction(language, init);
      }
      if (nd.type === 'expression_statement') {
        const expr = nd.namedChild(0);
        if (expr?.type === 'assignment_expression') {
          const rhs = expr.namedChild(1);
          return rhs !== null && isFunction(language, rhs);
        }
      }
      return false;
    case 'java':
      return nd.type === 'method_declaration';
  }
}
/**
 * 获取节点的前一个注释节点
 * @param nd 要查找的节点
 * @returns 如果找到注释节点则返回该节点，否则返回null
 */
export function getFirstPrecedingComment(nd:any) {
  let cur = nd;
  for (; cur.previousSibling?.type === 'comment'; ) {
    const prev = cur.previousSibling;
    if (prev.endPosition.row < cur.startPosition.row - 1) break;
    cur = prev;
  }
  return cur?.type === 'comment' ? cur : null;
}
export const vueFunctionQuery = `[
  (function_declaration)
  (method_definition)
  (variable_declarator
    name: (identifier)
    value: (arrow_function)
  )
  (variable_declarator
    name: (identifier)
    value: (generator_function)
  )
  (variable_declarator
    name: (identifier)
    value: (expression)
  )
  (field_definition
    value: (expression)
  )
  (function_declaration
    body: (statement_block) @body
  )
] @methods`;
export const jsFunctionQuery = `[
  (function_declaration (statement_block) @body)
  (function_declaration body: (statement_block) @body)
  (generator_function body: (statement_block) @body)
  (generator_function_declaration body: (statement_block) @body)
  (method_definition body: (statement_block) @body)
  (arrow_function body: (statement_block) @body)
  (class_declaration body: (class_body ("{") @class.body.cursor) @class.body)
  (comment) @comment
] @function`;
export const vueFunctionsQuery = {
  javascript: [[vueFunctionQuery]],
  typescript: [[jsFunctionQuery]],
} as any;
export const functionQuery = {
  python: [
    [
      `(function_definition body: (block
           (expression_statement (string))? @docstring) @body) @function`,
    ],
    ['(ERROR ("def" (identifier) (parameters))) @function'],
  ],
  javascript: [[jsFunctionQuery]],
  typescript: [[jsFunctionQuery]],
  tsx: [[jsFunctionQuery]],
  go: [
    [
      `[
          (function_declaration body: (block) @body)
          (method_declaration body: (block) @body)
        ] @function`,
    ],
  ],
  ruby: [
    [
      `[
          (method name: (_) parameters: (method_parameters)? @params [(_)+ "end"] @body)
          (singleton_method name: (_) parameters: (method_parameters)? @params [(_)+ "end"] @body)
        ] @function`,
    ],
  ],
  java: [
    [
      `(method_declaration
        name: (identifier) @functionName
        parameters: (formal_parameters) @params
        body: (block) @body
      ) @function`,
    ],
  ],
} as any;;
export const requireCall = '(call_expression function: ((identifier) @req (#eq? @req "require")))';
export const declaratorWithRequire = `(variable_declarator value: ${requireCall})`,
  commonJsImport = `
  (lexical_declaration ${declaratorWithRequire}+)
  (variable_declaration ${declaratorWithRequire}+)
`;
export const tsImportQueries = [
  [`(program [ ${commonJsImport} ] @import)`],
  ['(program [ (import_statement) (import_alias) ] @import)'],
];
export const importsQuery = {
  python: [
    ['(module (future_import_statement) @import)'],
    ['(module (import_statement) @import)'],
    ['(module (import_from_statement) @import)'],
  ],
  javascript: [[`(program [ ${commonJsImport} ] @import)`], ['(program [ (import_statement) ] @import)']],
  typescript: tsImportQueries,
  tsx: tsImportQueries,
  go: [],
  ruby: [],
  java: [
    [
      // Match import statements
      `(import_declaration
        (scoped_identifier) @importIdentifier
      )`,
    ],
  ],
} as any;;
export const jsExportQueries = [['(program (export_statement) @export)']] as any ,
  exportsQuery = {
    python: [],
    javascript: jsExportQueries,
    typescript: jsExportQueries,
    tsx: jsExportQueries,
    go: [],
    ruby: [],
    java: [
      [
        // Match public classes
        `(class_declaration
          (modifiers
            (modifier) @publicModifier
          )
          name: (identifier) @className
          body: (class_body)
        )`,
      ],
      [
        // Match public methods
        `(method_declaration
          (modifiers
            (modifier) @publicModifier
          )
          name: (identifier) @methodName
          body: (block)
        )`,
      ],
    ],
  } as any;
export const globalVarsQuery = {
  python: [['(module (global_statement) @globalVar)'], ['(module (expression_statement) @globalVar)']],
  javascript: [],
  typescript: [],
  tsx: [],
  go: [],
  ruby: [],
  java: [
    [
      `(class_body
        (field_declaration
          (variable_declarator
            name: (identifier) @globalVar
          )
        )
      )`,
    ],
  ],
}as any;
export const jsFunctionTypes = [
  'function',
  'function_declaration',
  'generator_function',
  'generator_function_declaration',
  'method_definition',
  'arrow_function',
  'class_declaration',
  'comment',
]as any;
export const javaFunctionTypes = [
  'method_declaration', // Regular method declaration in a class or interface
  'constructor_declaration', // Constructor declaration in a class
  'lambda_expression', // Lambda expression (anonymous function)
  'annotation_method_declaration', // Method declaration within an annotation type
  'interface_method_declaration', // Method declaration within an interface
  'comment', // Comments (for completeness, though not a function type)
]as any;

export const functionTypes = {
  python: new Set(['function_definition']),
  javascript: new Set(jsFunctionTypes),
  typescript: new Set(jsFunctionTypes),
  tsx: new Set(jsFunctionTypes),
  go: new Set(['function_declaration', 'method_declaration']),
  ruby: new Set(['method', 'singleton_method']),
  java: new Set(javaFunctionTypes),
}as any;
export const docstringQuery = [
  `[
    (class_definition (block (expression_statement (string))))
    (function_definition (block (expression_statement (string))))
]`,
]as any;
export const callSiteQuery = {
  python: [
    [
      `(call
        function:  [
            (identifier) @caller
            (attribute attribute:(identifier) @caller)
        ]
        arguments: (argument_list) @args
    )`,
    ],
  ],
  javascript: [],
  tsx: [],
  typescript: [],
  go: [],
  ruby: [],
  java: [
    [
      `(method_invocation
        object: (identifier) @caller
        name: (identifier) @method
        arguments: (argument_list) @args
      )`,
    ],
  ],
}as any;
export const isFunctionParent = {
  python: (nd:any) => nd.type === 'module' || (nd.type === 'block' && nd.parent?.type === 'class_definition'),
  javascript: (nd:any) => nd.type === 'program' || nd.type === 'class_body',
  typescript: (nd:any) => nd.type === 'program' || nd.type === 'class_body',
  tsx: (nd:any) => nd.type === 'program' || nd.type === 'class_body',
  go: (nd:any) => nd.type === 'source_file',
  ruby: (nd:any) => nd.type === 'program' || nd.type === 'class',
  java: (nd:any) => nd.type === 'class_body' || nd.type === 'interface_body',
}as any;
export const loadedLanguages = new Map();

/**
 * 将vscode.Position转换为查询点对象。
 * @param position - 包含行和字符的vscode.Position对象。
 * @returns 包含起始点和结束点的对象。
 */
export function positionToQueryPoints(position: Pick<vscode.Position, 'line' | 'character'>) {
  const startPoint = {
    row: position.line,
    column: position.character,
  };

  const endPoint = {
    row: position.line,
    // 在触发位置后一个字符。
    column: position.character + 1,
  };

  return { startPoint, endPoint };
}

interface LanguageConfig {
  blockStart: string;
  blockElseTest: RegExp;
  blockEnd: string | null;
  commentStart: string;
}

export function getLanguageConfig(languageId: string): LanguageConfig | null {
  switch (languageId) {
    case 'astro':
    case 'c':
    case 'cpp':
    case 'csharp':
    case 'dart':
    case 'go':
    case 'java':
    case 'javascript':
    case 'javascriptreact':
    case 'kotlin':
    case 'php':
    case 'rust':
    case 'svelte':
    case 'typescript':
    case 'typescriptreact':
    case 'vue':
      return {
        blockStart: '{',
        blockElseTest: /^[\t ]*} else/,
        blockEnd: '}',
        commentStart: `// `,
      };
    case 'python': {
      return {
        blockStart: ':',
        blockElseTest: /^[\t ]*(elif |else:)/,
        blockEnd: null,
        commentStart: `# `,
      };
    }
    case 'elixir': {
      return {
        blockStart: 'do',
        blockElseTest: /^[\t ]*(else|else do)/,
        blockEnd: 'end',
        commentStart: `# `,
      };
    }
    default:
      return null;
  }
}
