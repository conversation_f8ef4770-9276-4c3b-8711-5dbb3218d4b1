/**
 * 4419 openAIFetcher.ts
 * 
 * 该文件主要功能：
 * 1. 提供与OpenAI API交互的功能，包括发送请求和处理响应
 * 2. 处理请求ID、处理时间和引擎名称的提取
 * 3. 实现OpenAI获取器（Fetcher）的基类和实时获取器
 * 4. 处理完成（completions）的后处理和流式传输
 * 5. 提供错误处理和遥测数据收集的功能
 * 
 * 该文件是Copilot插件与OpenAI服务器通信的核心组件，负责管理API请求、
 * 处理响应，并提供了一系列工具函数来支持这些操作。
 */

import * as utilModule from 'util';
//import * as tokenManager from './362';
import * as asyncOps from './asyncIterableOperations';
import * as configManager from './configurationManager';
import * as featureManager from './featureManager';
import * as ghostTextDebounce from './ghostTextDebounceManager';
import {Logger,LogLevel} from './loggerManager';
import * as networking from './networkingModule';
//import * as statusReporter from './statusReporter';
import * as githubUtils from './repoInfoManager';
import * as repetitionFilter from './repetitionDetector';
//import * as telemetry from './telemetryManager';
import * as temperature from './completionUtils';
import * as sseProcessor from './sseStreamProcessor';

import {My_Context} from './contextType'

const logger = new Logger(LogLevel.INFO, "fetch");

// 定义CopilotUiKind枚举
export enum CopilotUiKind {
  GhostText = "ghostText",
  Panel = "synthesize"
}

// 定义请求ID相关信息的接口
interface RequestIdInfo {
  headerRequestId: string;
  completionId: string;
  created: number;
  serverExperiments: string;
  deploymentId: string;
}

/**
 * 获取请求ID相关信息
 * @param context - 请求上下文
 * @param completion - 完成对象
 * @returns 包含请求ID相关信息的对象
 */
export function getRequestId(context: any, completion?: any): RequestIdInfo {
  return {
    headerRequestId: context.headers.get("x-request-id") || "",
    completionId: completion && completion.id ? completion.id : "",
    created: completion && completion.created ? completion.created : 0,
    serverExperiments: context.headers.get("X-Copilot-Experiment") || "",
    deploymentId: context.headers.get("azureml-model-deployment") || "",
  };
}

/**
 * 获取处理时间
 * @param response - 响应对象
 * @returns 处理时间（毫秒）
 */
export function getProcessingTime(response: Response): number {
  const processingTime = response.headers.get("openai-processing-ms");
  return processingTime ? parseInt(processingTime, 10) : 0;
}

/**
 * 从引擎URL中提取引擎名称
 * @param ctx - 上下文对象
 * @param engineUrl - 引擎URL
 * @returns 引擎名称
 */
export function extractEngineName(ctx: any, engineUrl: string): string {
  return engineUrl.split("/").pop() || (logger.error(ctx, "Malformed engine URL: " + engineUrl), engineUrl);
}

/**
 * OpenAI获取器基类
 */
export abstract class OpenAIFetcher {
  // 基类方法将在子类中实现
}

/**
 * 对选项进行后处理
 * @param choices - 选项异步迭代器
 * @param allowEmptyChoices - 是否允许空选项
 * @returns 处理后的选项异步迭代器
 */
export function postProcessChoices(choices: AsyncIterable<any>, allowEmptyChoices?: boolean): AsyncIterable<any> {
  return allowEmptyChoices
    ? choices
    : asyncOps.asyncIterableFilter(choices, async (choice) => choice.completionText.trim().length > 0);
}

export class LiveOpenAIFetcher extends OpenAIFetcher {
  /**
   * 获取并流式传输完成结果
   * @param ctx - 上下文对象
   * @param params - 请求参数
   * @param resultInfo - 结果信息
   * @param onProgress - 进度回调函数
   * @param cancellation - 取消对象
   * @returns 完成结果
   */
  async fetchAndStreamCompletions(ctx: any, params: any, resultInfo: any, onProgress: Function, cancellation: any): Promise<any> {
   
    // const reporter = ctx.get(statusReporter.StatusReporter);
    const endpoint = "completions";
    const response = await this.fetchWithParameters(ctx, endpoint, params, cancellation);

    if (response === "not-sent") {
      return {
        type: "canceled",
        reason: "before fetch request",
      };
    }

    if (cancellation?.isCancellationRequested) {
      const responseBody = await (response as Response).body;
      try {
        responseBody?.cancel();
      } catch (error) {
        logger.error(ctx, `Error cancelling stream: ${error}`);
      }
      return {
        type: "canceled",
        reason: "after fetch request",
      };
    }

    if (response === undefined) {
      // const telemetryData = this.createTelemetryData(endpoint, ctx, params);
      // reporter.setWarning();
      // telemetryData.properties.error = "Response was undefined";
      // telemetry.telemetry(ctx, "request.shownWarning", telemetryData);
      return {
        type: "failed",
        reason: "fetch response was undefined",
      };
    }

    if ((response as Response).status !== 200) {
      const telemetryData = this.createTelemetryData(endpoint, ctx, params);
      return this.handleError(ctx, undefined, telemetryData, response as Response);
    }
    const resChoices =postProcessChoices(
      asyncOps.asyncIterableMap(sseProcessor.processSSE(ctx, response as Response, onProgress, resultInfo, cancellation), async (solution) =>
        sseProcessor.prepareSolutionForReturn(ctx, solution, resultInfo)
      ),
      params.allowEmptyChoices
    )
    
    
    return {
      type: "success",
      choices: resChoices,
      getProcessingTime: () => getProcessingTime(response as Response),
    };
  }


/**
   * 获取完成结果
   * @param ctx - 上下文对象
   * @param params - 请求参数
   * @param resultInfo - 结果信息
   * @param onProgress - 进度回调函数
   * @param cancellation - 取消对象
   * @returns 完成结果
   */
  async fetchAndCompletions(ctx: any, params: any, resultInfo: any, onProgress: Function, cancellation: any): Promise<any> {
   
    // const reporter = ctx.get(statusReporter.StatusReporter);
    const endpoint = "completions";
    const response = await this.fetchWithParameters(ctx, endpoint, params, cancellation);

    if (response === "not-sent") {
      return {
        type: "canceled",
        reason: "before fetch request",
      };
    }

    if (cancellation?.isCancellationRequested) {
      const responseBody = await (response as Response).body;
      try {
        responseBody?.cancel();
      } catch (error) {
        logger.error(ctx, `Error cancelling stream: ${error}`);
      }
      return {
        type: "canceled",
        reason: "after fetch request",
      };
    }

    if (response === undefined) {
      // const telemetryData = this.createTelemetryData(endpoint, ctx, params);
      // reporter.setWarning();
      // telemetryData.properties.error = "Response was undefined";
      // telemetry.telemetry(ctx, "request.shownWarning", telemetryData);
      return {
        type: "failed",
        reason: "fetch response was undefined",
      };
    }

    if ((response as Response).status !== 200) {
      const telemetryData = this.createTelemetryData(endpoint, ctx, params);
      return this.handleError(ctx, undefined, telemetryData, response as Response);
    }
  const body = await response.body();
  body.setEncoding("utf8");
  
  let resChoices=undefined;
  try{
    for await (const chunk of body) {
      resChoices = JSON.parse(chunk.toString());
    }
  }catch(error){
    logger.error(ctx,"fetchAndCompletions error:"+JSON.stringify(error));

  }
  logger.info(ctx,"fetchAndCompletions resChoices:"+JSON.stringify(resChoices));
    return {
      type: "success",
      choices: resChoices.data.choices[0],
      getProcessingTime: () => getProcessingTime(response as Response),
    };
  }


  /**
   * 创建遥测数据
   * @param endpoint - 端点
   * @param ctx - 上下文对象
   * @param params - 请求参数
   * @returns 遥测数据对象
   */
  private createTelemetryData(endpoint: string, ctx: any, params: any): any {
    return undefined;
    // telemetry.TelemetryData.createAndMarkAsIssued({
    //   endpoint: endpoint,
    //   engineName: extractEngineName(ctx, params.engineUrl),
    //   uiKind: params.uiKind,
    //   headerRequestId: params.ourRequestId,
    // });
  }
  /**
   * 使用参数进行获取
   * @param ctx - 上下文对象
   * @param endpoint - 端点
   * @param params - 请求参数
   * @param cancellation - 取消对象
   * @returns 响应对象或取消状态
   */
  private async fetchWithParameters(ctx: My_Context, endpoint: string, params: any, cancellation: any): Promise<Response | "not-sent"| any> {
    logger.info(ctx,"fetchWithParameters start:"+endpoint);
    var featureFlags;
    const stopWords = configManager.getLanguageConfig(ctx, configManager.ConfigKey.Stops);
    const disableLogProb = await ctx.get(featureManager.Features).disableLogProb();
    const requestOptions = {
      prefixCode: params.prompt.prefix,
      sufixCode: params.prompt.suffix,
      maxLines: 30,
      codeLanguage: params.languageId,
      max_tokens: configManager.getConfig(ctx, configManager.ConfigKey.SolutionLength),
      temperature: temperature.getTemperatureForSamples(ctx, params.count),
      top_p: configManager.getConfig(ctx, configManager.ConfigKey.TopP),
      n: params.count,
      stop: stopWords,
      userName:"songchenghong3",
      userToken:"sso.jd.com=BJ.77E2A98C8F4D766213488554FCF774B3.0920250512094151"
    } as any;
    if (!params.requestLogProbs && disableLogProb) {
      requestOptions.logprobs = 2;
    }
    const githubRepo = githubUtils.tryGetGitHubNWO(params.repoInfo);
    if (undefined !== githubRepo) {
      requestOptions.nwo = githubRepo;
    }
    if (
      [repetitionFilter.RepetitionFilterMode.PROXY, repetitionFilter.RepetitionFilterMode.BOTH].includes(
        await ctx.get(featureManager.Features).repetitionFilterMode()
      )
    ) {
      requestOptions.feature_flags = [
        ...(null !== (featureFlags = requestOptions.feature_flags) && undefined !== featureFlags ? featureFlags : []),
        "filter-repetitions",
      ];
    }
    if (params.postOptions) {
      Object.assign(requestOptions, params.postOptions);
    }
    return (null == cancellation ? undefined : cancellation.isCancellationRequested)
      ? "not-sent"
      : (logger.info(ctx, `[fetchCompletions] engine ${params.engineUrl}`),
        await (function (ctx, prompt, engineUrl, endpointName, requestId, options, authToken :'', uiKind, cancelToken) {
         // var optionValue;
        //  const reporter = ctx.get(statusReporter.StatusReporter);
          const fullUrl = engineUrl//utilModule.format("%s/%s", engineUrl, endpointName);
          // if (!authToken)
          //   return void logger.error(
          //     ctx,
          //     `Failed to send request to ${fullUrl} due to missing key`
          //   );
          // const telemetryData = telemetry.TelemetryData.createAndMarkAsIssued(
          //   {
          //     endpoint: endpointName,
          //     engineName: extractEngineName(ctx, engineUrl),
          //     uiKind: uiKind,
          //   },
          //   telemetry.telemetrizePromptLength(prompt)
          // );
          //for (const [key, value] of Object.entries(options))
            // if ("prompt" != key && "suffix" != key) {
            //   telemetryData.properties[`request.option.${key}`] =
            //     null !== (optionValue = JSON.stringify(value)) && undefined !== optionValue
            //       ? optionValue
            //       : "undefined";
            // }
          // telemetryData.properties.headerRequestId = requestId;
          // telemetry.telemetry(ctx, "request.sent", telemetryData);
          // const startTime = telemetry.now();
          const clientId = "copilot-ghost";
          logger.info(ctx, `[fetchCompletions] fullUrl ${fullUrl}`)
          logger.info(ctx, `[fetchCompletions] options`,JSON.stringify(options))
          
          return networking
            .postRequest(ctx, fullUrl, authToken, clientId, requestId, options, cancelToken)
            .then((response) => {
              const reqId = getRequestId(response, undefined);
             // telemetryData.extendWithRequestId(reqId);
             // const totalTime = telemetry.now() - startTime;
             // telemetryData.measurements.totalTimeMs = totalTime;
              // logger.info(ctx, `request.response: [${fullUrl}] took ${totalTime} ms`);
              // logger.debug(ctx, "request.response properties", telemetryData.properties);
              // logger.debug(
              //   ctx,
              //   "request.response measurements",
              //   telemetryData.measurements
              // );
              // logger.debug(ctx, `prompt: ${JSON.stringify(prompt)}`);
              // telemetry.telemetry(ctx, "request.response", telemetryData);
              const delayHeader = response.headers.get("x-copilot-delay");
              const delayMs = delayHeader ? parseInt(delayHeader, 10) : 0;
              ctx.get(ghostTextDebounce.GhostTextDebounceManager).extraDebounceMs = delayMs;
              return response;
            })
            .catch((error) => {
              logger.error(ctx, `Request Error: ${error}`);
              var errorCode;
              var errorErrno;
              var errorMessage;
              var errorType;
              if (networking.isAbortError(error)) throw error;
             // reporter.setWarning();
              // const warningData = telemetryData.extendedBy({
              //   error: "Network exception",
              // });
              // telemetry.telemetry(ctx, "request.shownWarning", warningData);
              // telemetryData.properties.code = String(
              //   null !== (errorCode = error.code) && undefined !== errorCode ? errorCode : ""
              // );
              // telemetryData.properties.errno = String(
              //   null !== (errorErrno = error.errno) && undefined !== errorErrno ? errorErrno : ""
              // );
              // telemetryData.properties.message = String(
              //   null !== (errorMessage = error.message) && undefined !== errorMessage ? errorMessage : ""
              // );
              // telemetryData.properties.type = String(
              //   null !== (errorType = error.type) && undefined !== errorType ? errorType : ""
              // );
              // const totalTime = telemetry.now() - startTime;
              // throw (
              //   ((telemetryData.measurements.totalTimeMs = totalTime),
              //   logger.debug(ctx, `request.response: [${fullUrl}] took ${totalTime} ms`),
              //   logger.debug(ctx, "request.error properties", telemetryData.properties),
              //   logger.debug(ctx, "request.error measurements", telemetryData.measurements),
              //   logger.error(ctx, `Request Error: ${error.message}`),
              //   //telemetry.telemetry(ctx, "request.error", telemetryData),
              //   error)
              // );
            })
            .finally(() => {
             // telemetry.logEnginePrompt(ctx, prompt, telemetryData);
            });
        })(
          ctx,
          params.prompt,
          params.engineUrl,
          endpoint,
          params.ourRequestId,
          requestOptions,
          '',
          params.uiKind,
          cancellation
        ));
  }

  /**
   * 处理错误
   * @param ctx - 上下文对象
   * @param reporter - 报告器对象
   * @param telemetryData - 遥测数据
   * @param response - 响应对象
   * @returns 错误处理结果
   */
  private async handleError(ctx: any, reporter: any, telemetryData: any, response: Response): Promise<any> {
    //reporter.setWarning();
    // telemetryData.properties.error = `Response status was ${response.status}`;
    // telemetryData.properties.status = String(response.status);
    // telemetry.telemetry(ctx, "request.shownWarning", telemetryData);
    if (401 === response.status || 403 === response.status)
      return (
        //ctx.get(tokenManager.CopilotTokenManager).resetCopilotToken(ctx, response.status),
        {
          type: "failed",
          reason: `token expired or invalid: ${response.status}`,
        }
      );
    if (499 === response.status) {
      logger.info(ctx, "Cancelled by server");
      return {
        type: "failed",
        reason: "canceled by server",
      };
    }
    const responseText = await response.text();
    return 466 === response.status
      ? (
        logger.info(ctx, responseText),
        {
          type: "failed",
          reason: `client not supported: ${responseText}`,
        })
      : (logger.error(ctx, "Unhandled status from server:", response.status, responseText),
        {
          type: "failed",
          reason: `unhandled status from server: ${response.status} ${responseText}`,
        });
  }
}
