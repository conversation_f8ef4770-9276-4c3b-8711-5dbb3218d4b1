/**
 * 862
 * 粒度实现模块
 * 
 * 该模块定义了用于处理时间粒度的类和函数。
 * 主要功能包括：
 * 1. 定义抽象的GranularityImplementation基类
 * 2. 实现DefaultGranularity类作为默认粒度实现
 * 3. 实现TimeBucketGranularity类，用于处理基于时间的粒度
 * 
 * 这个模块在Copilot扩展中用于管理和操作不同粒度的时间相关功能，
 * 支持灵活的时间周期和桶划分策略。
 */

// 声明模块为ES模块
export {};

// 定义GranularityImplementation类
export class GranularityImplementation {
  protected prefix: string;

  constructor(prefix: string) {
    this.prefix = prefix;
  }

  // 获取当前值和即将到来的值
  getCurrentAndUpComingValues(date: Date): [string, string[]] {
    return [this.getValue(date), this.getUpcomingValues(date)];
  }

  // 抽象方法，由子类实现
  getValue(date: Date): string {
    throw new Error("Method not implemented.");
  }

  // 抽象方法，由子类实现
  getUpcomingValues(date: Date): string[] {
    throw new Error("Method not implemented.");
  }
}

// 定义默认粒度实现类
class DefaultGranularity extends GranularityImplementation {
  getValue(date: Date): string {
    return this.prefix;
  }

  getUpcomingValues(date: Date): string[] {
    return [];
  }
}

// 导出默认粒度工厂函数
export const DEFAULT_GRANULARITY = (prefix: string): GranularityImplementation => new DefaultGranularity(prefix);

// 定义时间桶粒度类
export class TimeBucketGranularity extends GranularityImplementation {
  private fetchBeforeFactor: number;
  private anchor: number;
  private timePeriodLengthMs?: number;
  private numByCallBuckets?: number;

  constructor(prefix: string, fetchBeforeFactor: number = 0.5, anchor: number = new Date().setUTCHours(0, 0, 0, 0)) {
    super(prefix);
    this.fetchBeforeFactor = fetchBeforeFactor;
    this.anchor = anchor;
  }

  // 设置时间周期
  setTimePeriod(period: number): void {
    this.timePeriodLengthMs = isNaN(period) ? undefined : period;
  }

  // 设置通话桶数量
  setByCallBuckets(buckets: number): void {
    this.numByCallBuckets = isNaN(buckets) ? undefined : buckets;
  }

  // 获取值
  getValue(date: Date): string {
    return (
      this.prefix +
      this.getTimePeriodBucketString(date) +
      (this.numByCallBuckets ? this.timeHash(date).toString() : "")
    );
  }

  // 获取时间周期桶字符串
  private getTimePeriodBucketString(date: Date): string {
    return this.timePeriodLengthMs ? this.dateToTimePartString(date) : "";
  }

  // 获取即将到来的值
  getUpcomingValues(date: Date): string[] {
    const result: string[] = [];
    const upcomingTimePeriods = this.getUpcomingTimePeriodBucketStrings(date);
    const upcomingByCallBuckets = this.getUpcomingByCallBucketStrings();
    
    for (const timePeriod of upcomingTimePeriods) {
      for (const byCallBucket of upcomingByCallBuckets) {
        result.push(this.prefix + timePeriod + byCallBucket);
      }
    }
    
    return result;
  }

  // 获取即将到来的时间周期桶字符串
  private getUpcomingTimePeriodBucketStrings(date: Date): string[] {
    if (this.timePeriodLengthMs === undefined) return [""];
    
    if ((date.getTime() - this.anchor) % this.timePeriodLengthMs < this.fetchBeforeFactor * this.timePeriodLengthMs) {
      return [this.getTimePeriodBucketString(date)];
    } else {
      const nextDate = new Date(date.getTime() + this.timePeriodLengthMs);
      return [
        this.getTimePeriodBucketString(date),
        this.getTimePeriodBucketString(nextDate),
      ];
    }
  }

  // 获取即将到来的通话桶字符串
  private getUpcomingByCallBucketStrings(): string[] {
    return this.numByCallBuckets === undefined
      ? [""]
      : Array.from(Array(this.numByCallBuckets).keys()).map((e) => e.toString());
  }

  // 计算时间哈希
  private timeHash(date: Date): number {
    if (this.numByCallBuckets === undefined) return 0;
    return ((date.getTime() % this.numByCallBuckets) * 7883) % this.numByCallBuckets;
  }

  // 将日期转换为时间部分字符串
  private dateToTimePartString(date: Date): string {
    if (this.timePeriodLengthMs === undefined) return "";
    return Math.floor((date.getTime() - this.anchor) / this.timePeriodLengthMs).toString();
  }
}
