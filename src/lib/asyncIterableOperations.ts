/**
 * 6932 asyncIterableOperations
 * 此模块提供了用于处理异步可迭代对象的实用函数。
 * 包括用于映射、过滤和将数组转换为异步可迭代对象的函数。
 */

// 定义泛型类型 T 和 U
type T = any;
type U = any;

/**
 * 使用提供的函数对输入的异步可迭代对象中的每个值进行映射。
 * @param inputIterable - 输入的异步可迭代对象。
 * @param mapFunction - 映射函数。
 * @returns 一个包含映射后值的新异步可迭代对象。
 */
export async function* asyncIterableMap<T, U>(
  inputIterable: AsyncIterable<T>,
  mapFunction: (item: T) => Promise<U>
): AsyncIterable<U> {
  for await (const item of inputIterable) yield mapFunction(item);
}

/**
 * 使用提供的谓词函数对输入的异步可迭代对象中的值进行过滤。
 * @param inputIterable - 输入的异步可迭代对象。
 * @param filterFunction - 过滤谓词函数。
 * @returns 一个包含过滤后值的新异步可迭代对象。
 */
export async function* asyncIterableFilter<T>(
  inputIterable: AsyncIterable<T>,
  filterFunction: (item: T) => Promise<boolean>
): AsyncIterable<T> {
  for await (const item of inputIterable)
    if (await filterFunction(item)) {
      yield item;
    }
}

/**
 * 使用提供的函数对输入的异步可迭代对象中的值进行映射和过滤。
 * @param inputIterable - 输入的异步可迭代对象。
 * @param mapFilterFunction - 用于映射和过滤值的函数。
 * @returns 一个包含映射和过滤后值的新异步可迭代对象。
 */
export async function* asyncIterableMapFilter<T, U>(
  inputIterable: AsyncIterable<T>,
  mapFilterFunction: (item: T) => Promise<U | undefined>
): AsyncIterable<U> {
  for await (const item of inputIterable) {
    const result = await mapFilterFunction(item);
    if (undefined !== result) {
      yield result;
    }
  }
}

/**
 * 将数组转换为异步可迭代对象。
 * @param inputArray - 输入数组。
 * @returns 一个从输入数组中产生值的异步可迭代对象。
 */
export async function* asyncIterableFromArray<T>(
  inputArray: T[]
): AsyncIterable<T> {
  for (const item of inputArray) yield item;
}
