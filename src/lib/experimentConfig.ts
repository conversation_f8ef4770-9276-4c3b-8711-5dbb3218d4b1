/**
 * 219
 * 文件名: experimentConfig.ts
 * 
 * 主要功能：
 * 1. 定义实验处理变量的枚举（ExpTreatmentVariables）
 * 2. 实现实验配置类（ExpConfig）
 * 
 * 该模块主要用于管理和配置 Copilot 相关的实验参数。
 * 它提供了一系列实验变量的枚举定义，以及一个用于创建和管理实验配置的类。
 * 这个模块可能被用于 A/B 测试或其他类型的实验，以评估不同配置对 Copilot 功能的影响。
 */

// 导入所需模块
//const r = require(6333);
// 定义实验处理变量的枚举
export enum ExpTreatmentVariables {
  AA = "copilotaa",
  CustomEngine = "copilotcustomengine",
  Fetcher = "copilotfetcher",
  OverrideBlockMode = "copilotoverrideblockmode",
  OverrideNumGhostCompletions = "copilotoverridednumghostcompletions",
  SuffixPercent = "CopilotSuffixPercent",
  BeforeRequestWaitMs = "copilotlms",
  NeighboringTabsOption = "copilotneighboringtabs",
  DebounceMs = "copilotdebouncems",
  DebouncePredict = "copilotdebouncepredict",
  ContextualFilterEnable = "copilotcontextualfilterenable",
  ContextualFilterAcceptThreshold = "copilotcontextualfilteracceptthreshold",
  disableLogProb = "copilotLogProb",
  RepetitionFilterMode = "copilotrepetitionfiltermode",
  GranularityTimePeriodSizeInH = "copilottimeperiodsizeinh",
  GranularityByCallBuckets = "copilotbycallbuckets",
  SuffixStartMode = "copilotsuffixstartmode",
  SuffixMatchThreshold = "copilotsuffixmatchthreshold",
  FimSuffixLengthThreshold = "copilotfimsuffixlenthreshold",
  MultiLogitBias = "copilotlbeot"
}

// 定义实验配置类
export class ExpConfig {
  variables: Record<string, any>;
  assignmentContext: string;
  features: string;

  constructor(variables: Record<string, any>, assignmentContext: string, features: string) {
    this.variables = variables;
    this.assignmentContext = assignmentContext;
    this.features = features;
  }

  // 创建回退配置
  static createFallbackConfig(e: any, t: string): ExpConfig {
    // r.telemetryExpProblem(e, {
    //   reason: t,
    // });
    return this.createEmptyConfig();
  }

  // 创建空配置
  static createEmptyConfig(): ExpConfig {
    return new ExpConfig({}, "", "");
  }
}
