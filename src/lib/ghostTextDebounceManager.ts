/**
 * 文件名: ghostTextDebounceManager.ts
 * 
 * 主要功能：
 * 这个文件实现了 GhostText 的防抖管理。它包含了 GhostTextDebounceManager 类和 getDebounceLimit 函数，
 * 用于控制 GhostText 功能的触发频率，优化性能和用户体验。
 * 
 * 主要组件：
 * 1. GhostTextDebounceManager: 管理 GhostText 的防抖延迟。
 * 2. getDebounceLimit: 根据上下文和 GhostText 结果计算适当的防抖限制。
 */

import { Features } from './featureManager';
import { My_Context } from './contextType';


// 定义 GhostTextResult 接口
interface GhostTextResult {
  measurements: {
    contextualFilterScore?: number;
  };
}

/**
 * GhostTextDebounceManager 类
 * 用于管理 GhostText 的防抖延迟
 */
export class GhostTextDebounceManager {
  private forceDelayMs: number;
  public extraDebounceMs: number;

  /**
   * 构造函数
   * @param forceDelayMs - 强制延迟的毫秒数
   */
  constructor(forceDelayMs: number) {
    this.forceDelayMs = forceDelayMs;
    this.extraDebounceMs = 0; // 额外的防抖延迟毫秒数
  }
}

/**
 * 获取防抖限制
 * @param context - 上下文对象
 * @param ghostTextResult - GhostText 结果对象
 * @returns 防抖限制的毫秒数
 */
export async function getDebounceLimit(context: My_Context, ghostTextResult: GhostTextResult): Promise<number> {
  let debounceLimit: number;

  if (
    (await context.get(Features).debouncePredict()) &&
    ghostTextResult.measurements.contextualFilterScore !== undefined
  ) {
    // 如果启用了预测防抖并且有上下文过滤分数，则计算动态防抖限制
    const contextualFilterScore = ghostTextResult.measurements.contextualFilterScore;
    const threshold = 0.275;
    const exponent = 6;
    debounceLimit = 25 + 250 / (1 + Math.pow(contextualFilterScore / threshold, exponent));
  } else {
    // 否则使用默认的防抖毫秒数
    debounceLimit = await context.get(Features).debounceMs();
  }

  // 返回计算得到的防抖限制，如果小于等于0则使用75ms，并加上额外的防抖延迟
  return (debounceLimit > 0 ? debounceLimit : 75) + context.get(GhostTextDebounceManager).extraDebounceMs;
}
