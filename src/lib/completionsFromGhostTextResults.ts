/**
 * id:2218
 * @fileoverview 此模块提供将幽灵文本结果转换为补全项的功能。
 * 它是 GitHub Copilot 扩展的一部分，在处理和格式化代码补全建议中起着关键作用。
 *
 * @module completionsFromGhostTextResults
 */

import { v4 as uuidv4 } from 'uuid';
import { LocationFactory } from './locationFactory';
import { ResultType } from './ghostTextManager';
import { normalizeIndentCharacter } from './indentationNormalizer';

// 定义 GhostTextResult 接口
interface GhostTextResult {
  completion: {
    completionText: string;
    displayText: string;
    displayNeedsWsOffset?: boolean;
    completionIndex: number;
  };
  isMiddleOfTheLine: boolean;
  coversSuffix: boolean;
  telemetry: any;
}

// 定义 CompletionItem 接口
interface CompletionItem {
  uuid: string;
  text: string;
  range: any;
  file: any;
  index: number;
  telemetry: any;
  displayText: string;
  position: any;
  offset: number;
  resultType: ResultType;
}

// 定义 ContextManager 接口
interface ContextManager {
  get(key: any): any;
}

/**
 * 将幽灵文本结果转换为补全项。
 * @param contextManager - 上下文管理器对象。
 * @param ghostTextResults - 幽灵文本结果数组。
 * @param resultType - 结果类型。
 * @param document - 文档对象。
 * @param position - 位置对象。
 * @param indentationRules - 缩进规则对象。
 * @param typedIndex - 已输入补全的索引。
 * @returns 补全项数组。
 */
export function completionsFromGhostTextResults(
  contextManager: ContextManager,
  ghostTextResults: GhostTextResult[],
  resultType: ResultType,
  document: any,
  position: any,
  indentationRules: any,
  typedIndex?: number
): CompletionItem[] {
  const locationFactory: LocationFactory = contextManager.get(LocationFactory);
  const currentLine = document.lineAt(position);
  
  /**
   * 将每个幽灵文本结果映射为一个补全项。
   * @param ghostText - 幽灵文本对象。
   * @returns 补全项。
   */
  let completions: CompletionItem[] = ghostTextResults.map((ghostText: GhostTextResult) => {
    let range: any;
    let text: string = "";

    // 规范化补全文本的缩进
    if (indentationRules) {
      ghostText.completion = normalizeIndentCharacter(
        indentationRules,
        ghostText.completion,
        currentLine.isEmptyOrWhitespace
      );
    }

    // 根据当前行和幽灵文本确定补全项的范围和文本
    if (ghostText.completion.displayNeedsWsOffset && currentLine.isEmptyOrWhitespace) {
      range = locationFactory.range(locationFactory.position(position.line, 0), position);
      text = ghostText.completion.completionText;
    } else if (
      currentLine.isEmptyOrWhitespace &&
      ghostText.completion.completionText.startsWith(currentLine.text)
    ) {
      range = locationFactory.range(locationFactory.position(position.line, 0), position);
      text = ghostText.completion.completionText;
    } else {
      const wordRange = document.getWordRangeAtPosition(position);
      if (ghostText.isMiddleOfTheLine) {
        const lineRange = document.lineAt(position);
        const rangeToPosition = locationFactory.range(locationFactory.position(position.line, 0), position);
        const textToPosition = document.getText(rangeToPosition);
        range = ghostText.coversSuffix ? lineRange.range : rangeToPosition;
        text = textToPosition + ghostText.completion.displayText;
      } else if (wordRange) {
        const wordText = document.getText(wordRange);
        range = locationFactory.range(wordRange.start, position);
        text = wordText + ghostText.completion.completionText;
      } else {
        const rangeToPosition = locationFactory.range(locationFactory.position(position.line, 0), position);
        range = rangeToPosition;
        text = document.getText(rangeToPosition) + ghostText.completion.displayText;
      }
    }

    // 创建并返回一个补全项对象
    return {
      uuid: uuidv4(),
      text: text,
      range: range,
      file: document.uri,
      index: ghostText.completion.completionIndex,
      telemetry: ghostText.telemetry,
      displayText: ghostText.completion.displayText,
      position: position,
      offset: document.offsetAt(position),
      resultType: resultType,
    };
  });

  // 如果存在已输入的补全，则重新排序补全项
  if (resultType === ResultType.TypingAsSuggested && typedIndex !== undefined) {
    const typedCompletion = completions.find((completion) => completion.index === typedIndex);
    if (typedCompletion) {
      const otherCompletions = completions.filter((completion) => completion.index !== typedIndex);
      completions = [typedCompletion, ...otherCompletions];
    }
  }

  return completions;
}
