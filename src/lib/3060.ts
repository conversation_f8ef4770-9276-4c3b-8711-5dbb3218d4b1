// 告诉 TypeScript 这是一个模块
export {};

 // 定义常量并导出
// Copilot 相关命令字符串
export const CMDOpenPanel: string = "github.copilot.generate"; // 打开 Copilot 面板
export const CMDOpenPanelForRange: string = "github.copilot.openPanelForRange"; // 针对指定范围打开面板
export const CMDAcceptPanelSolution: string = "github.copilot.acceptPanelSolution"; // 接受面板中的解决方案
export const CMDToggleCopilot: string = "github.copilot.toggleCopilot"; // 切换 Copilot 启用状态
export const CMDShowActivationErrors: string = "github.copilot.showActivationErrors"; // 显示激活错误
export const CMDSendFeedback: string = "github.copilot.sendFeedback"; // 发送反馈
export const CopilotLabelPrefix: string = "$(copilot-logo) "; // Copilot 标签前缀
