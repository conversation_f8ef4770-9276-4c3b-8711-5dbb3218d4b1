/**
 * 9189
 * featureManager.ts
 * 
 * 该模块管理GitHub Copilot的特性和实验配置。
 * 它处理静态和动态过滤器，实验分配，以及各种Copilot功能的配置。
 */

import {SuffixStartMode, NeighboringTabsOption } from './promptManager';
import { Clock } from './clock';
import { LRUCache } from './cacheUtils';
import {getConfig,ConfigKey} from './configurationManager';
import * as contextualFilterConfig from './contextualFilterConstants';
import { RepetitionFilterMode} from './repetitionDetector';
import {ExpTreatmentVariables,ExpConfig} from './experimentConfig';
import { ExpConfigMaker ,} from './experimentConfigFactory';
import { FilterSettings, Filter } from './filterSettings';
import { GranularityDirectory } from './granularityDirectoryManager';
import { MyContext } from './myContext';

/**
 * 实验分配管理类
 */
class ExperimentAssignments {
  private ctx: any;
  private cache: LRUCache<Task<any>>;

  /**
   * 构造函数，初始化实验分配管理器
   * @param context - 上下文对象
   */
  constructor(context: any) {
    this.ctx = context;
    this.cache = new LRUCache<Task<any>>(200);
  }

  /**
   * 获取实验配置，如果缓存中没有则创建新的任务
   * @param filterSettings - 过滤器设置
   * @returns 返回实验配置的Promise
   */
  async fetchExpConfig(filterSettings: FilterSettings): Promise<any> {
    let task = this.cache.get(filterSettings.stringify());
    if (!task) {
      task = new Task(
        () =>
          this.ctx
            .get(ExpConfigMaker)
            .fetchExperiments(this.ctx, filterSettings.toHeaders()),
        36e5 // 1 hour in milliseconds
      );
      this.cache.put(filterSettings.stringify(), task);
    }
    return task.run();
  }

  /**
   * 获取缓存的实验配置
   * @param filterSettings - 过滤器设置
   * @returns 返回缓存的实验配置或undefined
   */
  getCachedExpConfig(filterSettings: FilterSettings): any | undefined {
    const task = this.cache.get(filterSettings.stringify());
    return task ? task.value() : undefined;
  }
}

/**
 * 任务类，用于管理异步操作
 */
export class Task<T> {
  private producer: () => Promise<T>;
  private expirationMs: number;
  private promise?: Promise<T>;
  private result?: T;

  /**
   * 构造函数，初始化任务
   * @param producer - 生成结果的函数
   * @param expirationMs - 过期时间（毫秒）
   */
  constructor(producer: () => Promise<T>, expirationMs: number = Infinity) {
    this.producer = producer;
    this.expirationMs = expirationMs;
  }

  /**
   * 运行任务
   * @returns 返回任务结果的Promise
   */
  async run(): Promise<T> {
    if (this.promise === undefined) {
      this.promise = this.producer();
      this.storeResult(this.promise).then(() => {
        if (this.expirationMs < Infinity && this.promise !== undefined) {
          setTimeout(() => (this.promise = undefined), this.expirationMs);
        }
      });
    }
    return this.promise;
  }

  /**
   * 存储任务结果
   * @param promise - 任务结果的Promise
   */
  private async storeResult(promise: Promise<T>): Promise<void> {
    try {
      this.result = await promise;
    } finally {
      if (this.result === undefined) {
        this.promise = undefined;
      }
    }
  }

  /**
   * 获取任务结果
   * @returns 返回任务结果
   */
  value(): T | undefined {
    return this.result;
  }
}

/**
 * 特性管理类
 */
export class Features {
  // 静态属性
  static upcomingDynamicFilterCheckDelayMs: number = 20;
  static upcomingTimeBucketMinutes: number = 5 + Math.floor(11 * Math.random());

  private ctx: MyContext;
  private staticFilters: Record<string, any>;
  private dynamicFilters: Record<string, () => any>;
  private upcomingDynamicFilters: Record<string, () => any>;
  private assignments: ExperimentAssignments;
  private granularityDirectory: GranularityDirectory;

  /**
   * 构造函数，初始化特性管理器
   * @param context - 上下文对象
   */
  constructor(context: MyContext) {
    this.ctx = context;
    this.staticFilters = {};
    this.dynamicFilters = {};
    this.upcomingDynamicFilters = {};
    this.assignments = new ExperimentAssignments(this.ctx);
    this.granularityDirectory = new GranularityDirectory(
      "unspecified",
      context.get(Clock)
    );
  }

  /**
   * 设置前缀并创建新的粒度目录
   * @param prefix - 前缀
   */
  setPrefix(prefix: string): void {
    this.granularityDirectory = new GranularityDirectory(
      prefix,
      this.ctx.get(Clock)
    );
  }

  /**
   * 注册静态过滤器
   * @param filters - 静态过滤器对象
   */
  registerStaticFilters(filters: Record<string, any>): void {
    Object.assign(this.staticFilters, filters);
  }

  /**
   * 注册动态过滤器
   * @param key - 过滤器键
   * @param valueFunction - 值函数
   */
  registerDynamicFilter(key: string, valueFunction: () => any): void {
    this.dynamicFilters[key] = valueFunction;
  }

  /**
   * 获取动态过滤器值
   * @returns 动态过滤器值对象
   */
  getDynamicFilterValues(): Record<string, any> {
    const values: Record<string, any> = {};
    for (const [key, valueFunction] of Object.entries(this.dynamicFilters)) {
      values[key] = valueFunction();
    }
    return values;
  }

  /**
   * 注册即将到来的动态过滤器
   * @param key - 过滤器键
   * @param valueFunction - 值函数
   */
  registerUpcomingDynamicFilter(key: string, valueFunction: () => any): void {
    this.upcomingDynamicFilters[key] = valueFunction;
  }

  /**
   * 获取分配
   * @param variable - 变量名
   * @param additionalFilters - 附加过滤器
   * @param telemetryData - 遥测数据
   * @returns 分配结果
   */
  async getAssignment(
    variable: string, 
    additionalFilters: Record<string, any> = {}, 
    telemetryData?: any
  ): Promise<any> {
    const initialFilterSettings = this.makeFilterSettings(additionalFilters);
    const extendedFilters = this.granularityDirectory.extendFilters(initialFilterSettings);
    const initialConfig = await this.getExpConfig(extendedFilters.newFilterSettings);

    this.granularityDirectory.update(
      initialFilterSettings,
      +(initialConfig.variables[ExpTreatmentVariables.GranularityByCallBuckets] ?? NaN),
      +(initialConfig.variables[ExpTreatmentVariables.GranularityTimePeriodSizeInH] ?? NaN)
    );

    const finalExtendedFilters = this.granularityDirectory.extendFilters(initialFilterSettings);
    const finalFilterSettings = finalExtendedFilters.newFilterSettings;
    const finalConfig = await this.getExpConfig(finalFilterSettings);

    let prefetchPromise = new Promise<void>((resolve) =>
      setTimeout(resolve, Features.upcomingDynamicFilterCheckDelayMs)
    );

    for (const filterSettings of finalExtendedFilters.otherFilterSettingsToPrefetch) {
      prefetchPromise = prefetchPromise.then(async () => {
        await new Promise<void>((resolve) =>
          setTimeout(resolve, Features.upcomingDynamicFilterCheckDelayMs)
        );
        this.getExpConfig(filterSettings);
      });
    }

    this.prepareForUpcomingFilters(finalFilterSettings);

    if (telemetryData) {
      telemetryData.filtersAndExp = {
        exp: finalConfig,
        filters: finalFilterSettings,
      };
    }

    return finalConfig.variables[variable];
  }

  /**
   * 创建过滤器设置
   * @param additionalFilters - 附加过滤器
   * @returns 过滤器设置对象
   */
  makeFilterSettings(additionalFilters: Record<string, any>): FilterSettings {
    return new FilterSettings({
      ...this.staticFilters,
      ...this.getDynamicFilterValues(),
      ...additionalFilters,
    });
  }

  /**
   * 获取实验配置
   * @param filterSettings - 过滤器设置
   * @returns 实验配置
   */
  async getExpConfig(filterSettings: FilterSettings): Promise<any> {
    try {
      return this.assignments.fetchExpConfig(filterSettings);
    } catch (error) {
      return ExpConfig.createFallbackConfig(
        this.ctx,
        `Error fetching ExP config: ${error}`
      );
    }
  }

  /**
   * 准备即将到来的过滤器
   * @param currentFilterSettings - 当前过滤器设置
   */
  async prepareForUpcomingFilters(currentFilterSettings: FilterSettings): Promise<void> {
    const currentMinute = new Date().getMinutes();
    if (currentMinute >= 60 - Features.upcomingTimeBucketMinutes) {
      for (const [key, valueFunction] of Object.entries(this.upcomingDynamicFilters)) {
        await new Promise<void>((resolve) =>
          setTimeout(resolve, Features.upcomingDynamicFilterCheckDelayMs)
        );
       // this.getExpConfig(currentFilterSettings.withChange(key, valueFunction()));
      }
    }
  }

  /**
   * 将特性配置转换为字符串
   * @returns JSON字符串
   */
  stringify(): string {
    const cachedConfig = this.assignments.getCachedExpConfig(new FilterSettings({}));
    return JSON.stringify(cachedConfig?.variables ?? {});
  }

  /**
   * 获取自定义引擎配置
   * @param repository - 仓库
   * @param fileType - 文件类型
   * @param isDogfood - 是否为内部测试版
   * @param userKind - 用户类型
   * @param telemetryData - 遥测数据
   * @returns 自定义引擎配置
   */
  async customEngine(
    repository: string, 
    fileType: string, 
    isDogfood: boolean, 
    userKind: string, 
    telemetryData?: any
  ): Promise<string> {
    const filters = {
      [Filter.CopilotRepository]: repository,
      [Filter.CopilotFileType]: fileType,
      [Filter.CopilotDogfood]: isDogfood,
      [Filter.CopilotUserKind]: userKind,
    };
    return await this.getAssignment(
      ExpTreatmentVariables.CustomEngine,
      filters,
      telemetryData
    ) ?? "";
  }

  /**
   * 获取请求前等待时间
   * @param repository - 仓库
   * @param fileType - 文件类型
   * @param telemetryData - 遥测数据
   * @returns 等待时间（毫秒）
   */
  async beforeRequestWaitMs(
    repository: string, 
    fileType: string, 
    telemetryData?: any
  ): Promise<number> {
    const filters = {
      [Filter.CopilotRepository]: repository,
      [Filter.CopilotFileType]: fileType,
    };
    return await this.getAssignment(
      ExpTreatmentVariables.BeforeRequestWaitMs,
      filters,
      telemetryData
    ) ?? 0;
  }

  /**
   * 获取多重logit偏差配置
   * @param repository - 仓库
   * @param fileType - 文件类型
   * @param telemetryData - 遥测数据
   * @returns 是否启用多重logit偏差
   */
  async multiLogitBias(
    repository: string, 
    fileType: string, 
    telemetryData?: any
  ): Promise<boolean> {
    const filters = {
      [Filter.CopilotRepository]: repository,
      [Filter.CopilotFileType]: fileType,
    };
    return await this.getAssignment(
      ExpTreatmentVariables.MultiLogitBias,
      filters,
      telemetryData
    ) ?? false;
  }

  /**
   * 获取防抖时间
   * @returns 防抖时间（毫秒）
   */
  async debounceMs(): Promise<number> {
    return await this.getAssignment(ExpTreatmentVariables.DebounceMs) ?? 0;
  }

  /**
   * 获取是否启用预测防抖
   * @returns 是否启用预测防抖
   */
  async debouncePredict(): Promise<boolean> {
    return await this.getAssignment(ExpTreatmentVariables.DebouncePredict) ?? false;
  }

  /**
   * 获取是否启用上下文过滤器
   * @returns 是否启用上下文过滤器
   */
  async contextualFilterEnable(): Promise<boolean> {
    return await this.getAssignment(ExpTreatmentVariables.ContextualFilterEnable) ?? true;
  }

  /**
   * 获取上下文过滤器接受阈值
   * @returns 上下文过滤器接受阈值
   */
  async contextualFilterAcceptThreshold(): Promise<number> {
    return await this.getAssignment(ExpTreatmentVariables.ContextualFilterAcceptThreshold) ?? 
      contextualFilterConfig.contextualFilterAcceptThreshold;
  }

  /**
   * 获取是否禁用日志概率
   * @returns 是否禁用日志概率
   */
  async disableLogProb(): Promise<boolean> {
    return await this.getAssignment(ExpTreatmentVariables.disableLogProb) ?? false;
  }

  /**
   * 获取覆盖块模式
   * @returns 覆盖块模式
   */
  async overrideBlockMode(): Promise<any> {
    return await this.getAssignment(ExpTreatmentVariables.OverrideBlockMode);
  }

  /**
   * 获取覆盖幽灵完成数量
   * @returns 覆盖幽灵完成数量
   */
  async overrideNumGhostCompletions(): Promise<any> {
    return await this.getAssignment(ExpTreatmentVariables.OverrideNumGhostCompletions);
  }

  /**
   * 获取后缀百分比
   * @param repository - 仓库
   * @param fileType - 文件类型
   * @returns 后缀百分比
   */
  async suffixPercent(repository: string, fileType: string): Promise<number> {
    const filters = {
      [Filter.CopilotRepository]: repository,
      [Filter.CopilotFileType]: fileType,
    };
    return getConfig(this.ctx, ConfigKey.DebugOverrideEngine)
      ? 0
      : await this.getAssignment(ExpTreatmentVariables.SuffixPercent, filters) ?? 0;
  }

  /**
   * 获取后缀匹配阈值
   * @param repository - 仓库
   * @param fileType - 文件类型
   * @returns 后缀匹配阈值
   */
  async suffixMatchThreshold(repository: string, fileType: string): Promise<number> {
    const filters = {
      [Filter.CopilotRepository]: repository,
      [Filter.CopilotFileType]: fileType,
    };
    return await this.getAssignment(ExpTreatmentVariables.SuffixMatchThreshold, filters) ?? 0;
  }

  /**
   * 获取FIM后缀长度阈值
   * @param repository - 仓库
   * @param fileType - 文件类型
   * @returns FIM后缀长度阈值
   */
  async fimSuffixLengthThreshold(repository: string, fileType: string): Promise<number> {
    const filters = {
      [Filter.CopilotRepository]: repository,
      [Filter.CopilotFileType]: fileType,
    };
    return await this.getAssignment(ExpTreatmentVariables.FimSuffixLengthThreshold, filters) ?? 0;
  }

  /**
   * 获取后缀开始模式
   * @param repository - 仓库
   * @param fileType - 文件类型
   * @returns 后缀开始模式
   */
  async suffixStartMode(repository: string, fileType: string): Promise<SuffixStartMode> {
    const filters = {
      [Filter.CopilotRepository]: repository,
      [Filter.CopilotFileType]: fileType,
    };
    const mode = await this.getAssignment(ExpTreatmentVariables.SuffixStartMode, filters);
    switch (mode) {
      case "cursortrimstart":
        return SuffixStartMode.CursorTrimStart;
      case "siblingblock":
        return SuffixStartMode.SiblingBlock;
      case "siblingblocktrimstart":
        return SuffixStartMode.SiblingBlockTrimStart;
      default:
        return SuffixStartMode.Cursor;
    }
  }

  /**
   * 获取相邻标签选项
   * @param repository - 仓库
   * @param fileType - 文件类型
   * @returns 相邻标签选项
   */
  async neighboringTabsOption(repository: string, fileType: string): Promise<NeighboringTabsOption> {
    const filters = {
      [Filter.CopilotRepository]: repository,
      [Filter.CopilotFileType]: fileType,
    };
    const option = await this.getAssignment(ExpTreatmentVariables.NeighboringTabsOption, filters);
    switch (option) {
      case "none":
        return NeighboringTabsOption.None;
      case "conservative":
        return NeighboringTabsOption.Conservative;
      case "medium":
        return NeighboringTabsOption.Medium;
      case "eagerbutlittle":
        return NeighboringTabsOption.EagerButLittle;
      default:
        return NeighboringTabsOption.Eager;
    }
  }

  /**
   * 获取重复过滤模式
   * @returns 重复过滤模式
   */
  async repetitionFilterMode(): Promise<typeof RepetitionFilterMode.CLIENT | typeof RepetitionFilterMode.PROXY | typeof RepetitionFilterMode.BOTH> {
    const mode = await this.getAssignment(ExpTreatmentVariables.RepetitionFilterMode);
    switch (mode) {
      case "proxy":
        return RepetitionFilterMode.PROXY;
      case "both":
        return RepetitionFilterMode.BOTH;
      default:
        return RepetitionFilterMode.CLIENT;
    }
  }

  /**
   * 将实验和过滤器添加到遥测数据
   * @param telemetryData - 遥测数据
   */
  async addExpAndFilterToTelemetry(telemetryData: any): Promise<void> {
    const filterSettings = this.makeFilterSettings({});
    telemetryData.filtersAndExp = {
      filters: filterSettings,
      exp: await this.getExpConfig(filterSettings),
    };
  }
}
