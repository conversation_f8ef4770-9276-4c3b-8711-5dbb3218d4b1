/*
9425
 * 文件描述：VSCode配置管理器
 * 
 * 本文件实现了GitHub Copilot扩展的VSCode特定配置管理。
 * 主要内容包括：
 * 1. VSCodeConfigProvider类：扩展了ConfigProvider，用于管理VSCode的配置
 * 2. 配置相关的辅助函数：如stringifyValue、getExtension等
 * 3. VSCodeEditorInfo类：提供编辑器和插件信息
 * 
 * 该文件负责处理配置的读取、更新、导出，以及与VSCode环境的交互。
 * 它是Copilot扩展在VSCode中正确运行和配置的关键组件。
 */

import * as vscode from 'vscode';
import { ConfigProvider, ConfigKey, VscInfo, EditorAndPluginInfo, getConfigDefaultForObjectKey, getVersion, MyContext } from './configurationManager';
import { CopilotConfigPrefix } from './copilotConstants';
import { isRunningInTest } from './runtimeModeManager';
import { default as contributes } from './copilotPackageConfig';

// 将值转换为字符串
function stringifyValue(value: any): string {
  return typeof value === 'string' ? value : JSON.stringify(value);
}

// VSCode 配置提供者类
class VSCodeConfigProvider extends ConfigProvider {
  private config: vscode.WorkspaceConfiguration;

  constructor() {
    super();
    this.config = vscode.workspace.getConfiguration(CopilotConfigPrefix);

    // 监听配置变化
    vscode.workspace.onDidChangeConfiguration((event) => {
      if (event.affectsConfiguration(CopilotConfigPrefix)) {
        this.config = vscode.workspace.getConfiguration(CopilotConfigPrefix);
      }
    });
  }

  // 从对象中获取配置键值
  private getConfigKeyFromObject(obj: string, key: string): any {
    const value = this.config[obj][key];
    return value === undefined ? getConfigDefaultForObjectKey(obj, key) : value;
  }

  // 获取配置
  getConfig(key: string | [string, string]): any {
    if (Array.isArray(key)) return this.getConfigKeyFromObject(key[0], key[1]);
    
    const value = this.config.get(key);
    if (value === undefined) {
      throw new Error(`Missing config default value: ${CopilotConfigPrefix}.${key}`);
    }
    return value;
  }

  // 检查默认设置是否被覆盖
  isDefaultSettingOverwritten(key: string | [string, string]): boolean {
    if (Array.isArray(key)) return this.config[key[0]][key[1]] !== undefined;
    
    const inspected = this.config.inspect(key);
    return !!inspected && !!(
      inspected.globalValue ||
      inspected.workspaceValue ||
      inspected.workspaceFolderValue ||
      inspected.defaultLanguageValue ||
      inspected.globalLanguageValue ||
      inspected.workspaceLanguageValue ||
      inspected.workspaceFolderLanguageValue
    );
  }

  // 导出配置
  dumpConfig(): Record<string, string> {
    const result: Record<string, string> = {};
    try {
      const properties = contributes.contributes.configuration[0].properties;
      for (const key in properties) {
        const value = key
          .replace(`${CopilotConfigPrefix}.`, '')
          .split('.')
          .reduce((obj, k) => obj[k], this.config);
        
        if (typeof value === 'object' && value !== null) {
          Object.keys(value)
            .filter(k => k !== 'secret_key')
            .forEach(k => result[`${key}.${k}`] = stringifyValue(value[k]));
        } else {
          result[key] = stringifyValue(value);
        }
      }
    } catch (error) {
      console.error(`Failed to retrieve configuration properties ${error}`);
    }
    return result;
  }

  // 获取语言特定配置
  getLanguageConfig(key: string, languageId?: string): any {
    const config = this.getConfig(key);
    if (languageId === undefined) {
      const editor = vscode.window.activeTextEditor;
      languageId = editor && editor.document.languageId;
    }
    return languageId && languageId in config ? config[languageId] : config['*'];
  }

  // 更新启用配置
  updateEnabledConfig(context: MyContext, languageId: string, enabled: boolean): Thenable<void> {
    const enableConfig = context.get(ConfigProvider).getConfig(ConfigKey.Enable);
    enableConfig[languageId] = enabled;
    return this.config.update(ConfigKey.Enable as string, enableConfig, true);
  }
}

// 扩展实例
let extension: vscode.Extension<any> | undefined;

// 设置扩展
export function setExtension(ext: vscode.Extension<any>): void {
  extension = ext;
}

// 获取扩展
export function getExtension(context: MyContext): vscode.Extension<any> {
  if (!extension && isRunningInTest(context)) {
    extension = vscode.extensions.all.find(ext => ext.id.startsWith('GitHub.copilot'));
  }
  if (!extension) throw new Error('No GitHub.copilot extension found');
  return extension;
}

// 创建 VscInfo 实例
export function makeVscInfo(): VscInfo {
  return new VscInfo(vscode.env.sessionId, vscode.env.machineId, vscode.version);
}

// VSCode 编辑器信息类
class VSCodeEditorInfo extends EditorAndPluginInfo {
  getEditorInfo(context: MyContext): { name: string; version: string } {
    return {
      name: 'vscode',
      version: vscode.version,
    };
  }

  getEditorPluginInfo(context: MyContext): { name: string; version: string } {
    return {
      name: 'copilot',
      version: getVersion(context),
    };
  }
}

export { VSCodeConfigProvider, VSCodeEditorInfo };
