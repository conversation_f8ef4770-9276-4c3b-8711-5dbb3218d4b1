/**
 * 3055456
 * promptElementManager.js
 *
 * 该文件主要用于管理和处理提示元素，包括提示背景、选择、范围和优先级。
 *
 * 主要功能：
 * 1. 定义和管理提示元素的类型（PromptElementKind）
 * 2. 处理提示背景信息（PromptBackground）
 * 3. 管理提示选择（PromptChoices）
 * 4. 计算和存储提示元素范围（PromptElementRanges）
 * 5. 实现提示愿望列表功能（PromptWishlist），包括内容管理和提示填充
 * 6. 处理优先级系统（Priorities）
 *
 * 该模块是Copilot提示系统的核心组件，负责组织和优化提示内容，
 * 以提供最相关和有效的代码建议。
 */

// 导入所需模块
// import * as promptManager from './promptManager';
// import * as tokenizer from './tokenizer';
import tokenUtil from "./tokenizer2";
export enum LineEndingOptionsType {
  ConvertToUnix = "unix",
  KeepOriginal = "keep",
}
/**
 * 提示元素的类型枚举
 */
export enum PromptElementKind {
  BeforeCursor = "BeforeCursor", // 光标之前的内容
  AfterCursor = "AfterCursor", // 光标之后的内容
  SimilarFile = "SimilarFile", // 相似文件的内容
  ImportedFile = "ImportedFile", // 导入文件的内容
  LanguageMarker = "LanguageMarker", // 语言标记
  PathMarker = "PathMarker", // 路径标记
}

/**
 * 表示一个提示元素的接口
 */
interface PromptElement {
  id: number; // 元素的唯一标识符
  text: string; // 元素的文本内容
  kind: PromptElementKind; // 元素的类型
  priority: number; // 元素的优先级
  tokens: number; // 元素包含的令牌数
  requires: PromptElement[]; // 该元素依赖的其他元素
  excludes: PromptElement[]; // 与该元素互斥的其他元素
  score: number; // 元素的分数
}

/**
 * 表示相邻标签信息的接口
 */
interface NeighboringTabInfo {
  id: string; // 标签的唯一标识符
  score: number; // 标签的相似度分数
  text: string; // 标签的文本内容
  kind: PromptElementKind; // 标签的类型
}

/**
 * 表示转换后的相邻标签信息的接口
 */
interface ConvertedNeighboringTabInfo {
  score: string; // 格式化后的分数
  length: number; // 文本内容的长度
}

/**
 * 管理提示背景信息，包括已使用和未使用的相邻标签
 */
export class PromptBackground {
  private usedTabs: Map<string, ConvertedNeighboringTabInfo> = new Map();
  private unusedTabs: Map<string, ConvertedNeighboringTabInfo> = new Map();

  /**
   * 标记一个标签为已使用
   * @param tabInfo 相邻标签信息
   */
  markUsed(tabInfo: NeighboringTabInfo): void {
    if (this.isNeighboringTab(tabInfo)) {
      this.usedTabs.set(tabInfo.id, this.convertTabInfo(tabInfo));
    }
  }

  /**
   * 撤销标记一个标签为已使用
   * @param tabInfo 相邻标签信息
   */
  undoMarkUsed(tabInfo: NeighboringTabInfo): void {
    if (this.isNeighboringTab(tabInfo)) {
      this.usedTabs.delete(tabInfo.id);
    }
  }

  /**
   * 标记一个标签为未使用
   * @param tabInfo 相邻标签信息
   */
  markUnused(tabInfo: NeighboringTabInfo): void {
    if (this.isNeighboringTab(tabInfo)) {
      this.unusedTabs.set(tabInfo.id, this.convertTabInfo(tabInfo));
    }
  }

  /**
   * 转换标签信息为简化格式
   * @param tabInfo 相邻标签信息
   * @returns 转换后的标签信息
   */
  private convertTabInfo(
    tabInfo: NeighboringTabInfo
  ): ConvertedNeighboringTabInfo {
    return {
      score: tabInfo.score.toFixed(4),
      length: tabInfo.text.length,
    };
  }

  /**
   * 检查给定的标签信息是否为相邻标签
   * @param tabInfo 相邻标签信息
   * @returns 是否为相邻标签
   */
  private isNeighboringTab(tabInfo: NeighboringTabInfo): boolean {
    return tabInfo.kind == PromptElementKind.SimilarFile;
  }
}

/**
 * 管理提示选择，跟踪已使用和未使用的令牌数量
 */
export class PromptChoices {
  private usedTokens: Map<PromptElementKind, number> = new Map();
  private unusedTokens: Map<PromptElementKind, number> = new Map();

  /**
   * 标记一个元素为已使用，并更新已使用的令牌数
   * @param element 要标记的提示元素
   */
  markUsed(element: PromptElement): void {
    this.usedTokens.set(
      element.kind,
      (this.usedTokens.get(element.kind) || 0) + element.tokens
    );
  }

  /**
   * 撤销标记一个元素为已使用，并更新已使用的令牌数
   * @param element 要撤销标记的提示元素
   */
  undoMarkUsed(element: PromptElement): void {
    this.usedTokens.set(
      element.kind,
      (this.usedTokens.get(element.kind) || 0) - element.tokens
    );
  }

  /**
   * 标记一个元素为未使用，并更新未使用的令牌数
   * @param element 要标记为未使用的提示元素
   */
  markUnused(element: PromptElement): void {
    this.unusedTokens.set(
      element.kind,
      (this.unusedTokens.get(element.kind) || 0) + element.tokens
    );
  }
}

interface PromptElementRange {
  kind: PromptElementKind;
  start: number;
  end: number;
}

/**
 * 管理提示元素的范围信息
 */
export class PromptElementRanges {
  ranges: PromptElementRange[] = [];

  /**
   * 创建一个新的PromptElementRanges实例
   * @param elements 提示元素数组
   */
  constructor(elements: { element: PromptElement }[]) {
    let previousKind: PromptElementKind | undefined;
    let currentPosition = 0;
    for (const { element } of elements) {
      if (element.text.length !== 0) {
        // 如果当前元素和前一个元素都是BeforeCursor类型，合并它们的范围
        if (
          previousKind === PromptElementKind.BeforeCursor &&
          element.kind === PromptElementKind.BeforeCursor
        ) {
          this.ranges[this.ranges.length - 1].end += element.text.length;
        } else {
          // 否则，创建一个新的范围
          this.ranges.push({
            kind: element.kind,
            start: currentPosition,
            end: currentPosition + element.text.length,
          });
        }
        previousKind = element.kind;
        currentPosition += element.text.length;
      }
    }
  }
}

/**
 * 表示fulfill方法的结果接口
 */
interface FulfillResult {
  prefix: string; // 选中元素组成的前缀文本
  suffix: string; // 选中元素组成的后缀文本
  prefixLength: number; // 前缀文本的长度
  suffixLength: number; // 后缀文本的长度
  promptChoices: PromptChoices; // 提示选择信息
  promptBackground: PromptBackground; // 提示背景信息
  promptElementRanges: PromptElementRanges; // 提示元素范围信息
}

export class PromptWishlist {
  private content: PromptElement[] = [];
  private lineEndingOption: LineEndingOptionsType;

  /**
   * 创建一个新的PromptWishlist实例
   * @param lineEndingOption 行尾处理选项
   */
  constructor(lineEndingOption: LineEndingOptionsType) {
    this.lineEndingOption = lineEndingOption;
  }

  /**
   * 获取当前内容的副本
   * @returns PromptElement数组的副本
   */
  getContent(): PromptElement[] {
    return [...this.content];
  }

  /**
   * 根据设置的选项转换行尾
   * @param text 要转换的文本
   * @returns 转换后的文本
   */
  private convertLineEndings(text: string): string {
    if (this.lineEndingOption === LineEndingOptionsType.ConvertToUnix) {
      text = text.replace(/\r\n/g, "\n").replace(/\r/g, "\n");
    }
    return text;
  }

  /**
   * 添加一个新的提示元素
   * @param text 元素文本
   * @param kind 元素类型
   * @param priority 优先级
   * @param tokens 令牌数（默认使用tokenUtil计算）
   * @param score 分数（默认为NaN）
   * @returns 新添加元素的ID
   */
  append(
    text: string,
    kind: PromptElementKind,
    priority: number,
    tokens: number = tokenUtil.tokenLength(text),
    score: number = NaN
  ): number {
    text = this.convertLineEndings(text);
    const id = this.content.length;
    this.content.push({
      id,
      text,
      kind,
      priority,
      tokens,
      requires: [],
      excludes: [],
      score,
    });
    return id;
  }

  /**
   * 逐行添加提示元素
   * @param text 要添加的文本
   * @param kind 元素类型
   * @param priority 优先级
   * @returns 添加的元素ID数组
   */
  appendLineForLine(
    text: string,
    kind: PromptElementKind,
    priority: number
  ): number[] {
    const lines = (text = this.convertLineEndings(text)).split("\n");
    for (let i = 0; i < lines.length - 1; i++) lines[i] += "\n";
    const processedLines: string[] = [];
    lines.forEach((line) => {
      if (
        "\n" === line &&
        processedLines.length > 0 &&
        !processedLines[processedLines.length - 1].endsWith("\n\n")
      ) {
        processedLines[processedLines.length - 1] += "\n";
      } else {
        processedLines.push(line);
      }
    });
    const ids: number[] = [];
    processedLines.forEach((line, index) => {
      if ("" !== line) {
        ids.push(this.append(line, kind, priority));
        if (index > 0) {
          this.content[this.content.length - 2].requires = [
            this.content[this.content.length - 1],
          ];
        }
      }
    });
    return ids;
  }

  /**
   * 设置元素之间的依赖关系
   * @param sourceId 源元素ID
   * @param targetId 目标元素ID
   */
  require(sourceId: number, targetId: number): void {
    const sourceElement = this.content.find(
      (element) => element.id === sourceId
    );
    const targetElement = this.content.find(
      (element) => element.id === targetId
    );
    if (sourceElement && targetElement) {
      sourceElement.requires.push(targetElement);
    }
  }

  /**
   * 设置元素之间的排斥关系
   * @param sourceId 源元素ID
   * @param targetId 目标元素ID
   */
  exclude(sourceId: number, targetId: number): void {
    const sourceElement = this.content.find(
      (element) => element.id === sourceId
    );
    const targetElement = this.content.find(
      (element) => element.id === targetId
    );
    if (sourceElement && targetElement) {
      sourceElement.excludes.push(targetElement);
    }
  }

  /**
   * 根据给定的最大令牌数选择和组织提示元素
   * @param maxTokens 允许的最大令牌数
   * @returns FulfillResult 包含选定的提示元素和相关信息
   */
  fulfill(maxTokens: number): FulfillResult {
    // 初始化提示选择和背景
    const promptChoices = new PromptChoices();
    const promptBackground = new PromptBackground();

    // 将内容元素映射为包含索引的对象，并按优先级和索引排序
    const sortedElements = this.content.map((element, index) => ({
      element,
      index,
    }));
    sortedElements.sort((a, b) =>
      a.element.priority === b.element.priority
        ? b.index - a.index
        : b.element.priority - a.element.priority
    );

    // 初始化用于跟踪已包含和排除的元素ID的集合
    const includedIds = new Set<number>();
    const excludedIds = new Set<number>();
    let lastElement: { element: PromptElement; index: number } | undefined;
    const selectedElements: { element: PromptElement; index: number }[] = [];
    let remainingTokens = maxTokens;

    // 遍历排序后的元素，选择符合条件的元素
    sortedElements.forEach((currentElement) => {
      const element = currentElement.element;
      const index = currentElement.index;
      if (
        remainingTokens >= 0 &&
        (remainingTokens > 0 || lastElement === undefined) &&
        element.requires.every((req) => includedIds.has(req.id)) &&
        !excludedIds.has(element.id)
      ) {
        // 计算所需的令牌数，考虑特殊情况（如换行符）
        let requiredTokens = element.tokens;
        const nextElement = this.findNextElement(
          selectedElements,
          index
        )?.element;
        if (
          element.text.endsWith("\n\n") &&
          nextElement &&
          !nextElement.text.match(/^\s/)
        ) {
          requiredTokens++;
        }

        // 如果剩余令牌足够，添加当前元素
        if (remainingTokens >= requiredTokens) {
          remainingTokens -= requiredTokens;
          includedIds.add(element.id);
          element.excludes.forEach((excluded) => excludedIds.add(excluded.id));
          promptChoices.markUsed(element);
          if (element.kind === PromptElementKind.SimilarFile) {
            promptBackground.markUsed({
              id: element.id.toString(),
              score: element.score,
              text: element.text,
              kind: element.kind,
            });
          }
          selectedElements.push(currentElement);
        } else {
          // 如果剩余令牌不足，将当前元素标记为最后一个元素
          lastElement = lastElement ?? currentElement;
        }
      } else {
        // 如果元素不符合条件，标记为未使用
        promptChoices.markUnused(element);
        if (element.kind === PromptElementKind.SimilarFile) {
          promptBackground.markUnused({
            id: element.id.toString(),
            score: element.score,
            text: element.text,
            kind: element.kind,
          });
        }
      }
    });

    // 按原始索引排序选定的元素
    selectedElements.sort((a, b) => a.index - b.index);
    let resultText = selectedElements.reduce(
      (text, el) => text + el.element.text,
      ""
    );
    let resultTokens = tokenUtil.tokenLength(resultText);

    // 如果结果超过最大令牌数，移除元素直到符合限制
    while (resultTokens > maxTokens) {
      selectedElements.sort((a, b) =>
        b.element.priority === a.element.priority
          ? b.index - a.index
          : b.element.priority - a.element.priority
      );
      const removedElement = selectedElements.pop();
      if (removedElement) {
        promptChoices.undoMarkUsed(removedElement.element);
        promptChoices.markUnused(removedElement.element);
        if (removedElement.element.kind === PromptElementKind.SimilarFile) {
          promptBackground.undoMarkUsed({
            id: removedElement.element.id.toString(),
            score: removedElement.element.score,
            text: removedElement.element.text,
            kind: removedElement.element.kind,
          });
          promptBackground.markUnused({
            id: removedElement.element.id.toString(),
            score: removedElement.element.score,
            text: removedElement.element.text,
            kind: removedElement.element.kind,
          });
        }
        lastElement = undefined;
      }
      selectedElements.sort((a, b) => a.index - b.index);
      resultText = selectedElements.reduce(
        (text, el) => text + el.element.text,
        ""
      );
      resultTokens = tokenUtil.tokenLength(resultText);
    }

    // 尝试添加最后一个元素（如果存在）
    const finalElements = [...selectedElements];
    if (lastElement !== undefined) {
      finalElements.push(lastElement);
      finalElements.sort((a, b) => a.index - b.index);
      const extendedText = finalElements.reduce(
        (text, el) => text + el.element.text,
        ""
      );
      const extendedTokens = tokenUtil.tokenLength(extendedText);
      if (extendedTokens <= maxTokens) {
        // 如果添加最后一个元素后仍然符合令牌限制，则使用扩展后的结果
        promptChoices.markUsed(lastElement.element);
        if (lastElement.element.kind === PromptElementKind.SimilarFile) {
          promptBackground.markUsed({
            id: lastElement.element.id.toString(),
            score: lastElement.element.score,
            text: lastElement.element.text,
            kind: lastElement.element.kind,
          });
        }
        const promptElementRanges = new PromptElementRanges(finalElements);
        return {
          prefix: extendedText,
          suffix: "",
          prefixLength: extendedTokens,
          suffixLength: 0,
          promptChoices,
          promptBackground,
          promptElementRanges,
        };
      }
      // 如果添加最后一个元素后超过令牌限制，则将其标记为未使用
      promptChoices.markUnused(lastElement.element);
      if (lastElement.element.kind === PromptElementKind.SimilarFile) {
        promptBackground.markUnused({
          id: lastElement.element.id.toString(),
          score: lastElement.element.score,
          text: lastElement.element.text,
          kind: lastElement.element.kind,
        });
      }
    }

    // 创建最终的提示元素范围
    const promptElementRanges = new PromptElementRanges(selectedElements);

    // 返回最终结果
    return {
      prefix: resultText,
      suffix: "",
      prefixLength: resultTokens,
      suffixLength: 0,
      promptChoices,
      promptBackground,
      promptElementRanges,
    };
  }

  private findNextElement(
    elements: { element: PromptElement; index: number }[],
    currentIndex: number
  ): { element: PromptElement; index: number } | undefined {
    let nextElement: { element: PromptElement; index: number } | undefined;
    let minIndex = Infinity;
    for (const element of elements) {
      if (element.index > currentIndex && element.index < minIndex) {
        nextElement = element;
        minIndex = element.index;
      }
    }
    return nextElement;
  }
}

/**
 * 管理优先级系统，提供优先级注册和计算功能
 */
export class Priorities {
  static readonly TOP = 1;
  static readonly BOTTOM = 0;

  private registeredPriorities: number[] = [0, 1];

  /**
   * 注册一个新的优先级
   * @param priority 要注册的优先级值
   * @returns 注册的优先级值
   * @throws 如果优先级值不在0到1之间
   */
  register(priority: number): number {
    if (priority > Priorities.TOP || priority < Priorities.BOTTOM)
      throw new Error("Priority must be between 0 and 1");
    this.registeredPriorities.push(priority);
    return priority;
  }

  /**
   * 计算给定优先级列表中最高优先级的正上方优先级
   * @param priorities 优先级列表
   * @returns 计算得到的新优先级
   */
  justAbove(...priorities: number[]): number {
    const maxPriority = Math.max(...priorities);
    const nextHigherPriority = Math.min(
      ...this.registeredPriorities.filter((p) => p > maxPriority)
    );
    return this.register((nextHigherPriority + maxPriority) / 2);
  }

  /**
   * 计算给定优先级列表中最低优先级的正下方优先级
   * @param priorities 优先级列表
   * @returns 计算得到的新优先级
   */
  justBelow(...priorities: number[]): number {
    const minPriority = Math.min(...priorities);
    const nextLowerPriority = Math.max(
      ...this.registeredPriorities.filter((p) => p < minPriority)
    );
    return this.register((nextLowerPriority + minPriority) / 2);
  }

  /**
   * 计算两个优先级之间的中间优先级
   * @param lowerPriority 较低的优先级
   * @param higherPriority 较高的优先级
   * @returns 计算得到的中间优先级
   * @throws 如果给定的优先级不相邻或不在已注册的优先级列表中
   */
  between(lowerPriority: number, higherPriority: number): number {
    if (
      this.registeredPriorities.some(
        (p) => p > lowerPriority && p < higherPriority
      ) ||
      !this.registeredPriorities.includes(lowerPriority) ||
      !this.registeredPriorities.includes(higherPriority)
    )
      throw new Error("Priorities must be adjacent in the list of priorities");
    return this.register((lowerPriority + higherPriority) / 2);
  }
}
