/**
 * 3055404
 * jaccardMatcher.ts
 * 
 * 本文件实现了基于Jaccard相似度的文本匹配器。
 * 主要包含两种匹配器：固定窗口大小的Jaccard匹配器和基于缩进的Jaccard匹配器。
 * 这些匹配器用于计算文本片段之间的相似度，可能用于代码补全或相似度分析等任务。
 */

import * as windowDelineationUtils from './windowDelineator';
import { WindowedMatcher } from './windowedMatcher';
import {Context} from './currentContext'

/**
 * 固定窗口大小的Jaccard匹配器
 */
export class FixedWindowSizeJaccardMatcher extends WindowedMatcher {
  private windowLength: number;

  /**
   * 构造函数
   * @param context - 要处理的文档对象
   * @param windowSize - 窗口大小
   */
  constructor(context: Context, windowSize: number) {
    super(context);
    this.windowLength = windowSize;
  }

  /**
   * 获取匹配器的唯一标识符
   * @returns 匹配器的ID
   */
  id(): string {
    return "fixed:" + this.windowLength;
  }

  /**
   * 获取文档的窗口划分
   * @param document - 要划分的文档
   * @returns 窗口划分结果
   */
  getWindowsDelineations(lines: string[]): [number, number][] {
    const windows: [number, number][] = [];
    const documentLength = lines.length;
    for (let startIndex = 0; startIndex === 0 || startIndex < documentLength - this.windowLength; startIndex++) {
      const endIndex = Math.min(startIndex + this.windowLength, documentLength);
      windows.push([startIndex, endIndex]);
    }
    return windows;
  }

  /**
   * 裁剪文档
   * @param source - 源文档内容
   * @returns 裁剪后的文档内容
   */
  trimDocument(context: Context): string {
    return context.source
      .split("\n")
      .slice(-this.windowLength)
      .join("\n");
  }

  /**
   * 计算相似度得分
   * @param set1 - 第一个集合
   * @param set2 - 第二个集合
   * @returns 相似度得分
   */
  similarityScore(set1: Set<string>, set2: Set<string>): number {
    return computeScore(set1, set2);
  }

  static FACTORY = (windowSize: number) => ({
    to: (context: Context) => new FixedWindowSizeJaccardMatcher(context, windowSize),
  });
}

/**
 * 基于缩进的Jaccard匹配器
 */
export class IndentationBasedJaccardMatcher extends WindowedMatcher {
  private indentationMinLength: number;
  private indentationMaxLength: number;
  private languageId: string;

  /**
   * 构造函数
   * @param document - 要处理的文档对象
   * @param minLength - 最小缩进长度
   * @param maxLength - 最大缩进长度
   */
  constructor(context: Context, minLength: number, maxLength: number) {
    super(context);
    this.indentationMinLength = minLength;
    this.indentationMaxLength = maxLength;
    this.languageId = context.languageId;
  }

  /**
   * 获取匹配器的唯一标识符
   * @returns 匹配器的ID
   */
  id(): string {
    return `indent:${this.indentationMinLength}:${this.indentationMaxLength}:${this.languageId}`;
  }

  /**
   * 获取文档的窗口划分
   * @param document - 要划分的文档
   * @returns 窗口划分结果
   */
  getWindowsDelineations(lines: string[]): number[][]{
    return windowDelineationUtils.getWindowsDelineations(
      lines,
      this.languageId,
      this.indentationMinLength,
      this.indentationMaxLength
    );
  }

  /**
   * 裁剪文档
   * @param sourceDocument - 源文档对象
   * @returns 裁剪后的文档内容
   */
  trimDocument(context: Context,): string {
    return context.source
      .slice(0, context.offset)
      .split("\n")
      .slice(-this.indentationMaxLength)
      .join("\n");
  }

  /**
   * 计算相似度得分
   * @param set1 - 第一个集合
   * @param set2 - 第二个集合
   * @returns 相似度得分
   */
  similarityScore(set1: Set<string>, set2: Set<string>): number {
    return computeScore(set1, set2);
  }

  static FACTORY = (minLength: number, maxLength: number) => ({
    to: (context: Context) => new IndentationBasedJaccardMatcher(context, minLength, maxLength),
  });
}

/**
 * 计算两个集合的Jaccard相似度
 * @param set1 - 第一个集合
 * @param set2 - 第二个集合
 * @returns Jaccard相似度
 */
/**
 * 计算两个集合的相似度得分
 * @param set1 第一个集合
 * @param set2 第二个集合
 * @returns 相似度得分，范围在0到1之间
 */
export function computeScore(set1: Set<unknown>, set2: Set<unknown>): number {
  // 创建一个新的集合来存储两个集合的交集
  const intersection: Set<unknown> = new Set();

  // 遍历第一个集合的元素
  set1.forEach((element) => {
    // 如果第二个集合包含当前元素，则将其添加到交集中
    if (set2.has(element)) {
      intersection.add(element);
    }
  });

  // 计算并返回相似度得分
  // 公式：交集大小 / (集合1大小 + 集合2大小 - 交集大小)
  return intersection.size / (set1.size + set2.size - intersection.size);
}
