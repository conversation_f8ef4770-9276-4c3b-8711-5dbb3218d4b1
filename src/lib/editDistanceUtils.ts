/**
 * editDistanceUtils.ts
 * 
 * 这个文件包含了与编辑距离和词法编辑距离相关的实用函数。
 * 主要功能包括：
 * - 计算字符串之间的编辑距离
 * - 计算词法编辑距离
 * - 词法分析
 * - 词典操作
 * 
 * 这些函数可用于文本比较、相似度计算等场景。
 */

// 定义编辑距离函数的返回类型
interface EditDistanceResult {
  distance: number;
  startOffset: number;
  endOffset: number;
}

// 定义词法分析结果的类型
type LexicalAnalysisResult = [number, number][];

// 定义词法编辑距离函数的返回类型
interface LexEditDistanceResult {
  lexDistance: number;
  startOffset: number;
  endOffset: number;
  haystackLexLength: number;
  needleLexLength: number;
}

// 编辑距离函数
function editDistance<T>(
  e: T[],
  t: T[],
  n: (e: T, t: T, i: number, j: number) => number = (e, t) => (e === t ? 0 : 1)
): EditDistanceResult {
  if (t.length === 0 || e.length === 0)
    return {
      distance: t.length,
      startOffset: 0,
      endOffset: 0,
    };

  let r = new Array(t.length + 1).fill(0);
  let o = new Array(t.length + 1).fill(0);
  let i = new Array(e.length + 1).fill(0);
  let s = new Array(e.length + 1).fill(0);
  let a = t[0];

  for (let t = 0; t < e.length + 1; t++) {
    r[t] = t === 0 ? 1 : n(e[t - 1], a, t - 1, 0);
    o[t] = t > 0 ? t - 1 : 0;
  }

  for (let c = 1; c < t.length; c++) {
    let l = i;
    i = r;
    r = l;
    l = s;
    s = o;
    o = l;
    a = t[c];
    r[0] = c + 1;

    for (let t = 1; t < e.length + 1; t++) {
      const l = 1 + i[t];
      const u = 1 + r[t - 1];
      const d = n(e[t - 1], a, t - 1, c) + i[t - 1];
      r[t] = Math.min(u, l, d);
      if (r[t] === d) {
        o[t] = s[t - 1];
      } else if (r[t] === l) {
        o[t] = s[t];
      } else {
        o[t] = o[t - 1];
      }
    }
  }

  let c = 0;
  for (let t = 0; t < e.length + 1; t++)
    if (r[t] < r[c]) {
      c = t;
    }

  return {
    distance: r[c],
    startOffset: o[c],
    endOffset: c,
  };
}

// 创建空的词典
function emptyLexDictionary(): Map<string, number> {
  return new Map();
}

// 反转词典
function reverseLexDictionary(e: Map<string, number>): string[] {
  const t = new Array(e.size);
  for (const [n, r] of e) t[r] = n;
  return t;
}

// 词法生成器
function* lexGeneratorWords(e: string): Generator<string, void, unknown> {
  enum TokenType {
    Word,
    Space,
    Other,
  }

  let n = "";
  let r = TokenType.Word;

  for (const o of e) {
    let e: TokenType;
    e = /(\p{L}|\p{Nd}|_)/u.test(o) ? TokenType.Word : o === " " ? TokenType.Space : TokenType.Other;
    if (e === r && e !== TokenType.Other) {
      n += o;
    } else {
      if (n.length > 0) {
        yield n;
      }
      n = o;
      r = e;
    }
  }
  if (n.length > 0) {
    yield n;
  }
}

// 词法分析器
function lexicalAnalyzer(
  e: string,
  t: Map<string, number>,
  n: (e: string) => Generator<string, void, unknown>,
  r: (s: string) => boolean
): [LexicalAnalysisResult, Map<string, number>] {
  const o: LexicalAnalysisResult = [];
  let i = 0;
  for (const s of n(e)) {
    if (r(s)) {
      if (!t.has(s)) {
        t.set(s, t.size);
      }
      o.push([t.get(s)!, i]);
    }
    i += s.length;
  }
  return [o, t];
}

// 判断是否为非空格字符
function a(e: string): boolean {
  return e !== " ";
}

// 词法编辑距离函数
function lexEditDistance(
  e: string,
  t: string,
  c: (e: string) => Generator<string, void, unknown> = lexGeneratorWords
): LexEditDistanceResult {
  const [l, u] = lexicalAnalyzer(e, emptyLexDictionary(), c, a);
  const [d, p] = lexicalAnalyzer(t, u, c, a);

  if (d.length === 0 || l.length === 0)
    return {
      lexDistance: d.length,
      startOffset: 0,
      endOffset: 0,
      haystackLexLength: l.length,
      needleLexLength: d.length,
    };

  const h = reverseLexDictionary(p);
  const f = d.length;
  const m = h[d[0][0]];
  const g = h[d[f - 1][0]];

  const _ = editDistance(
    l.map((e) => e[0]),
    d.map((e) => e[0]),
    function (e: number, t: number, n: number, r: number): number {
      if (r === 0 || r === f - 1) {
        const e = h[l[n][0]];
        return (r == 0 && e.endsWith(m)) || (r == f - 1 && e.startsWith(g)) ? 0 : 1;
      }
      return e === t ? 0 : 1;
    }
  );

  const y = l[_.startOffset][1];
  let v = _.endOffset < l.length ? l[_.endOffset][1] : e.length;
  if (v > 0 && e[v - 1] === " ") {
    --v;
  }

  return {
    lexDistance: _.distance,
    startOffset: y,
    endOffset: v,
    haystackLexLength: l.length,
    needleLexLength: d.length,
  };
}

// 导出模块
export {
  editDistance,
  emptyLexDictionary,
  reverseLexDictionary,
  lexGeneratorWords,
  lexicalAnalyzer,
  lexEditDistance,
};
