/**
 * 3055610
 * blockParser.ts
 * 
 * 这个文件主要实现了代码块解析器的功能，用于分析和处理不同编程语言的代码块结构。
 * 它包含了通用的BlockParser类，以及针对特定语言的LanguageSpecificBlockParser和AdvancedBlockParser类。
 * 这些解析器可以识别代码块的开始和结束，判断块是否为空，并处理不同语言的特殊语法结构。
 * 
 * 主要功能：
 * 1. 解析不同编程语言的代码块结构
 * 2. 判断代码块是否为空
 * 3. 识别代码块的开始和结束位置
 * 4. 处理特定语言的语法特性（如Python的缩进、JavaScript的花括号等）
 * 5. 提供语言无关的代码块分析工具
 */

// 导入所需的模块
import * as languageParserAndQuerier from './languageParserAndQuerier'; // 假设模块路径为 './someModule'

// 定义基础解析器抽象类
abstract class BaseParser {
  constructor(
    protected languageId: string,
    protected nodeMatch: Record<string, string>,
    protected nodeTypesWithBlockOrStmtChild: Map<string, string>
  ) {}

  // 添加 isEmptyBlockStart 抽象方法
  abstract isEmptyBlockStart(code: string, position: number): Promise<boolean>;

  // 获取指定位置的节点匹配
  async getNodeMatchAtPosition(
    code: string,
    position: number,
    callback: (node: any) => any
  ): Promise<any> {
    const tree = await languageParserAndQuerier.parseTree(this.languageId, code);
    if(tree) {
      try {
        let node = tree.rootNode.descendantForIndex(position);
        while (node) {
          const matchType = this.nodeMatch[node.type];
          if (matchType) {
            if (!this.nodeTypesWithBlockOrStmtChild.has(node.type)) break;
            const childField = this.nodeTypesWithBlockOrStmtChild.get(node.type);
            const childNode = (childField===undefined || childField === "") ? node.namedChildren[0] : node.childForFieldName(childField);
            if (childNode?.type === matchType) break;
          }
          node = node.parent;
        }
        if (!node) return;
        return callback(node);
      } finally {
        tree.delete();
      }
    }
   
  }

  // 获取指定位置的下一个块
  getNextBlockAtPosition(
    code: string,
    position: number,
    callback: (node: any) => any
  ): Promise<any> {
    return this.getNodeMatchAtPosition(code, position, (node) => {
      let lastChild = node.children
        .reverse()
        .find((child: any) => child.type === this.nodeMatch[node.type]);
      if (lastChild) {
        // 处理 Python 特殊情况
        if (this.languageId === "python" && lastChild.parent) {
          const parent = lastChild.parent.type === ":" ? lastChild.parent.parent : lastChild.parent;
          let sibling = parent?.nextSibling;
          while (sibling && sibling.type === "comment") {
            const isInline = sibling.startPosition.row === lastChild.endPosition.row &&
              sibling.startPosition.column >= lastChild.endPosition.column;
            const isIndented = sibling.startPosition.row > parent.endPosition.row &&
              sibling.startPosition.column > parent.startPosition.column;
            if (!isInline && !isIndented) break;
            lastChild = sibling;
            sibling = sibling.nextSibling;
          }
        }
        if (
          !(
            lastChild.endIndex >= lastChild.tree.rootNode.endIndex - 1 &&
            (lastChild.hasError() || lastChild.parent.hasError())
          )
        )
          return callback(lastChild);
      }
    });
  }

  // 检查块体是否已完成
  async isBlockBodyFinished(
    prefix: string,
    suffix: string,
    position: number
  ): Promise<number | undefined> {
    const code = (prefix + suffix).trimEnd();
    const endIndex = await this.getNextBlockAtPosition(code, position, (node) => node.endIndex);
    if (endIndex !== undefined && endIndex < code.length) {
      const suffixStart = endIndex - prefix.length;
      return suffixStart > 0 ? suffixStart : undefined;
    }
  }

  // 获取节点起始位置
  getNodeStart(code: string, position: number): Promise<number | undefined> {
    const trimmedCode = code.trimEnd();
    return this.getNodeMatchAtPosition(trimmedCode, position, (node) => node.startIndex);
  }
}

// 定义简单块解析器类
class SimpleBlockParser extends BaseParser {
  constructor(
    languageId: string,
    private blockEmptyMatch: string,
    private lineMatch: RegExp,
    nodeMatch: Record<string, string>,
    nodeTypesWithBlockOrStmtChild: Map<string, string>
  ) {
    super(languageId, nodeMatch, nodeTypesWithBlockOrStmtChild);
  }

  // 检查是否为块起始
  isBlockStart(line: string): boolean {
    return this.lineMatch.test(line.trimStart());
  }

  // 检查块体是否为空
  async isBlockBodyEmpty(code: string, position: number): Promise<boolean> {
    const result = await this.getNextBlockAtPosition(code, position, (node) => {
      if (node.startIndex < position) {
        position = node.startIndex;
      }
      let content = code.substring(position, node.endIndex).trim();
      return content === "" || content.replace(/\s/g, "") === this.blockEmptyMatch;
    });
    return result === undefined || result;
  }

  // 检查是否为空块起始
  async isEmptyBlockStart(code: string, position: number): Promise<boolean> {
    position = findNonWhitespacePosition(code, position);
    const line = getLine(code, position);
    if (!this.isBlockStart(line)) {
      return false;
    }
    return await this.isBlockBodyEmpty(code, position);
  }
}

// 辅助函数：查找非空白字符位置
function findNonWhitespacePosition(code: string, position: number): number {
  let index = position;
  while (index > 0 && /\s/.test(code.charAt(index - 1))) index--;
  return index;
}

// 辅助函数：获取指定位置所在行
function getLine(code: string, position: number): string {
  const lineStart = code.lastIndexOf("\n", position - 1) + 1;
  const lineEnd = code.indexOf("\n", position);
  return code.slice(lineStart, lineEnd < 0 ? code.length : lineEnd);
}

// 辅助函数：获取节点前的空白字符
function getLeadingWhitespace(node: any, code: string): string | undefined {
  const start = node.startIndex;
  const lineStart = node.startIndex - node.startPosition.column;
  const whitespace = code.substring(lineStart, start);
  if (/^\s*$/.test(whitespace)) return whitespace;
}

// 辅助函数：检查节点缩进
function hasMatchingIndentation(parent: any, child: any, code: string): boolean {
  if (child.startPosition.row <= parent.startPosition.row) return false;
  const parentWhitespace = getLeadingWhitespace(parent, code);
  const childWhitespace = getLeadingWhitespace(child, code);
  return parentWhitespace !== undefined && childWhitespace !== undefined && childWhitespace.startsWith(parentWhitespace);
}

// 定义复杂块解析器类
class ComplexBlockParser extends BaseParser {
  constructor(
    languageId: string,
    nodeMatch: Record<string, string>,
    nodeTypesWithBlockOrStmtChild: Map<string, string>,
    private startKeywords: string[],
    private blockNodeType: string,
    private emptyStatementType: string | null,
    private curlyBraceLanguage: boolean
  ) {
    super(languageId, nodeMatch, nodeTypesWithBlockOrStmtChild);
  }

  // 检查块是否为空
  isBlockEmpty(node: any, position: number): boolean {
    let text = node.text.trim();
    if (this.curlyBraceLanguage) {
      if (text.startsWith("{")) {
        text = text.slice(1);
      }
      if (text.endsWith("}")) {
        text = text.slice(0, -1);
      }
      text = text.trim();
    }
    return (
      text.length === 0 ||
      !(
        this.languageId !== "python" ||
        (node.parent?.type !== "class_definition" &&
          node.parent?.type !== "function_definition") ||
        node.children.length !== 1 ||
        !languageParserAndQuerier.queryPythonIsDocstring(node.parent)
      )
    );
  }

  // 检查是否为空块起始
  // async isEmptyBlockStart(code: string, position: number): Promise<boolean> {
  //   position = findNonWhitespacePosition(code, position);
  //   return (
  //     this.isBlockStart(
  //       (function (e, t) {
  //         const n = e.lastIndexOf("\n", t - 1);
  //         let r = e.indexOf("\n", t);
  //         if (r < 0) {
  //           r = e.length;
  //         }
  //         return e.slice(n + 1, r);
  //       })(e, t)
  //     ) && this.isBlockBodyEmpty(e, t)
  //   );
  // }
  //e, t
  async isEmptyBlockStart(code: string, position: number): Promise<boolean> {
    if (position > code.length) throw new RangeError("Invalid offset");

    // 检查当前行是否只包含空白字符
    for (let i = position; i < code.length && code.charAt(i) !== "\n"; i++) {
      if (/\S/.test(code.charAt(i))) return false;
    }

    position = findNonWhitespacePosition(code, position);
    const tree = await languageParserAndQuerier.parseTree(this.languageId, code);
    if(!tree){
      return false;
    }
    try {
      const node = tree.rootNode.descendantForIndex(position - 1);
      if (!node) return false;

      // 处理特定语言的情况
      if (this.curlyBraceLanguage && node.type === "}") return false;
      if (
        (this.languageId === "javascript" || this.languageId === "typescript") &&
        node.parent &&
        node.parent.type === "object" &&
        node.parent.text.trim() === "{"
      )
        return true;

      // 处理 TypeScript 的特殊情况
      if (this.languageId === "typescript") {
        let current: any = node;
        while (current && current.parent) {
          if (current.type === "function_signature" || current.type === "method_signature") {
            const nextSibling = node.nextSibling;
            return (
              !!(nextSibling && current.hasError && hasMatchingIndentation(current, nextSibling, code)) ||
              (!current.children.find((child: any) => child.type === ";") && current.endIndex <= position)
            );
          }
          current = current.parent;
        }
      }

      // 查找相关节点
      let errorNode: any = null;
      let blockNode: any = null;
      let matchNode: any = null;
      let current: any = node;

      while (current != null) {
        if (current.type === this.blockNodeType) {
          blockNode = current;
          break;
        }
        if (this.nodeMatch[current.type]) {
          matchNode = current;
          break;
        }
        if (current.type === "ERROR") {
          errorNode = current;
          break;
        }
        current = current.parent;
      }

      // 处理找到的节点
      if (blockNode != null) {
        if (!blockNode.parent || !this.nodeMatch[blockNode.parent.type]) return false;
        if (this.languageId === "python") {
          const prevSibling = blockNode.previousSibling;
          if (
            prevSibling != null &&
            prevSibling.hasError &&
            (prevSibling.text.startsWith('"""') || prevSibling.text.startsWith("'''"))
          )
            return true;
        }
        return this.isBlockEmpty(blockNode, position);
      }

      if (errorNode != null) {
        if (
          errorNode.previousSibling?.type === "module" ||
          errorNode.previousSibling?.type === "internal_module"
        )
          return true;

        const children = [...errorNode.children].reverse();
        const startKeyword = children.find((child: any) => child && this.startKeywords.includes(child.type));
        let blockChild = children.find((child: any) => child && child.type === this.blockNodeType);

        if (startKeyword) {
          // 处理不同语言的特殊情况
          switch (this.languageId) {
            case "python":
              // Python 特殊处理逻辑
              break;
            case "javascript":
              // JavaScript 特殊处理逻辑
              break;
            case "typescript":
              // TypeScript 特殊处理逻辑
              break;
          }
          return !(blockChild && blockChild.startIndex > startKeyword.endIndex) || this.isBlockEmpty(blockChild, position);
        }
      }

      if (matchNode != null) {
        const matchType = this.nodeMatch[matchNode.type];
        const lastChild = matchNode.children
          .slice()
          .reverse()
          .find((child: any) => child.type === matchType);

        if (lastChild) return this.isBlockEmpty(lastChild, position);

        if (this.nodeTypesWithBlockOrStmtChild.has(matchNode.type)) {
          const fieldName = this.nodeTypesWithBlockOrStmtChild.get(matchNode.type);
          const child = (fieldName === undefined || fieldName === "") ? matchNode.children[0] : matchNode.childForFieldName(fieldName);
          if (
            child &&
            child.type !== this.blockNodeType &&
            child.type !== this.emptyStatementType
          )
            return false;
        }
        return true;
      }

      return false;
    } finally {
      tree.delete();
    }
  }
}

// 定义支持的语言解析器
const languageParsers: Record<string, BaseParser> = {
  python: new ComplexBlockParser(
    "python",
    {
      class_definition: "block",
      elif_clause: "block",
      else_clause: "block",
      except_clause: "block",
      finally_clause: "block",
      for_statement: "block",
      function_definition: "block",
      if_statement: "block",
      try_statement: "block",
      while_statement: "block",
      with_statement: "block",
    },
    new Map(),
    [
      "def",
      "class",
      "if",
      "elif",
      "else",
      "for",
      "while",
      "try",
      "except",
      "finally",
      "with",
    ],
    "block",
    null,
    false
  ),
  javascript: new ComplexBlockParser(
    "javascript",
    {
      arrow_function: "statement_block",
      catch_clause: "statement_block",
      do_statement: "statement_block",
      else_clause: "statement_block",
      finally_clause: "statement_block",
      for_in_statement: "statement_block",
      for_statement: "statement_block",
      function: "statement_block",
      function_declaration: "statement_block",
      generator_function: "statement_block",
      generator_function_declaration: "statement_block",
      if_statement: "statement_block",
      method_definition: "statement_block",
      try_statement: "statement_block",
      while_statement: "statement_block",
      with_statement: "statement_block",
      class: "class_body",
      class_declaration: "class_body",
    },
    new Map([
      ["arrow_function", "body"],
      ["do_statement", "body"],
      ["else_clause", ""],
      ["for_in_statement", "body"],
      ["for_statement", "body"],
      ["if_statement", "consequence"],
      ["while_statement", "body"],
      ["with_statement", "body"],
    ]),
    [
      "=>",
      "try",
      "catch",
      "finally",
      "do",
      "for",
      "if",
      "else",
      "while",
      "with",
      "function",
      "function*",
      "class",
    ],
    "statement_block",
    "empty_statement",
    true
  ),
  typescript: new ComplexBlockParser(
    "typescript",
    {
      ambient_declaration: "statement_block",
      arrow_function: "statement_block",
      catch_clause: "statement_block",
      do_statement: "statement_block",
      else_clause: "statement_block",
      finally_clause: "statement_block",
      for_in_statement: "statement_block",
      for_statement: "statement_block",
      function: "statement_block",
      function_declaration: "statement_block",
      generator_function: "statement_block",
      generator_function_declaration: "statement_block",
      if_statement: "statement_block",
      internal_module: "statement_block",
      method_definition: "statement_block",
      module: "statement_block",
      try_statement: "statement_block",
      while_statement: "statement_block",
      abstract_class_declaration: "class_body",
      class: "class_body",
      class_declaration: "class_body",
    },
    new Map([
      ["arrow_function", "body"],
      ["do_statement", "body"],
      ["else_clause", ""],
      ["for_in_statement", "body"],
      ["for_statement", "body"],
      ["if_statement", "consequence"],
      ["while_statement", "body"],
      ["with_statement", "body"],
    ]),
    [
      "declare",
      "=>",
      "try",
      "catch",
      "finally",
      "do",
      "for",
      "if",
      "else",
      "while",
      "with",
      "function",
      "function*",
      "class",
    ],
    "statement_block",
    "empty_statement",
    true
  ),
  go: new SimpleBlockParser(
    "go",
    "{}",
    /\b(func|if|else|for)\b/,
    {
      communication_case: "block",
      default_case: "block",
      expression_case: "block",
      for_statement: "block",
      func_literal: "block",
      function_declaration: "block",
      if_statement: "block",
      labeled_statement: "block",
      method_declaration: "block",
      type_case: "block",
    },
    new Map()
  ),
  ruby: new SimpleBlockParser(
    "ruby",
    "end",
    /\b(BEGIN|END|case|class|def|do|else|elsif|for|if|module|unless|until|while)\b|->/,
    {
      begin_block: "}",
      block: "}",
      end_block: "}",
      lambda: "block",
      for: "do",
      until: "do",
      while: "do",
      case: "end",
      do: "end",
      if: "end",
      method: "end",
      module: "end",
      unless: "end",
      do_block: "end",
    },
    new Map()
  ),
};

// 获取指定语言的块解析器
export function getBlockParser(languageId: string): BaseParser {
  return languageParsers[languageParserAndQuerier.languageIdToWasmLanguage(languageId)];
}

// 检查是否为空块起始
export async function isEmptyBlockStart(
  languageId: string,
  code: string,
  position: number
): Promise<boolean> {
  return (
    languageParserAndQuerier.isSupportedLanguageId(languageId) &&
    getBlockParser(languageId).isEmptyBlockStart(code, position)
  );
}

// 检查块体是否已完成
export async function isBlockBodyFinished(
  languageId: string,
  prefix: string,
  suffix: string,
  position: number
): Promise<number | undefined> {
  if (languageParserAndQuerier.isSupportedLanguageId(languageId)) {
    return getBlockParser(languageId).isBlockBodyFinished(prefix, suffix, position);
  }
}

// 获取节点起始位置
export async function getNodeStart(
  languageId: string,
  code: string,
  position: number
): Promise<number | undefined> {
  if (languageParserAndQuerier.isSupportedLanguageId(languageId)) {
    return getBlockParser(languageId).getNodeStart(code, position);
  }
}
