/**
 * @file  4540 documentIgnoreManager.ts
 * @description 该模块负责管理文档忽略逻辑，用于确定GitHub Copilot是否应该处理特定文档。
 * 
 * 主要功能：
 * 1. 提供判断是否应忽略文档的方法
 * 2. 基于文档的语言和URI scheme进行判断
 * 3. 与配置管理器和Copilot常量模块集成，以获取必要的配置信息
 * 
 * 该模块在GitHub Copilot的文档处理流程中起着关键作用，
 * 确保Copilot只在适当的上下文中提供建议，从而提高性能和用户体验。
 */

import {getEnabledConfig} from './configurationManager';
import * as copilotConstants from './copilotConstants';
import {My_Context,Document} from './contextType'
/**
 * 判断是否应该忽略给定的文档
 * @param context - 上下文对象，包含配置信息
 * @param document - 要检查的文档对象
 * @returns 如果应该忽略文档则返回true，否则返回false
 */
export function ignoreDocument(context: My_Context, document: Document): boolean {
  const languageId = document.languageId;
  return (
    // 检查该语言的Copilot功能是否被禁用
    !getEnabledConfig(context, languageId) ||
    // 检查文档的URI scheme是否属于应该被忽略的类型
    [copilotConstants.CopilotScheme, "output", "search-editor"].includes(document.uri.scheme)
  );
}
