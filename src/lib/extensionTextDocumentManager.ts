/**
 * 385
 * extensionTextDocumentManager.ts
 * 
 * 这个文件定义了 ExtensionTextDocumentManager 类，它扩展了基本的 TextDocumentManager 类。
 * 该类提供了与 VSCode 文本文档相关的操作和事件处理，包括：
 * - 文本文档焦点和变更事件的处理
 * - 获取文本文档和相对路径
 * - 查找包含指定文本文档的笔记本
 * 
 * 这个类主要用于 VSCode 扩展中，用于管理和操作文本文档。
 */

import * as path from 'path';
import * as vscode from 'vscode';
import { TextDocumentManager, getRelativePath } from './textDocumentUtils';

export class ExtensionTextDocumentManager extends TextDocumentManager {
  // 文本文档获得焦点事件
  onDidFocusTextDocument = vscode.window.onDidChangeActiveTextEditor;
  
  // 文本文档变更事件
  onDidChangeTextDocument = vscode.workspace.onDidChangeTextDocument;

  // 获取所有文本文档
  get textDocuments(): readonly vscode.TextDocument[] {
    return vscode.workspace.textDocuments;
  }

  // 获取指定文本文档
  async getTextDocument(e: vscode.Uri): Promise<vscode.TextDocument> {
    return vscode.workspace.openTextDocument(e);
  }

  // 获取相对路径
  async getRelativePath(document: vscode.TextDocument | undefined): Promise<string | undefined> {
    if (document) {
      if (document.isUntitled) return undefined;
      
      const workspaceFolders = vscode.workspace.workspaceFolders?.map(folder => folder.uri) ?? [];
      const relativePath = getRelativePath(workspaceFolders, document.fileName);
      
      return relativePath ?? path.basename(document.fileName);
    }
    return undefined;
  }

  // 查找包含指定文本文档的笔记本
  findNotebook(document: vscode.TextDocument): vscode.NotebookDocument | undefined {
    return vscode.workspace.notebookDocuments.find(notebook =>
      notebook.getCells().some(cell => cell.document === document)
    );
  }
}
