/**
 * 8129
 * extensionLocationFactory.ts
 * 
 * 这个文件定义了 ExtensionLocationFactory 类，它是 LocationFactory 的一个扩展实现。
 * 该类提供了创建 VSCode 特定的 Range 和 Position 对象的方法，这些对象在 VSCode 扩展开发中经常使用。
 * 
 * ExtensionLocationFactory 类的主要功能：
 * 1. 创建 VSCode Range 对象，可以用两种方式：
 *    - 使用起始和结束的行号和字符位置
 *    - 使用两个 Position 对象
 * 2. 创建 VSCode Position 对象
 * 
 * 这个工厂类的实现使得在扩展中处理文本位置和范围变得更加方便和统一。
 */

// 导入所需的模块
import * as vscode from 'vscode';
import { LocationFactory } from './locationFactory';
import {VSRange,Position} from './contextType';

// 定义 ExtensionLocationFactory 类，继承自 LocationFactory
export class ExtensionLocationFactory extends LocationFactory {
  /**
   * 创建一个 Range 对象
   * @param startLine 起始行
   * @param startCharacter 起始字符
   * @param endLine 结束行（可选）
   * @param endCharacter 结束字符（可选）
   * @returns vscode.Range 对象
   */
  range(startLine: number|Position, startCharacter: number | Position, endLine?: number, endCharacter?: number): VSRange {
    return endLine !== undefined && endCharacter !== undefined
      ? new vscode.Range(startLine as number, startCharacter as number, endLine, endCharacter)
      : new vscode.Range(startLine as Position, startCharacter as Position);
  }

  /**
   * 创建一个 Position 对象
   * @param line 行号
   * @param character 字符位置
   * @returns vscode.Position 对象
   */
  position(line: number, character: number): Position {
    return new vscode.Position(line, character);
  }
}
