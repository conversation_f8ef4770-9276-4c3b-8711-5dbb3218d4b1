/**
 * 2533 文件名：workerManager.ts
 * 
 * 主要功能：
 * 该模块管理与Web Worker的通信，处理文件系统操作，并提供了一系列与代码分析相关的函数。
 * 它负责初始化worker、处理消息传递、管理文件读取请求，以及提供代码结构分析的功能。
 */
import {promptManager,createWorker,defaultFileSystem,MyFileSystem,blockParser,languageParserAndQuerier,workerThreads} from './moduleExports';

import {Logger } from './loggerManager';
import {MyContext} from './myContext'

type MessageCallback = {
  resolve: (value: any) => void;
  reject: (reason?: any) => void;
};

let worker: workerThreads.Worker | null = null;

const messageCallbacks = new Map<number, MessageCallback>();
let messageId = 0;

/**
 * 初始化worker和相关功能
 * @param context - 上下文对象
 * @param useWorker - 是否使用worker
 * @param logger - 日志对象
 */
export function init(context: MyContext, useWorker: boolean, logger: Logger): void {
  if (!useWorker) {
    // for (const functionName of [...workerFunctions, ...localFunctions]) {
    //   (exports as any)[functionName] = fallbackModule[functionName];
    // }
    return;
  }

  for (const functionName of workerFunctions) {
    (exports as any)[functionName] = createWorkerProxy(context, logger, functionName);
  }

  exports.getPrompt = ((contextParam: MyContext, loggerParam: Logger) => {
    return (unused: any, ...args: any[]) => {
      const id = messageId++;
      return new Promise((resolve, reject) => {
        messageCallbacks.set(id, { resolve, reject });
        loggerParam.debug(contextParam, `Proxy getPrompt - ${id}`);
        if (worker !== null) {
          worker.postMessage({ id, fn: "getPrompt", args });
        }
      });
    };
  })(context, logger);

  worker = createWorker();
  messageCallbacks.clear();
  messageId = 0;
  const fileSystem: MyFileSystem = context.get(MyFileSystem);

  /**
   * 处理错误
   * @param error - 错误对象
   */
  function handleError(error: Error): void {
    logger.error(context, error);
    for (const callback of messageCallbacks.values()) {
      callback.reject(error);
    }
    messageCallbacks.clear();
  }

  worker.on("message", ({ id, err, res }: { id: number; err: any; res: any }) => {
    const callback = messageCallbacks.get(id);
    logger.debug(context, `Response ${id} - ${res}, ${err}`);
    if (callback) {
      messageCallbacks.delete(id);
      if (err) {
        callback.reject(err);
      } else {
        callback.resolve(res);
      }
    }
  });

  worker.on("error", handleError);

  worker.on("exit", (code: number) => {
    if (code !== 0) {
      handleError(new Error(`Worker thread exited with code ${code}.`));
    }
  });

  worker.on("readFileReq", (filePath: string) => {
    logger.debug(context, `READ_FILE_REQ - ${filePath}`);
    fileSystem.readFile(filePath)
      .then((content) => {
        if (worker !== null) {
          worker.emit("readFileRes", content);
        }
      })
      .catch(handleError);
  });

  worker.on("mtimeRes", (filePath: string) => {
    logger.debug(context, `mTime_REQ - ${filePath}`);
    fileSystem.mtime(filePath)
      .then((mtime) => {
        if (worker !== null) {
          worker.emit("mtimeRes", mtime);
        }
      })
      .catch(handleError);
  });
}

/**
 * 终止worker
 */
export function terminate(): void {
  if (worker) {
    worker.removeAllListeners();
    worker.terminate();
    worker = null;
    messageCallbacks.clear();
  }
}

const workerFunctions = [
  "getFunctionPositions",
  "isEmptyBlockStart",
  "isBlockBodyFinished",
  "getNodeStart",
  "parsesWithoutError",
];
const localFunctions = ["isSupportedLanguageId", "getBlockCloseToken"];

/**
 * 创建worker代理函数
 * @param context - 上下文对象
 * @param logger - 日志对象
 * @param functionName - 函数名
 * @returns 代理函数
 */
function createWorkerProxy(context: MyContext, logger: Logger, functionName: string): (...args: any[]) => Promise<any> {
  return (...args: any[]) => {
    const id = messageId++;
    return new Promise((resolve, reject) => {
      messageCallbacks.set(id, { resolve, reject });
      logger.debug(context, `Proxy ${functionName}`);
      if (worker !== null) {
        worker.postMessage({ id, fn: functionName, args });
      }
    });
  };
}

// 导出模块中的函数
export const isEmptyBlockStart = blockParser.isEmptyBlockStart;
export const isBlockBodyFinished = blockParser.isBlockBodyFinished;
export const isSupportedLanguageId = languageParserAndQuerier.isSupportedLanguageId;
export const getBlockCloseToken = languageParserAndQuerier.getBlockCloseToken;
export const getFunctionPositions = languageParserAndQuerier.getFunctionPositions;
export const getNodeStart = blockParser.getNodeStart;
export const getPrompt = promptManager.getPrompt;
export const parsesWithoutError = languageParserAndQuerier.parsesWithoutError;

