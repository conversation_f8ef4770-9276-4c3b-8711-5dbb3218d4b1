/**
 * id：3055563
 * @fileoverview 模块导出和工作线程创建
 * 
 * 该模块提供了以下主要功能：
 * 1. 创建绑定函数：用于将源对象的属性绑定到目标对象。
 * 2. 导出所有属性函数：用于将一个模块的所有属性导出到另一个对象。
 * 3. 导出其他模块的属性：包括语言注释标记、注释函数和文件系统。
 * 4. 创建工作线程：提供创建新的 Worker 线程的功能。
 * 
 * 这个文件作为一个中央导出点，汇集了多个相关模块的功能，并提供了创建工作线程的能力。
 */

import * as path from 'path';
import * as workerThreads from 'worker_threads';

// 创建绑定函数
const createBinding = (target: any, source: any, key: string, descriptor?: PropertyDescriptor): void => {
  if (descriptor === undefined) {
    descriptor = Object.getOwnPropertyDescriptor(source, key);
  }
  Object.defineProperty(target, key, {
    enumerable: true,
    get: function () {
      return source[key];
    },
  });
};

// 导出所有属性函数
const exportAllProperties = (source: any, target: any): void => {
  for (const key in source) {
    if (key !== "default" && !Object.prototype.hasOwnProperty.call(target, key)) {
      createBinding(target, source, key);
    }
  }
};

// 导出其他模块的所有属性
import * as languageParserAndQuerier from './languageParserAndQuerier';
import * as blockParser from './blockParser';
export { blockParser };
import * as promptManager from './promptManager';

//import * as tokenizers from 'tokenizers';

//exportAllProperties(languageParserAndQuerier, exports);
export { languageParserAndQuerier };
//exportAllProperties(blockParser, exports);
//exportAllProperties(promptManager, exports);
export { promptManager };
export { workerThreads };
//exportAllProperties(tokenizers, exports);

// 导入并导出特定属性
import { languageCommentMarkers, comment } from './languageUtils';
export { languageCommentMarkers, comment };

import { defaultFileSystem ,MyFileSystem} from './fileSystemOperations';
export { defaultFileSystem ,MyFileSystem};

// 创建工作线程函数
export const createWorker = (): workerThreads.Worker => {
  return new workerThreads.Worker(path.resolve(__dirname, "..", "dist", "worker.js"));
};
