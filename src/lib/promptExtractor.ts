/**
 * 4969
 * promptExtractor.js - GitHub Copilot 提示提取器
 * 
 * 该模块是 GitHub Copilot 的核心组件之一，主要负责从用户的编辑上下文中提取和处理用于生成代码建议的提示（prompt）。
 * 
 * 主要功能：
 * 1. 提取文档上下文：从当前文档和相邻文档中获取相关代码上下文。
 * 2. 处理 GitHub 仓库信息：获取和处理与当前文档相关的 GitHub 仓库信息。
 * 3. 生成提示：基于提取的上下文和配置信息生成用于 AI 模型的提示。
 * 4. 支持不同文档类型：处理常规文档和笔记本（notebook）文档的不同情况。
 * 5. 优化提示质量：通过各种配置选项（如后缀百分比、FIM 阈值等）优化生成的提示。
 * 
 * 这个模块是 Copilot 理解用户编码上下文和生成相关代码建议的关键部分，
 * 它的输出直接影响 Copilot 生成建议的质量和相关性。
 */
// 导入其他模块
import {defaultFileSystem,MyFileSystem} from './moduleExports';  // 可能包含文件系统相关功能
import * as configModule from './configurationManager';     // 可能包含配置相关功能
import * as documentTrackingModule from './documentAccessTracker';     // 可能包含文档跟踪相关功能
import * as featureModule from './featureManager';     // 可能包含特性相关功能
import * as textDocumentModule from './textDocumentUtils';     // 可能包含文本文档管理相关功能
import * as workerManager from './workerManager';     // 可能包含提示获取相关功能
import { My_Context,Document,Position ,Uri,Cell} from './contextType';
import {SiblingOption} from './promptManager';
import {Context,NeighboringFiles}  from './currentContext';

import { logger } from './loggerManager';
import * as repoInfoModule from './repoInfoManager';
import { ExtensionTextDocumentManager } from './extensionTextDocumentManager';
// 定义常量
export const MIN_PROMPT_CHARS = 10;

// 定义接口
interface ContextTooShort {
  type: "contextTooShort";
}

export interface PromptInfo {
  type: "prompt";
  prompt: Prompt;
  trailingWs: string;
  promptChoices: any[]; // 这里可能需要更具体的类型
  computeTimeMs: number;
  promptBackground: any; // 这里可能需要更具体的类型

}
export type Prompt= {
  prefix: string;
  suffix: string;
  isFimEnabled: boolean;
  promptElementRanges: any[]; // 这里可能需要更具体的类型
};
/**
 * 修剪字符串的最后一行
 * 这个函数用于处理输入字符串的最后一行，去除尾部空白字符。
 * 主要用于处理代码提示时的文本格式化。
 * 
 * @param inputString - 需要处理的输入字符串
 * @returns 返回一个数组，包含修剪后的字符串和被移除的空白字符
 */
export function trimLastLine(inputString: string): [string, string] {
  const lines = inputString.split("\n");
  const lastLine = lines[lines.length - 1];
  const trailingSpaces = lastLine.length - lastLine.trimRight().length;
  const trimmedString = inputString.slice(0, inputString.length - trailingSpaces);
  const removedWhitespace = inputString.substr(trimmedString.length);
  return [lastLine.length == trailingSpaces ? trimmedString : inputString, removedWhitespace];
}

/**
 * 获取提示辅助函数 ！！！！！重点关注
 * 这个函数是获取代码提示的核心功能。它处理文档内容，分析上下文，
 * 并生成适当的提示信息。包括处理GitHub仓库信息、配置后缀百分比、
 * 以及生成最终的提示对象。
 */
async function getPromptInfo(
  context: My_Context,
  documentText: string,
  insertOffset: number,
  relativePath: string,
  documentUri: Uri,
  languageId: string
): Promise<PromptInfo | ContextTooShort> {

  logger.debug(context,"开始获取promptInfo:getPromptInfo")

  // 获取后缀百分比配置和FIM后缀长度阈值
  const suffixPercent =await configModule.suffixPercent();
  // 检查上下文是否太短
  if ((suffixPercent > 0 ? documentText.length : insertOffset) < MIN_PROMPT_CHARS) {
    return { type: "contextTooShort" };
  }
 
  // 记录开始时间
  const startTime = Date.now(); 

  // 调用匿名异步函数获取提示相关信息
  const {
    prefix,
    suffix,
    promptChoices,
    promptBackground,
    promptElementRanges,
  } = await preGetPrompt(context, documentText, insertOffset, relativePath, documentUri, languageId);
  
  // 处理前缀的最后一行进行trim右边
  const [trimmedPrefix, trailingWhitespace] = trimLastLine(prefix);
  // 记录结束时间
  const endTime = Date.now();
  // 返回最终的提示信息对象
  return {
    type: "prompt",
    prompt: {
      prefix: trimmedPrefix,
      suffix: suffix,
      isFimEnabled: true,
      promptElementRanges: promptElementRanges.ranges,
    },
    trailingWs: trailingWhitespace,
    promptChoices: promptChoices,
    computeTimeMs: endTime - startTime,
    promptBackground: promptBackground,
  };
}

async function preGetPrompt(
    context: My_Context,
    text: string,
    offset: number,
    relPath: string,
    docUri: Uri,
    langId: string
  ) {
    // 初始化相邻文档数组
    let neighboringFiles: NeighboringFiles[] = [];
    // 获取相邻文档信息
    neighboringFiles = await fetchNeighboringFiles(context, docUri.fsPath, langId);
    
    // 构建当前文档的信息对象
    const currentDocument:Context = {
      uri: docUri.toString(),
      source: text,
      offset: offset,
      relativePath: relPath,
      languageId: langId,
    } ;
    
    // 再次获取GitHub仓库信息
      const background =repoInfoModule.extractRepoInfoInBackground(context, docUri.fsPath)
      const repoNwo =repoInfoModule.tryGetGitHubNWO(background)
      const githubNwo = (null !== repoNwo && undefined !== repoNwo) ? repoNwo : "";
  
    // 初始化提示配置
    let promptConfig:any = {
      maxPromptLength: 8192 - configModule.getConfig(context, configModule.ConfigKey.SolutionLength),
      neighboringTabs: await context.get(featureModule.Features).neighboringTabsOption(githubNwo, langId),
      suffixStartMode: await context.get(featureModule.Features).suffixStartMode(githubNwo, langId),
    };
    
    // 获取后缀相关配置
    const suffixPercent =20; //await configModule.suffixPercent(context, githubNwo, langId);
    const suffixMatchThreshold = 80; //await configModule.suffixMatchThreshold(context, githubNwo, langId);
    const fimSuffixLengthThreshold = 75;//await configModule.fimSuffixLengthThreshold(context, githubNwo, langId);
    
    // 如果启用了后缀功能，则扩展提示配置
    if (suffixPercent > 0) {
      promptConfig = {
        ...promptConfig,
        includeSiblingFunctions: SiblingOption.NoSiblings,
        suffixPercent: suffixPercent,
        suffixMatchThreshold: suffixMatchThreshold,
        fimSuffixLengthThreshold: fimSuffixLengthThreshold,
      };
    }
    
    // 获取文件系统实例
    const fileSystem = context.get(MyFileSystem);
    // 调用promptModule的getPrompt方法生成提示
    return await workerManager.getPrompt(fileSystem, currentDocument, promptConfig, neighboringFiles);
  }


/**
 * 获取相邻文件信息
 * @param context - 上下文对象
 * @param currentFilePath - 当前文件路径
 * @param currentLanguageId - 当前语言ID
 * @returns 相邻文件数组，包含URI、相对路径、语言ID和源代码
 */
  async function fetchNeighboringFiles(context: My_Context, currentFilePath: string, currentLanguageId: string) {
      const result: NeighboringFiles[] = [];
      const  textDocumentManager =  context.get(textDocumentModule.TextDocumentManager) 
      // 按访问时间对文档进行排序
      const sortedDocs = documentTrackingModule.sortByAccessTimes(textDocumentManager.textDocuments);
      let totalLength = 0;
      // 遍历排序后的文档，收集符合条件的相邻文档
      for (const doc of sortedDocs) {
        const doc_ = doc as Document
       
        if (result.length + 1 > 20 || totalLength + doc_.getText().length > 2e5) break;
        if (doc_.uri.scheme == "file" && doc_.fileName !== currentFilePath && doc_.languageId === currentLanguageId) {
          result.push({
            uri: doc.uri.toString(),
            relativePath: await textDocumentManager.getRelativePath(doc),
            languageId: doc_.languageId,
            source: doc_.getText(),
          });
          totalLength += doc_.getText().length;
        }
      }
      return result;
    }
/**
 * 获取常规文档的提示
 * 这个函数处理常规（非笔记本）文档的提示获取过程。
 * 它获取文档的相对路径，然后调用getPromptInfo函数来生成提示。
 */
async function getPromptForRegularDocument(context: My_Context, document: Document, position: Position): Promise<PromptInfo | ContextTooShort> {
  const relativePath = await context.get(textDocumentModule.TextDocumentManager).getRelativePath(document);
  return getPromptInfo(context, document.getText(), document.offsetAt(position), relativePath, document.uri, document.languageId);
}

/**
 * 提取提示
 * 这是模块的主要导出函数，用于从给定的文档和位置提取代码提示。
 * 它首先检查文档是否属于笔记本，然后相应地处理常规文档或笔记本单元。
 * 对于笔记本，它会考虑前面单元格的内容来生成更准确的提示。
 */
export async function extractPrompt(
                                    context: My_Context,
                                    document: Document,
                                    position: Position,): Promise<PromptInfo | ContextTooShort> {
  logger.debug(context, "提取提示开始extractPrompt ");                    
  logger.debug(context, "默认常规文档提取提示");
    return getPromptForRegularDocument(context, document, position);
}
