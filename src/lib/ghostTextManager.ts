/**
 * 9334
 * GitHub Copilot 幽灵文本管理器 (ghostTextManager.js)
 * 
 * 该模块是GitHub Copilot的核心组件之一，负责管理和处理IDE中显示的实时代码建议（幽灵文本）。
 * 
 * 主要功能：
 * 1. 实时代码建议生成：
 *    - 从OpenAI服务获取代码建议
 *    - 处理多行和单行代码补全
 *    - 支持缓存和复用之前的建议
 * 
 * 2. 性能优化：
 *    - 实现请求去抖动，避免频繁API调用
 *    - 使用LRU缓存存储历史建议
 *    - 支持流式处理大量建议
 * 
 * 3. 上下文感知：
 *    - 分析当前编辑位置的代码上下文
 *    - 考虑缩进和代码风格
 *    - 智能处理多行代码块
 * 
 * 4. 遥测和监控：
 *    - 收集性能和使用数据
 *    - 监控API调用状态
 *    - 错误追踪和日志记录
 * 
 * 5. 用户体验优化：
 *    - 处理建议的显示时机和位置
 *    - 支持建议循环切换
 *    - 处理特殊字符和缩进
 * 
 * 技术特点：
 * - 使用TypeScript/JavaScript开发
 * - 支持异步操作和Promise
 * - 实现了完整的错误处理机制
 * - 集成了详细的日志和遥测系统
 */

import { logger } from './loggerManager';
import { extractPrompt,trimLastLine } from './promptExtractor';
import { My_Context,Document,Position,Token,IndentationInfo } from './contextType';
import {Features} from './featureManager';
import {LocationFactory} from './locationFactory';
import { v4 as uuidv4 } from 'uuid';// 定义结果类型枚举
import { BlockModeConfig,BlockMode,shouldDoParsingTrimming,getConfig ,ConfigKey,shouldDoServerTrimming} from './configurationManager';
import {isSupportedLanguageId } from './workerManager';
import {isEmptyBlockStart ,isBlockBodyFinished,contextIndentation} from './indentationUtils';
import {LRUCache,keyForPrompt} from './cacheUtils';
import {PromptInfo,Prompt} from './promptExtractor';
import * as completionUtils from './completionUtils';
import * as openAIFetcher from './openAIFetcher';


import * as debounceUtils  from './debounceUtils' ;
import * as asyncOps from './asyncIterableOperations' ;
import * as  runtimeManager from './runtimeModeManager';
import * as  postProcessor from './completionPostProcessor';
import * as debounceManager  from './ghostTextDebounceManager';
import {StatusReporter}  from './statusReporter';

export enum ResultType {
  Network,
  Cache,
  TypingAsSuggested,
  Cycling
}

let lastProcessedPrefix:any=undefined;
let lastProcessedPromptKey:any=undefined;

// 创建幽灵文本完成缓存
export const completionCache = new LRUCache<any>(100);

// 创建幽灵文本去抖动器
const ghostTextDebouncer = new debounceUtils.Debouncer();


/**
 * 更新最近处理的前缀和提示键，用于缓存管理
 * @param {string} prefix - 当前的代码前缀
 * @param {string} promptKey - 提示键
 */
function updateLastProcessedInfo(prefix:any, promptKey:any) {
  lastProcessedPrefix = prefix;
  lastProcessedPromptKey = promptKey;
}

interface RequestParams {
  blockMode: BlockMode; // 假设 blockMode 是字符串类型
  languageId: string; // 假设 languageId 是字符串类型
  repoInfo: any; // 根据具体情况定义类型，可能是对象或其他类型
  engineURL: string; // 假设 engineURL 是字符串类型
  ourRequestId: string; // 假设 ourRequestId 是字符串类型
  prefix: string; // 假设 prefix 是字符串类型
  prompt: Prompt; // 假设 prompt 是字符串类型
  multiline: boolean; // 假设 requestMultiline 是布尔类型
  indentation: IndentationInfo; // 假设 indentation 是数字类型
  isCycling: boolean; // 假设 isCycling 是布尔类型
  delayMs: number; // 假设 beforeRequestWaitMs 是数字类型
  multiLogitBias: any; // 根据具体情况定义类型，可能是对象或其他类型
}

/**
 * 从网络获取幽灵文本完成建议
 * @param {Object} context - 执行上下文，包含配置和环境信息
 * @param {Object} requestParams - 请求参数，包含语言、缩进等信息
 * @param {Object} telemetryData - 遥测数据收集对象
 * @param {Function} cancelToken - 用于取消请求的令牌
 * @param {Function} completionCallback - 完成回调函数
 * @param {string} logIdentifier - 日志标识符
 * @param {Function} resultHandler - 处理返回结果的回调函数
 * @returns {Promise<Object>} 返回处理结果，包含建议内容或错误信息
 */
async function fetchGhostTextFromNetwork(context:My_Context, 
                                        requestParams:RequestParams,
                                        telemetryData =null,
                                        cancelToken:Token,
                                        completionCallback:Function,
                                        logIdentifier:string,
                                        resultHandler:Function) {
  var nextIndent;
  var stopJson;
  var logitBiasJson;
  logger.debug(context, `Getting ${logIdentifier} from network start`);
  logger.debug(context, "GhostText:"+ JSON.stringify(requestParams));
  //telemetryData = telemetryData.extendedBy();
  const completionCount = await (async function (context, requestParams) {
    const overrideCount = await context.get(Features).overrideNumGhostCompletions();
    return overrideCount
      ? requestParams.isCycling
        ? Math.max(0, 3 - overrideCount)
        : overrideCount
      : shouldDoParsingTrimming(requestParams.blockMode) && requestParams.multiline
      ? getConfig(context, ConfigKey.InlineSuggestCount)
      : requestParams.isCycling
      ? 2
      : 1;
  })(context, requestParams);
  const temperature = completionUtils.getTemperatureForSamples(context, completionCount);
  const postOptions = {
    stream: true,
    n: completionCount,
    temperature: temperature,
    extra: {
      language: requestParams.languageId,
      next_indent: null !== (nextIndent = requestParams.indentation.next) && undefined !== nextIndent ? nextIndent : 0,
      trim_by_indentation: shouldDoServerTrimming(requestParams.blockMode),
    },
  }as any;
  if (requestParams.multiline) {
    postOptions.stop = ["\n"];
  }
  if (requestParams.multiline && requestParams.multiLogitBias) {
    postOptions.logit_bias = {
      50256: -100,
    };
  }
  const startTime = Date.now();
  // const telemetryProperties = {
  //   endpoint: "completions",
  //   uiKind: openAIFetcher.CopilotUiKind.GhostText,
  //   isCycling: JSON.stringify(requestParams.isCycling),
  //   temperature: JSON.stringify(temperature),
  //   n: JSON.stringify(completionCount),
  //   stop:
  //     null !== (stopJson = JSON.stringify(postOptions.stop)) && undefined !== stopJson ? stopJson : "unset",
  //   logit_bias: JSON.stringify(
  //     null !== (logitBiasJson = postOptions.logit_bias) && undefined !== logitBiasJson ? logitBiasJson : null
  //   ),
  // };
  // const promptLengthTelemetry = telemetryManager.telemetrizePromptLength(requestParams.prompt);
  // Object.assign(telemetryData.properties, telemetryProperties);
  // Object.assign(telemetryData.measurements, promptLengthTelemetry);
  try {
    const fetchParams = {
      prompt: requestParams.prompt,
      languageId: requestParams.languageId,
      repoInfo: requestParams.repoInfo,
      ourRequestId: requestParams.ourRequestId,
      engineUrl: requestParams.engineURL,
      count: completionCount,
      uiKind: 'ghostText',//openAIFetcher.CopilotUiKind.GhostText,
      postOptions: postOptions,
    };
    if (requestParams.delayMs > 0) {
      await new Promise((resolve) => setTimeout(resolve, requestParams.delayMs));
    }
    const fetchResult = await context
      .get(openAIFetcher.OpenAIFetcher)
      .fetchAndCompletions(context, fetchParams, telemetryData, completionCallback, cancelToken);
    return "failed" === fetchResult.type
      ? {
          type: "failed",
          reason: fetchResult.reason,
          telemetryData: undefined//telemetryUtils.mkBasicResultTelemetry(telemetryData),
        }
      : "canceled" === fetchResult.type
      ? (logger.debug(
          context,
          "Cancelled after awaiting fetchCompletions"
        ),
        {
          type: "canceled",
          reason: fetchResult.reason,
          telemetryData: undefined//telemetryUtils.mkCanceledResultTelemetry(telemetryData),
        })
      : resultHandler(completionCount, startTime, fetchResult.getProcessingTime(), fetchResult.choices);
  } catch (error) {
    logger.error(context, `Error on ghost text request ${error}`);  
    return {
      type: "canceled",
      reason: "network request aborted",
      telemetryData:undefined
      // telemetryData: telemetryUtils.mkCanceledResultTelemetry(telemetryData, {
      //   cancelledNetworkRequest: true,
      // }),
    };
    // if (networkModule.isAbortError(error))
    //   return {
    //     type: "canceled",
    //     reason: "network request aborted",
    //     telemetryData:undefined
    //     // telemetryData: telemetryUtils.mkCanceledResultTelemetry(telemetryData, {
    //     //   cancelledNetworkRequest: true,
    //     // }),
    //   };
    // logger.error(context, `Error on ghost text request ${error}`);
    // if ((0, runtimeManager.shouldFailForDebugPurposes)(context)) throw error;
    // return {
    //   type: "failed",
    //   reason: "non-abort error on ghost text request",
    //   telemetryData: undefined//telemetryUtils.mkBasicResultTelemetry(telemetryData),
    // };
  }
}

/**
 * 处理完成结果，对建议文本进行格式化和清理
 * @param {Object} completionResult - 原始完成结果
 * @param {Object} options - 处理选项，如是否强制单行
 * @returns {Object} 处理后的完成结果
 */
function processCompletionResult(completionResult:any, options:any) {
  const processedResult = {
    ...completionResult,
  };
  processedResult.completionText = completionResult.completionText? completionResult.completionText.trimEnd() : completionResult.text.trimEnd();
  if (options.forceSingleLine) {
    processedResult.completionText = processedResult.completionText.split("\n")[0];
  }
  return processedResult;
}

/**
 * 将幽灵文本建议添加到缓存中
 * @param {Object} context - 执行上下文
 * @param {Object} requestParams - 请求参数
 * @param {Object} completionResult - 完成结果
 */
function appendToCompletionCache(context:My_Context, requestParams:RequestParams, completionResult:any) {
  const cacheKey = keyForPrompt(requestParams.prompt);
  const existingCache = completionCache.get(cacheKey);
  if (existingCache && existingCache.multiline === completionResult.multiline) {
    completionCache.put(cacheKey, {
      multiline: existingCache.multiline,
      choices: existingCache.choices.concat(completionResult.choices),
    });
  } else {
    completionCache.put(cacheKey, completionResult);
  }
  logger.debug(
    context,
    `Appended cached ghost text for key: ${cacheKey}, multiline: ${completionResult.multiline}, number of suggestions: ${completionResult.choices.length}`
  );
}

/**
 * 处理完成文本的显示格式
 * @param {number} completionIndex - 完成索引
 * @param {string} completionText - 完成文本
 * @param {string} trailingWhitespace - 尾随空白
 * @returns {Object} 返回处理后的显示对象，包含显示文本和偏移信息
 */
function processCompletionDisplay(completionIndex:number, completionText:string, trailingWhitespace:string) {
  if (trailingWhitespace.length > 0) {
    if (completionText.startsWith(trailingWhitespace))
      return {
        completionIndex: completionIndex,
        completionText: completionText,
        displayText: completionText.substr(trailingWhitespace.length),
        displayNeedsWsOffset: false,
      };
    {
      const r = completionText.substr(0, completionText.length - completionText.trimLeft().length);
      return trailingWhitespace.startsWith(r)
        ? {
            completionIndex: completionIndex,
            completionText: completionText,
            displayText: completionText.trimLeft(),
            displayNeedsWsOffset: true,
          }
        : {
            completionIndex: completionIndex,
            completionText: completionText,
            displayText: completionText,
            displayNeedsWsOffset: false,
          };
    }
  }
  return {
    completionIndex: completionIndex,
    completionText: completionText,
    displayText: completionText,
    displayNeedsWsOffset: false,
  };
}

/**
 * 扩展遥测数据，添加完成相关的指标
 * @param {Object} context - 执行上下文
 * @param {Object} completion - 完成结果
 * @returns {Object} 返回扩展后的遥测数据
 */
function extendTelemetryData(context:My_Context, completion:any) {
  // const requestId = completion.requestId;
  // const properties = {
  //   choiceIndex: completion.choiceIndex.toString(),
  // };
  // const measurements = {
  //   numTokens: completion.numTokens,
  //   compCharLen: completion.completionText.length,
  //   numLines: completion.completionText.split("\n").length,
  // } as any;
  // if (completion.meanLogProb) {
  //   measurements.meanLogProb = completion.meanLogProb;
  // }
  // if (completion.meanAlternativeLogProb) {
  //   measurements.meanAlternativeLogProb = completion.meanAlternativeLogProb;
  // }
  // const extendedTelemetry = completion.telemetryData.extendedBy(properties, measurements);
  // extendedTelemetry.extendWithRequestId(requestId);
  // extendedTelemetry.measurements.confidence = scoreCalculator.ghostTextScoreConfidence(context, extendedTelemetry);
  // extendedTelemetry.measurements.quantile = scoreCalculator.ghostTextScoreQuantile(context, extendedTelemetry);
  // logger.debug(
  //   context,
  //   `Extended telemetry for ${completion.telemetryData.properties.headerRequestId} with retention confidence ${extendedTelemetry.measurements.confidence} (expected as good or better than about ${extendedTelemetry.measurements.quantile} of all suggestions)`
  // );
  // return extendedTelemetry;
  return undefined;
}

/**
 * 记录幽灵文本性能遥测数据F
 * @param {Object} context - 执行上下文
 * @param {string} telemetryType - 遥测类型
 * @param {Object} completion - 完成结果
 * @param {number} startTime - 开始时间
 * @param {number} processingTime - 处理时间
 */
function recordPerformanceTelemetry(context:My_Context, telemetryType:any, completion:any, startTime:number, processingTime:number) {
  const totalTime = Date.now() - startTime;
  const deltaTime = totalTime - processingTime;
  const extendedTelemetry = completion.telemetryData.extendedBy(
    {},
    {
      completionCharLen: completion.completionText.length,
      requestTimeMs: totalTime,
      processingTimeMs: processingTime,
      deltaMs: deltaTime,
      meanLogProb: completion.meanLogProb || NaN,
      meanAlternativeLogProb: completion.meanAlternativeLogProb || NaN,
      numTokens: completion.numTokens,
    }
  );
  extendedTelemetry.extendWithRequestId(completion.requestId);
  //telemetryManager.telemetry(context, `ghostText.${telemetryType}`, extendedTelemetry);
}

/**
 * 从缓存中检索幽灵文本建议
 * @param {string} promptKey - 提示键
 * @param {boolean} isMultiline - 是否为多行建议
 * @returns {Array|undefined} 返回缓存的建议数组或undefined
 */
function getCompletionChoicesFromCache(promptKey:string, isMultiline:boolean) {
  const cachedResult =completionCache.get(promptKey);
  if (cachedResult && (!isMultiline || cachedResult.multiline)){
    return cachedResult.choices;
  } 
}

/**
 * 获取幽灵文本建议的主要入口函数
 * @param context - 执行上下文
 * @param document - 文档对象
 * @param position - 光标位置
 * @param isCycling - 是否正在循环建议
 * @param telemetryData - 遥测数据
 * @param cancelToken - 取消令牌
 * @returns 返回幽灵文本建议结果
 */
import * as fs from 'fs';
import * as path from 'path';

export async function getGhostText(
   context: My_Context,
   document: Document,
   position: Position,
   isCycling: boolean,
   telemetryData:any,
   token: Token
): Promise<any> {
  var resultValue;
  var cyclingResults;
  // 提取提示信息  ！！！！！！重点关注，获取prompt
  logger.debug(context, "开始执行getGhostText.extractPrompt");
  const promptResult = await extractPrompt(context, document, position);

  logger.debug(context, "promptResult:",JSON.stringify(promptResult));

  // 保存 promptResult 和返回数据
  const saveData = async (data: any) => {
    const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace('T', '').split('.')[0];
    const fileName = `ghostText_${timestamp}.json`;
    const filePath = path.join('/Users/<USER>/Documents/workspace/cline/assets', fileName);
    
    await fs.promises.writeFile(filePath, JSON.stringify(data, null, 2));
    logger.debug(context, `Data saved to ${filePath}`);
  };

  // 在函数结束时保存数据
  const originalReturn = async (result: any) => {
    await saveData({
      promptResult,
      returnData: result
    });
    return result;
  };


  // 检查上下文是否足够
  if ("contextTooShort" === promptResult.type) {
    logger.debug(context, "Breaking, not enough context");
    return {
      type: "abortedBeforeIssued",
      reason: "Not enough context",
    };
  }
  // 检查是否取消请求
if (null == token ? undefined : token.isCancellationRequested) {
    logger.info(context, "Cancelled after extractPrompt");
    return {
      type: "abortedBeforeIssued",
      reason: "Cancelled after extractPrompt",
    };
  }
  /**
   * 检查光标是否在行中间 isMiddleOfLine
   * @param doc 文档对象
   * @param pos 光标位置
   * @returns 是否在行中间的有效位置
   */
  // 检查光标是否在行中间
  const isMiddleOfLine = (function (doc:Document, pos:Position) {
    const hasTrailingContent =
      ((cursorPos = pos), 0 != doc.lineAt(cursorPos).text.substr(cursorPos.character).trim().length);
    const isValidPosition = (function (pos, doc) {
      const trailingText = doc.lineAt(pos).text.substr(pos.character).trim();
      return /^\s*[)}\]"'`]*\s*[:{;,]?\s*$/.test(trailingText);
    })(pos, doc);
    var cursorPos;
    if (!hasTrailingContent || isValidPosition) return hasTrailingContent && isValidPosition;
  })(document, position);
   // 检查结果是否为undefined
  if (undefined === isMiddleOfLine) {
    logger.debug(context, "Breaking, invalid middle of the line");
    return {
      type: "abortedBeforeIssued",
      reason: "Invalid middle of the line",
    };
  }
  


  // 初始化必要的实例和信息
  const statusReporterInstance = context.get(StatusReporter);
  const locationFactoryInstance = context.get(LocationFactory);
  
  const blockModeResult = await (async function (context:My_Context, document: Document, position, promptResult, triggerKind, isMiddleOfLine) {
    const a = await context.get(BlockModeConfig).forLanguage(context, document.languageId);
    switch (a) {
      case BlockMode.Server:
        return {
          blockMode: BlockMode.Server,
          requestMultiline: true,
          isCyclingRequest: triggerKind,
          finishedCb: async () => {},
        };
      case BlockMode.Parsing:
      case BlockMode.ParsingAndServer:
      default: {
        const c = await (async function (context, document, position, isMiddleOfLine) {
          if (document.lineCount >= 8e3){
            //遥测
          }else {
            if (!isMiddleOfLine && isSupportedLanguageId(document.languageId))
              return await isEmptyBlockStart(document, position);
            if (isMiddleOfLine && isSupportedLanguageId(document.languageId))
              return (
                (await isEmptyBlockStart(document, position)) ||
                (await isEmptyBlockStart(document, document.lineAt(position).range.end))
              );
          }
          return false;
        })(context, document, position, isMiddleOfLine);
        return c
          ? {
              blockMode: a,
              requestMultiline: true,
              isCyclingRequest: false,
              finishedCb: async (prefix:string) => {
                let posi :Position;
                posi =
                promptResult.trailingWs.length > 0 &&
                  !promptResult.prompt.prefix.endsWith(promptResult.trailingWs)
                    ? context
                        .get(LocationFactory)
                        .position(
                          position.line,
                          Math.max(position.character - promptResult.trailingWs.length, 0)
                        )
                    : position;
                return isBlockBodyFinished(context, document, posi, prefix);
              },
            }
          : {
              blockMode: a,
              requestMultiline: false,
              isCyclingRequest: triggerKind,
              finishedCb: async () => {},
            };
      }
    }
  })(context, document, position, promptResult, isCycling, isMiddleOfLine);
  
  const [trimmedPrefix] = trimLastLine(document.getText(locationFactoryInstance.range(locationFactoryInstance.position(0, 0), position)));

  let V = (function (context:My_Context, prefix:string, promptContent:Prompt, isMultilineRequest) {
    const typingSuggestions = (function (context, prefix, promptContent) {
      if (lastProcessedPrefix ===undefined || !lastProcessedPromptKey || !prefix.startsWith(lastProcessedPrefix)){
        return;
      } 
      const cachedChoices = getCompletionChoicesFromCache(lastProcessedPromptKey, promptContent);
      if (!cachedChoices){
        return;
      }
        
      const remainingPrefix = prefix.substring(lastProcessedPrefix.length);
      logger.debug(
        context,
        `Getting completions for user-typing flow - remaining prefix: ${remainingPrefix}`
      );
      const filteredSuggestions:any = [];
      cachedChoices.forEach((choice:any) => {
        const processedChoice = processCompletionResult(choice, {
          forceSingleLine: false,
        });
        if (processedChoice.completionText.startsWith(remainingPrefix)) {
          processedChoice.completionText = processedChoice.completionText.substring(remainingPrefix.length);
          filteredSuggestions.push(processedChoice);
        }
      });
      return filteredSuggestions;
    })(context, prefix, isMultilineRequest);
    if (typingSuggestions && typingSuggestions.length > 0){
      return [typingSuggestions, ResultType.TypingAsSuggested];
    } 

    const cachedResults = (function (context, prefix, promptContent, isMultiline) {
      const cacheKey = keyForPrompt(promptContent);
      logger.debug(
        context,
        `Trying to get completions from cache for key: ${cacheKey}`
      );
      const cachedChoices = getCompletionChoicesFromCache(cacheKey, isMultiline);
      if (cachedChoices) {
        logger.debug(
          context,
          `Got completions from cache for key: ${cacheKey}`
        );
        const processedChoices:any = [];
        cachedChoices.forEach((choice:any) => {
          const processedChoice = processCompletionResult(choice, {
            forceSingleLine: !isMultiline,
          });
          processedChoices.push(processedChoice);
        });
        const validChoices = processedChoices.filter((choice:any) => choice.completionText);
        if (validChoices.length > 0) {
          updateLastProcessedInfo(prefix, cacheKey);
        }
        return validChoices;
      }
    })(context, prefix, promptContent, isMultilineRequest);
    return cachedResults && cachedResults.length > 0 ? [cachedResults, ResultType.Cache] : undefined;
  })(context, trimmedPrefix, promptResult.prompt as Prompt , blockModeResult.requestMultiline);


    // 检查是否取消请求
    if (null == token ? undefined : token.isCancellationRequested) {
      logger.info(context, "Cancelled after extractPrompt");
      return {
        type: "abortedBeforeIssued",
        reason: "Cancelled after extractPrompt",
      };
    }

    
  const requestId =uuidv4();
  const repoInfo = "";//repoManager.extractRepoInfoInBackground(context, document.fileName);
  // 获取引擎URL
  const engineUrl = "http://11.152.70.129/completion/autocompletion"

  // 获取请求参数
  const beforeRequestWaitMs = await context.get(Features).beforeRequestWaitMs("", document.languageId);
  const multiLogitBiasValue = await context.get(Features).multiLogitBias("", document.languageId);
  const requestParams = {
    blockMode: blockModeResult.blockMode,
    languageId: document.languageId,
    repoInfo: repoInfo,
    engineURL: engineUrl,
    ourRequestId: requestId,
    prefix: trimmedPrefix,
    prompt: promptResult.prompt,
    multiline: blockModeResult.requestMultiline,
    indentation: contextIndentation(document, position),
    isCycling: isCycling,
    delayMs: beforeRequestWaitMs,
    multiLogitBias: multiLogitBiasValue,
  };
  // 检查是否应该使用去抖动和上下文过滤
  const shouldDebouncePredict = await context.get(Features).debouncePredict();
  const shouldUseContextualFilter = await context.get(Features).contextualFilterEnable();
  const contextualFilterThreshold = await context.get(Features).contextualFilterAcceptThreshold();
  let useContextualFiltering = false;
  if (shouldDebouncePredict || shouldUseContextualFilter) {
    useContextualFiltering = true;
  }

 //遥测
  const re = (function (e, t, n, r, o, i, s) {
   return {}as any
  })(context, document, requestParams, position, promptResult, telemetryData, useContextualFiltering);

  const hasLocalSuggestions = (blockModeResult.isCyclingRequest && V?.[0]?.length > 1) ||
  (!blockModeResult.isCyclingRequest && V != null);
  if (hasLocalSuggestions){
    logger.info(context, "Found inline suggestions locally");
  }
  else {
    if (null == statusReporterInstance) {
      statusReporterInstance.setProgress();
    }
    if (blockModeResult.isCyclingRequest) {
      const fetchRes = await (async function (context:My_Context, requestParams:any, re:any, token:Token, finishedCb) {
        return fetchGhostTextFromNetwork(context, requestParams, re, token, finishedCb, "all completions", async (completionCount:number, startTime:number, processingTime:number, choices:any[]) => {
          const l = [];
          for await (const choice of choices) {
            if (null == token ? void 0 : token.isCancellationRequested)
              return (
                logger.debug(
                  context,
                  "Cancelled after awaiting choices iterator"
                ),
                {
                  type: "canceled",
                  reason: "after awaiting choices iterator",
                  telemetryData: undefined
                }
              );
            if (choice.completionText.trimEnd()) {
              if (
                -1 !==
                l.findIndex(
                  (e) => e.completionText.trim() === choice.completionText.trim()
                )
              )
                continue;
              l.push(choice);
            }
          }
          return (
            l.length > 0 &&
              (appendToCompletionCache(context, requestParams, {
                multiline: requestParams.multiline,
                choices: l,
              }),
              recordPerformanceTelemetry(context, "cyclingPerformance", l[0], startTime, processingTime)),
            {
              type: "success",
              value: l,
              telemetryData: undefined,
              telemetryBlob: re,
            }
          );
        });
      })(context, requestParams, re, token, blockModeResult.finishedCb);
      if ("success" === fetchRes.type) {
        const existingResults = V?.[0] ?? [];

          // 遍历 fetchRes.value,添加不重复的结果
        fetchRes.value.forEach((newResult:any) => {
          const isDuplicate = existingResults.some(
            (existing:any) => existing.completionText.trim() === newResult.completionText.trim()
          );
          
          if (!isDuplicate) {
            existingResults.push(newResult);
          }
        });
          (V = [existingResults, ResultType.Cycling]);
      } else if (void 0 === V) {
        return null == statusReporterInstance || statusReporterInstance.removeProgress(), fetchRes;
      }
    } else {
      const n = await debounceManager.getDebounceLimit(context, re);
      try {
        await ghostTextDebouncer.debounce(n);
      } catch {
        return {
          type: "canceled",
          reason: "by debouncer",
          telemetryData: undefined,
        };
      }
      if (null == token ? void 0 : token.isCancellationRequested){
        return (
          logger.info(context, "Cancelled during debounce"),
          {
            type: "canceled",
            reason: "during debounce",
            telemetryData: undefined,
          }
        );
      }
      if (
        shouldUseContextualFilter &&
        re?.measurements?.contextualFilterScore &&
        re?.measurements?.contextualFilterScore < contextualFilterThreshold / 100
      ){
        return (
          logger.info(context, "Cancelled by contextual filter"),
          {
            type: "canceled",
            reason: "contextualFilterScore below threshold",
            telemetryData: undefined,
          }
        );
      }
     
      const r = await (async function (context:My_Context, requestParams, re, token:Token, finishedCb) {

        return fetchGhostTextFromNetwork(context, requestParams, re, token, finishedCb, "completions", async (completionCount: number, startTime:number, processingTime :number , choices:any) => {
          // const u = choices[Symbol.asyncIterator](),
          //   d = await u.next();
          // if (d.done){
          //   return (
          //     logger.debug(context, "All choices redacted"),
          //     {
          //       type: "empty",
          //       reason: "all choices redacted",
          //       telemetryData: undefined,
          //     }
          //   );
          // }
          // if (null == token ? void 0 : token.isCancellationRequested)
          //   return (
          //     logger.debug(
          //       context,
          //       "Cancelled after awaiting redactedChoices iterator"
          //     ),
          //     {
          //       type: "canceled",
          //       reason: "after awaiting redactedChoices iterator",
          //       telemetryData: undefined,
          //     }
          //   );
          // const p = d.value;
          // if (void 0 === p)
          //   return (
          //     logger.debug(
          //       context,
          //       "Got undefined choice from redactedChoices iterator"
          //     ),
          //     {
          //       type: "empty",
          //       reason: "got undefined choice from redactedChoices iterator",
          //       telemetryData: undefined,
          //     }
          //   );
          // recordPerformanceTelemetry(context, "performance", p, startTime, processingTime);
          // const h = completionCount - 1;
          // logger.debug(
          //   context,
          //   `Awaited first result, id:  ${p.choiceIndex}`
          // ),
          //   (function (context, requestParams, prompt) {
          //     const sha256prompt = keyForPrompt(requestParams.prompt);
          //     updateLastProcessedInfo(requestParams.prefix, sha256prompt),
          //       exports.completionCache.put(sha256prompt, prompt),
          //       logger.debug(
          //         context,
          //         `Cached ghost text for key: ${sha256prompt}, multiline: ${prompt.multiline}, number of suggestions: ${prompt.choices.length}`
          //       );
          //   })(context, requestParams, {
          //     multiline: requestParams.multiline,
          //     choices: [p],
          //   });
          // const f = [];
          // for (let e = 0; e < h; e++) {
          //   f.push(u.next());
          // }
          // const m = Promise.all(f).then((r) => {
          //   logger.debug(
          //     context,
          //     `Awaited remaining results, number of results: ${r.length}`
          //   );
          //   const o = [];
          //   for (const n of r) {
          //     const r = n.value;
          //     if (
          //       void 0 !== r &&
          //       (logger.info(
          //         context,
          //         `GhostText later completion: [${r.completionText}]`
          //       ),
          //       r.completionText.trimEnd())
          //     ) {
          //       if (
          //         -1 !==
          //         o.findIndex(
          //           (e) => e.completionText.trim() === r.completionText.trim()
          //         )
          //       )
          //         continue;
          //       if (r.completionText.trim() === p.completionText.trim())
          //         continue;
          //       o.push(r);
          //     }
          //   }
          //   o.length > 0 &&
          //   appendToCompletionCache(context, requestParams, {
          //       multiline: requestParams.multiline,
          //       choices: o,
          //     });
          // });
          return (
            runtimeManager.isRunningInTest(context),// && (await m),
            {
              type: "success",
              value: processCompletionResult(choices, {
                forceSingleLine: !1,
              }),
              telemetryData: undefined,
              telemetryBlob: undefined,
            }
          );
        });
      })(context, requestParams, re, token, blockModeResult.finishedCb);
      if ("success" !== r.type){
        return null == statusReporterInstance || statusReporterInstance.removeProgress(), r;
      }
      V = [[r.value], ResultType.Network];
    }
    if (null == statusReporterInstance) {
      statusReporterInstance.removeProgress();
    }
  }
  if (undefined === V){
    return {
      type: "failed",
      reason: "internal error: choices should be defined after network call",
      telemetryData: undefined,
    };
  }
  
    const [suggestions, resultType] = V;
    // const processedSuggestions = asyncOps.asyncIterableMapFilter(
    //   asyncOps.asyncIterableFromArray(suggestions), 
    //   async (suggestion: any) => postProcessor.postProcessChoice(
    //     context, 
    //     "ghostText", 
    //     document, 
    //     position, 
    //     suggestion, 
    //     isMiddleOfLine, 
    //     logger
    //   )
    // );
    const finalSuggestions = [];
    // for await (const suggestion of processedSuggestions) {
    //   const coversSuffix = isMiddleOfLine && postProcessor.checkSuffix(document, position, suggestion);
    //   if (null == token ? undefined : token.isCancellationRequested) {
    //     logger.info(context, "Cancelled after post processing completions");
    //     return {
    //       type: "canceled",
    //       reason: "after post processing completions",
    //       telemetryData: undefined,
    //     };
    //   }
    //   const suggestionTelemetry = extendTelemetryData(context, suggestion);
    //   const processedSuggestion = {
    //     completion: processCompletionDisplay(suggestion.choiceIndex, suggestion.completionText, promptResult.trailingWs),
    //     telemetry: suggestionTelemetry,
    //     isMiddleOfTheLine: isMiddleOfLine,
    //     coversSuffix: coversSuffix,
    //   };
    //   finalSuggestions.push(processedSuggestion);
    // }
    const coversSuffix = isMiddleOfLine && postProcessor.checkSuffix(document, position, suggestions[0]);
    const processedSuggestion = {
      completion: processCompletionDisplay(suggestions[0].choiceIndex, suggestions[0].completionText, promptResult.trailingWs),
      telemetry: undefined,
      isMiddleOfTheLine: isMiddleOfLine,
      coversSuffix: coversSuffix,
    };
    finalSuggestions.push(processedSuggestion);
  const finalResult = {
    type: "success",
    value: [finalSuggestions, resultType],
    telemetryData: undefined,
    telemetryBlob: undefined,
  };

  return originalReturn(finalResult);
};
