/**
 * 6722  状态报告器模块 statusReporter
 * 
 * 该模块定义了状态报告器的接口和一个无操作的实现。
 * 主要用于在应用程序中报告进度、警告和错误，但在某些情况下可能不需要实际执行任何操作。
 */

/**
 * 状态报告器接口
 */
export abstract class StatusReporter {
  // 接口定义，具体实现由子类提供
  abstract setProgress: (progress: number) => void;
  abstract removeProgress: () => void;
  abstract setWarning: (message: string) => void;
  abstract setError: (e: Error) => void;
  abstract forceNormal: () => void;
}

/**
 * 无操作状态报告器
 * 实现 StatusReporter 接口，但所有方法都是空实现
 */
export class NoOpStatusReporter implements StatusReporter {
  /**
   * 设置进度（空实现）
   * @param progress - 进度值
   */
  setProgress(progress: number): void {}

  /**
   * 移除进度（空实现）
   */
  removeProgress(): void {}

  /**
   * 设置警告（空实现）
   * @param message - 警告消息
   */
  setWarning(message: string): void {}

  /**
   * 设置错误（空实现）
   * @param e - 错误对象
   */
  setError(e: Error): void {}

  /**
   * 强制正常状态（空实现）
   */
  forceNormal(): void {}
}
