/**
 * 6881.ts
 * 
 * 这个文件定义了一个 ChangeTracker 类，用于跟踪文档中特定位置的变化。
 * 主要功能包括：
 * 1. 监听文档变化事件，并相应地更新内部偏移量。
 * 2. 提供一个机制来推送延迟执行的操作。
 * 3. 自动管理资源释放，当没有待处理的操作时释放监听器。
 * 
 * 这个类在处理需要跟踪文档变化的场景中非常有用，例如在代码补全或者
 * 其他需要精确定位的功能中。
 */

import { TextDocumentManager } from './textDocumentUtils';
import { My_Context,Uri,TextDocumentChangeEvent} from './contextType';
// 定义 ChangeTracker 类
export class ChangeTracker {
  private _referenceCount: number = 0;
  private _isDisposed: boolean = false;
  private _offset: number;
  private _tracker: { dispose: () => void };

  constructor(context: My_Context, uri: Uri, initialOffset: number) {
    this._offset = initialOffset;
    const textDocumentManager = context.get(TextDocumentManager);
    
    // 监听文档变化事件
    this._tracker = textDocumentManager.onDidChangeTextDocument(async (event: TextDocumentChangeEvent) => {
      if (event.document.uri === uri) {
        for (const change of event.contentChanges) {
          if (change.rangeOffset + change.rangeLength <= this.offset) {
            // 更新偏移量
            const offsetDelta = change.text.length - change.rangeLength;
            this._offset = this._offset + offsetDelta;
          }
        }
      }
    });
  }

  // 获取当前偏移量
  get offset(): number {
    return this._offset;
  }

  // 推送新的操作
  push(action: () => void, delay: number): void {
    if (this._isDisposed) {
      throw new Error("无法向已释放的 ChangeTracker 推送新操作");
    }

    this._referenceCount++;

    setTimeout(() => {
      action();
      this._referenceCount--;

      // 如果没有待处理的操作，释放资源
      if (this._referenceCount === 0) {
        this._tracker.dispose();
        this._isDisposed = true;
      }
    }, delay);
  }
}
