import parseUrl from './9266';

interface ParsedUrl {
  token?: string;
  resource: string;
  pathname?: string;
  href: string;
  user?: string;
  name: string;
  owner: string;
  organization?: string;
  source: string;
  git_suffix?: boolean;
  full_name?: string;
  filepath?: string;
  filepathtype?: string;
  ref?: string;
  commit?: string;
  protocols?: string[];
  protocol?: string;
  port?: string;
  query?: {
    path?: string;
    version?: string;
    at?: string;
  };
  toString: (protocol?: string) => string;
}

export function parseGitUrl(url: string): ParsedUrl {
  if (typeof url !== "string") {
    throw new Error("The url must be a string.");
  }

  if (/^([a-z\d-]{1,39})\/([-\.\w]{1,100})$/i.test(url)) {
    url = "https://github.com/" + url;
  }

  const parsedUrl = parseUrl(url) as ParsedUrl;
  const domainParts = parsedUrl.resource.split(".");
  let pathParts: string[] | null = null;

  parsedUrl.toString = function(protocol?: string) {
    return stringifyGitUrl(this, protocol);
  };

  parsedUrl.source = domainParts.length > 2 ? domainParts.slice(1 - domainParts.length).join(".") : parsedUrl.resource;
  parsedUrl.git_suffix = /\.git$/.test(parsedUrl.pathname || '');
  parsedUrl.name = decodeURIComponent((parsedUrl.pathname || parsedUrl.href).replace(/(^\/)|(\/$)/g, "").replace(/\.git$/, ""));
  parsedUrl.owner = decodeURIComponent(parsedUrl.user || '');
  parsedUrl.user = parsedUrl.user || '';

  switch (parsedUrl.source) {
    case "git.cloudforge.com":
      parsedUrl.owner = parsedUrl.user;
      parsedUrl.organization = domainParts[0];
      parsedUrl.source = "cloudforge.com";
      break;

    case "visualstudio.com":
      handleVisualStudioUrl(parsedUrl);
      break;

    case "dev.azure.com":
    case "azure.com":
      handleAzureUrl(parsedUrl);
      break;

    default:
      handleDefaultUrl(parsedUrl);
  }

  handleBitbucketServer(parsedUrl);

  return parsedUrl;
}

function handleVisualStudioUrl(parsedUrl: ParsedUrl): void {
  if (parsedUrl.resource === "vs-ssh.visualstudio.com") {
    const pathParts = parsedUrl.name.split("/");
    if (pathParts.length === 4) {
      parsedUrl.organization = pathParts[1];
      parsedUrl.owner = pathParts[2];
      parsedUrl.name = pathParts[3];
      parsedUrl.full_name = `${pathParts[2]}/${pathParts[3]}`;
    }
  } else {
    const pathParts = parsedUrl.name.split("/");
    if (pathParts.length === 2) {
      parsedUrl.owner = pathParts[1];
      parsedUrl.name = pathParts[1];
      parsedUrl.full_name = `_git/${parsedUrl.name}`;
    } else if (pathParts.length === 3) {
      parsedUrl.name = pathParts[2];
      if (pathParts[0] === "DefaultCollection") {
        parsedUrl.owner = pathParts[2];
        parsedUrl.organization = pathParts[0];
        parsedUrl.full_name = `${parsedUrl.organization}/_git/${parsedUrl.name}`;
      } else {
        parsedUrl.owner = pathParts[0];
        parsedUrl.full_name = `${parsedUrl.owner}/_git/${parsedUrl.name}`;
      }
    } else if (pathParts.length === 4) {
      parsedUrl.organization = pathParts[0];
      parsedUrl.owner = pathParts[1];
      parsedUrl.name = pathParts[3];
      parsedUrl.full_name = `${parsedUrl.organization}/${parsedUrl.owner}/_git/${parsedUrl.name}`;
    }
  }
}

function handleAzureUrl(parsedUrl: ParsedUrl): void {
  if (parsedUrl.resource === "ssh.dev.azure.com") {
    const pathParts = parsedUrl.name.split("/");
    if (pathParts.length === 4) {
      parsedUrl.organization = pathParts[1];
      parsedUrl.owner = pathParts[2];
      parsedUrl.name = pathParts[3];
    }
  } else {
    const pathParts = parsedUrl.name.split("/");
    if (pathParts.length === 5) {
      parsedUrl.organization = pathParts[0];
      parsedUrl.owner = pathParts[1];
      parsedUrl.name = pathParts[4];
      parsedUrl.full_name = `_git/${parsedUrl.name}`;
    } else if (pathParts.length === 3) {
      parsedUrl.name = pathParts[2];
      if (pathParts[0] === "DefaultCollection") {
        parsedUrl.owner = pathParts[2];
        parsedUrl.organization = pathParts[0];
        parsedUrl.full_name = `${parsedUrl.organization}/_git/${parsedUrl.name}`;
      } else {
        parsedUrl.owner = pathParts[0];
        parsedUrl.full_name = `${parsedUrl.owner}/_git/${parsedUrl.name}`;
      }
    } else if (pathParts.length === 4) {
      parsedUrl.organization = pathParts[0];
      parsedUrl.owner = pathParts[1];
      parsedUrl.name = pathParts[3];
      parsedUrl.full_name = `${parsedUrl.organization}/${parsedUrl.owner}/_git/${parsedUrl.name}`;
    }
  }

  if (parsedUrl.query && parsedUrl.query.path) {
    parsedUrl.filepath = parsedUrl.query.path.replace(/^\/+/g, "");
  }
  if (parsedUrl.query && parsedUrl.query.version) {
    parsedUrl.ref = parsedUrl.query.version.replace(/^GB/, "");
  }
}

function handleDefaultUrl(parsedUrl: ParsedUrl): void {
  const pathParts = parsedUrl.name.split("/");
  let nameIndex = pathParts.length - 1;

  if (pathParts.length >= 2) {
    const specialParts = ["blob", "tree", "commit", "src", "raw", "edit"];
    const specialIndex = specialParts.reduce((index, part) => {
      const partIndex = pathParts.indexOf(part, 2);
      return partIndex > 0 && partIndex < index ? partIndex : index;
    }, pathParts.indexOf("-", 2));

    nameIndex = specialIndex > 0 ? specialIndex - 1 : nameIndex;
    parsedUrl.owner = pathParts.slice(0, nameIndex).join("/");
    parsedUrl.name = pathParts[nameIndex];

    if (pathParts.indexOf("commit") > 0) {
      parsedUrl.commit = pathParts[nameIndex + 2];
    }
  }

  parsedUrl.ref = "";
  parsedUrl.filepathtype = "";
  parsedUrl.filepath = "";

  const filePathStartIndex = pathParts.length > nameIndex && pathParts[nameIndex + 1] === "-" ? nameIndex + 1 : nameIndex;

  if (pathParts.length > filePathStartIndex + 2 && ["raw", "src", "blob", "tree", "edit"].includes(pathParts[filePathStartIndex + 1])) {
    parsedUrl.filepathtype = pathParts[filePathStartIndex + 1];
    parsedUrl.ref = pathParts[filePathStartIndex + 2];
    if (pathParts.length > filePathStartIndex + 3) {
      parsedUrl.filepath = pathParts.slice(filePathStartIndex + 3).join("/");
    }
  }

  parsedUrl.organization = parsedUrl.owner;
}

function handleBitbucketServer(parsedUrl: ParsedUrl): void {
  if (parsedUrl.full_name) {
    parsedUrl.full_name = parsedUrl.owner;
    if (parsedUrl.name) {
      if (parsedUrl.full_name) {
        parsedUrl.full_name += "/";
      }
      parsedUrl.full_name += parsedUrl.name;
    }
  }

  if (parsedUrl.owner.startsWith("scm/")) {
    parsedUrl.source = "bitbucket-server";
    parsedUrl.owner = parsedUrl.owner.replace("scm/", "");
    parsedUrl.organization = parsedUrl.owner;
    parsedUrl.full_name = `${parsedUrl.owner}/${parsedUrl.name}`;
  }

  const bitbucketMatch = /(projects|users)\/(.*?)\/repos\/(.*?)((\/.*$)|$)/.exec(parsedUrl.pathname || '');
  if (bitbucketMatch) {
    parsedUrl.source = "bitbucket-server";
    parsedUrl.owner = bitbucketMatch[1] === "users" ? `~${bitbucketMatch[2]}` : bitbucketMatch[2];
    parsedUrl.organization = parsedUrl.owner;
    parsedUrl.name = bitbucketMatch[3];

    const remainingPath = bitbucketMatch[4].split("/");
    if (remainingPath.length > 1) {
      if (["raw", "browse"].includes(remainingPath[1])) {
        parsedUrl.filepathtype = remainingPath[1];
        if (remainingPath.length > 2) {
          parsedUrl.filepath = remainingPath.slice(2).join("/");
        }
      } else if (remainingPath[1] === "commits" && remainingPath.length > 2) {
        parsedUrl.commit = remainingPath[2];
      }
    }

    parsedUrl.full_name = `${parsedUrl.owner}/${parsedUrl.name}`;
    parsedUrl.ref = parsedUrl.query && parsedUrl.query.at ? parsedUrl.query.at : "";
  }
}

function stringifyGitUrl(parsedUrl: ParsedUrl, protocol?: string): string {
  protocol = protocol || (parsedUrl.protocols && parsedUrl.protocols.length ? parsedUrl.protocols.join("+") : parsedUrl.protocol);
  const port = parsedUrl.port ? `:${parsedUrl.port}` : "";
  const user = parsedUrl.user || "git";
  const gitSuffix = parsedUrl.git_suffix ? ".git" : "";

  switch (protocol) {
    case "ssh":
      return port
        ? `ssh://${user}@${parsedUrl.resource}${port}/${parsedUrl.full_name}${gitSuffix}`
        : `${user}@${parsedUrl.resource}:${parsedUrl.full_name}${gitSuffix}`;
    case "git+ssh":
    case "ssh+git":
    case "ftp":
    case "ftps":
      return `${protocol}://${user}@${parsedUrl.resource}${port}/${parsedUrl.full_name}${gitSuffix}`;
    case "http":
    case "https":
      const auth = parsedUrl.token
        ? (parsedUrl.source === "bitbucket.org" ? `x-token-auth:${parsedUrl.token}@` : `${parsedUrl.token}@`)
        : (parsedUrl.user && (parsedUrl.protocols?.includes("http") || parsedUrl.protocols?.includes("https"))
          ? `${parsedUrl.user}@`
          : "");
      const bitbucketPath = parsedUrl.source === "bitbucket-server" ? `scm/${parsedUrl.full_name}` : parsedUrl.full_name;
      return `${protocol}://${auth}${parsedUrl.resource}${port}/${bitbucketPath}${gitSuffix}`;
    default:
      return parsedUrl.href;
  }
}

