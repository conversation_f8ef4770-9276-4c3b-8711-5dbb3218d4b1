/**
 * @file 7017.ts
 * @description 此模块处理代码完成后的任务，包括代码捕获、遥测数据收集和处理。
 * 
 * 主要功能：
 * 1. 捕获代码并提取提示信息
 * 2. 计算编辑距离和相关度量
 * 3. 处理代码完成被拒绝后的任务
 * 4. 处理代码完成被接受后的任务
 * 
 * 该模块与GitHub Copilot的代码完成功能密切相关，用于收集和分析用户交互数据，
 * 以及处理代码完成后的各种场景，包括代码被接受或拒绝的情况。
 */

import * as changeTracker from './changeTracker';
//import * as telemetryUtils from './telemetryResultUtils';
import * as logManager from './loggerManager';
import * as indentationUtils from './indentationUtils';
import * as promptExtractor from './promptExtractor';
import * as editDistanceUtils from './editDistanceUtils';
//import * as telemetryReporter from './telemetryManager';
import * as textDocumentManager from './textDocumentUtils';
import {My_Context} from './contextType'
// 定义日志记录器
const logger = new logManager.Logger(logManager.LogLevel.INFO, "post-insertion");

// 定义时间间隔配置
interface TimeInterval {
  seconds: number;
  captureCode: boolean;
  captureRejection: boolean;
}

const timeIntervals: TimeInterval[] = [
  { seconds: 15, captureCode: false, captureRejection: false },
  { seconds: 30, captureCode: true, captureRejection: true },
  { seconds: 120, captureCode: false, captureRejection: false },
  { seconds: 300, captureCode: false, captureRejection: false },
  { seconds: 600, captureCode: false, captureRejection: false },
];

// 定义提示信息接口
interface Prompt {
  prefix: string;
  suffix: string;
  isFimEnabled: boolean;
  promptElementRanges: any[];
}

// 定义捕获代码结果接口
interface CaptureCodeResult {
  prompt: Prompt;
  capturedCode: string;
  terminationOffset: number;
}

/**
 * 捕获代码并提取提示信息
 * @param context - 上下文对象
 * @param documentUri - 文档URI
 * @param insertionOffset - 插入偏移量
 * @returns 包含提示、捕获的代码和终止偏移量的对象
 */
async function captureCode(context: any, documentUri: any, insertionOffset: number): Promise<CaptureCodeResult> {
  const document = await context.get(textDocumentManager.TextDocumentManager).getTextDocument(documentUri);
  if (!document) {
    logger.info(
      context,
      `Could not get document for ${documentUri.fsPath}. Maybe it was closed by the editor.`
    );
    return {
      prompt: {
        prefix: "",
        suffix: "",
        isFimEnabled: false,
        promptElementRanges: [],
      },
      capturedCode: "",
      terminationOffset: 0,
    };
  }

  const documentText = document.getText();
  const textBeforeOffset = documentText.substring(0, insertionOffset);
  const offsetPosition = document.positionAt(insertionOffset);
  const extractedPrompt = await promptExtractor.extractPrompt(context, document, offsetPosition);
  
  const prompt: Prompt =
    extractedPrompt.type === "prompt"
      ? extractedPrompt.prompt
      : {
          prefix: textBeforeOffset,
          suffix: "",
          isFimEnabled: false,
          promptElementRanges: [],
        };

  const textAfterOffset = documentText.substring(insertionOffset);
  const contextIndentation = indentationUtils.contextIndentationFromText(textBeforeOffset, insertionOffset, document.languageId);
  const isIndentationBlockFinished = indentationUtils.indentationBlockFinished(contextIndentation, undefined);
  const terminationOffset = await isIndentationBlockFinished(textAfterOffset);
  const captureEndOffset = Math.min(documentText.length, insertionOffset + (terminationOffset ? 2 * terminationOffset : 500));

  return {
    prompt: prompt,
    capturedCode: documentText.substring(insertionOffset, captureEndOffset),
    terminationOffset: terminationOffset !== undefined ? terminationOffset : -1,
  };
}

/**
 * 计算编辑距离和相关度量
 * @param documentText - 文档文本
 * @param completionText - 完成文本
 * @param contextWindowSize - 上下文窗口大小
 * @param offset - 偏移量
 * @returns 包含编辑距离和相关度量的对象
 */
function calculateEditDistance(documentText: string, completionText: string, contextWindowSize: number, offset: number) {
  const contextSubstring = documentText.substring(
    Math.max(0, offset - contextWindowSize),
    Math.min(documentText.length, offset + completionText.length + contextWindowSize)
  );
  const lexEditDistanceResult = editDistanceUtils.lexEditDistance(contextSubstring, completionText);
  const relativeLexEditDistance = lexEditDistanceResult.lexDistance / lexEditDistanceResult.needleLexLength;
  const { distance: charEditDistance } = editDistanceUtils.editDistance(
    Array.from(contextSubstring.substring(lexEditDistanceResult.startOffset, lexEditDistanceResult.endOffset)),
    Array.from(completionText)
  );

  return {
    relativeLexEditDistance: relativeLexEditDistance,
    charEditDistance: charEditDistance,
    completionLexLength: lexEditDistanceResult.needleLexLength,
    foundOffset: lexEditDistanceResult.startOffset + Math.max(0, offset - contextWindowSize),
    lexEditDistance: lexEditDistanceResult.lexDistance,
    stillInCodeHeuristic: relativeLexEditDistance <= 0.5 ? 1 : 0,
  };
}

export { captureCode };

/**
 * 处理代码完成被拒绝后的任务
 * @param context - 上下文对象
 * @param eventName - 事件名称
 * @param insertionOffset - 插入偏移量
 * @param documentUri - 文档URI
 * @param completions - 完成选项数组
 */
export function postRejectionTasks(context: My_Context, eventName: string, insertionOffset: number, documentUri: any, completions: any[]) {
  // completions.forEach(({ completionText, completionTelemetryData }) => {
  //   logger.debug(context, `${eventName}.rejected choiceIndex: ${completionTelemetryData.properties.choiceIndex}`);
  //   telemetryUtils.telemetryRejected(context, eventName, completionTelemetryData);
  // });

  const tracker = new changeTracker.ChangeTracker(context, documentUri, insertionOffset);

  timeIntervals.filter((interval) => interval.captureRejection).forEach((interval) => {
    tracker.push(async () => {
      logger.debug(context, `Original offset: ${insertionOffset}, Tracked offset: ${tracker.offset}`);
      const { completionTelemetryData } = completions[0];
      const { prompt: capturedPrompt, capturedCode, terminationOffset } = await captureCode(context, documentUri, tracker.offset);

      let promptData: any;
      if (capturedPrompt.isFimEnabled) {
        promptData = {
          hypotheticalPromptPrefixJson: JSON.stringify(capturedPrompt.prefix),
          hypotheticalPromptSuffixJson: JSON.stringify(capturedPrompt.suffix),
        };
      } else {
        promptData = {
          hypotheticalPromptJson: JSON.stringify(capturedPrompt.prefix),
        };
      }

      const extendedTelemetryData = completionTelemetryData.extendedBy(
        {
          ...promptData,
          capturedCodeJson: JSON.stringify(capturedCode),
        },
        {
          timeout: interval.seconds,
          insertionOffset: insertionOffset,
          trackedOffset: tracker.offset,
          terminationOffsetInCapturedCode: terminationOffset,
        }
      );

      logger.debug(
        context,
        `${eventName}.capturedAfterRejected choiceIndex: ${completionTelemetryData.properties.choiceIndex}`,
        extendedTelemetryData
      );
      //telemetryReporter.telemetry(context, eventName + ".capturedAfterRejected", extendedTelemetryData, true);
    }, 1000 * interval.seconds);
  });
}

/**
 * 处理代码完成被接受后的任务
 * @param context - 上下文对象
 * @param eventName - 事件名称
 * @param completionText - 完成文本
 * @param insertionOffset - 插入偏移量
 * @param documentUri - 文档URI
 * @param telemetryData - 遥测数据
 */
export async function postInsertionTasks(
  context: any,
  eventName: string,
  completionText: string,
  insertionOffset: number,
  documentUri: any,
  telemetryData: any
) {
  logger.debug(context, `${eventName}.accepted choiceIndex: ${telemetryData.properties.choiceIndex}`);
  //telemetryUtils.telemetryAccepted(context, eventName, telemetryData);

  const tracker = new changeTracker.ChangeTracker(context, documentUri, insertionOffset);
  const trimmedCompletion = completionText.trim();

  timeIntervals.forEach((interval) =>
    tracker.push(
      async () => {
        const document = await context.get(textDocumentManager.TextDocumentManager).getTextDocument(documentUri);
        if (document) {
          const documentText = document.getText();
          let editDistanceResult = calculateEditDistance(documentText, trimmedCompletion, 50, tracker.offset);
          if (editDistanceResult.stillInCodeHeuristic) {
            editDistanceResult = calculateEditDistance(documentText, trimmedCompletion, 1500, tracker.offset);
          }

          logger.debug(
            context,
            `stillInCode: ${
              editDistanceResult.stillInCodeHeuristic ? "Found" : "Not found"
            }! Completion '${trimmedCompletion}' in file ${
              documentUri.fsPath
            }. lexEditDistance fraction was ${
              editDistanceResult.relativeLexEditDistance
            }. Char edit distance was ${
              editDistanceResult.charEditDistance
            }. Inserted at ${insertionOffset}, tracked at ${tracker.offset}, found at ${
              editDistanceResult.foundOffset
            }. choiceIndex: ${telemetryData.properties.choiceIndex}`
          );

          const extendedTelemetryData = telemetryData
            .extendedBy(
              {},
              {
                timeout: interval.seconds,
                insertionOffset: insertionOffset,
                trackedOffset: tracker.offset,
              }
            )
            .extendedBy({}, editDistanceResult);

          //telemetryReporter.telemetry(context, eventName + ".stillInCode", extendedTelemetryData);

          if (interval.captureCode) {
            const { prompt: capturedPrompt, capturedCode, terminationOffset } = await captureCode(context, documentUri, tracker.offset);
            
            let promptData: any;
            if (capturedPrompt.isFimEnabled) {
              promptData = {
                hypotheticalPromptPrefixJson: JSON.stringify(capturedPrompt.prefix),
                hypotheticalPromptSuffixJson: JSON.stringify(capturedPrompt.suffix),
              };
            } else {
              promptData = {
                hypotheticalPromptJson: JSON.stringify(capturedPrompt.prefix),
              };
            }

            const captureExtendedTelemetryData = telemetryData.extendedBy(
              {
                ...promptData,
                capturedCodeJson: JSON.stringify(capturedCode),
              },
              {
                timeout: interval.seconds,
                insertionOffset: insertionOffset,
                trackedOffset: tracker.offset,
                terminationOffsetInCapturedCode: terminationOffset,
              }
            );

            logger.debug(
              context,
              `${eventName}.capturedAfterAccepted choiceIndex: ${telemetryData.properties.choiceIndex}`,
              captureExtendedTelemetryData
            );
           // telemetryReporter.telemetry(context, eventName + ".capturedAfterAccepted", captureExtendedTelemetryData, true);
          }
        }
      },
      1000 * interval.seconds
    )
  );
}
