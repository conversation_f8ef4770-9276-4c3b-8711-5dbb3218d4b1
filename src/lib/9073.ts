// 导入 './2337' 模块
import r from './2337';

/**
 * 检查给定的输入是否为 SSH 或 RSYNC 协议
 * @param t - 输入参数，可以是字符串数组或字符串
 * @returns 如果是 SSH 或 RSYNC 协议则返回 true，否则返回 false
 */
export default function e(t: string[] | string): boolean {
  // 如果输入是数组
  if (Array.isArray(t)) {
    return t.includes('ssh') || t.includes('rsync');
  }

  // 如果输入不是字符串，返回 false
  if (typeof t !== 'string') {
    return false;
  }

  // 使用导入的函数 r 处理输入字符串
  const n: any = r(t);

  // 提取协议后的部分
  t = t.substring(t.indexOf('://') + 3);

  // 递归检查处理后的数组
  if (e(n)) {
    return true;
  }

  // 定义正则表达式用于匹配特定格式
  const o: RegExp = new RegExp('.([a-zA-Z\\d]+):(\\d+)/');

  // 检查字符串格式
  return !t.match(o) && t.indexOf('@') < t.indexOf(':');
}
