/**
 * 9657
 * 文件名: repetitionDetector.ts
 * 
 * 主要功能：
 * 1. 定义重复过滤模式（RepetitionFilterMode）
 * 2. 实现KMP算法用于字符串匹配
 * 3. 提供重复序列检测功能
 * 
 * 该模块主要用于检测给定token序列是否存在重复，可用于文本生成或处理中的重复内容过滤。
 * 它使用KMP算法来高效地进行字符串匹配，并根据预定义的配置来判断序列是否重复。
 */

// 定义 RepetitionFilterMode 枚举
export enum RepetitionFilterMode {
  CLIENT = "client",
  PROXY = "proxy",
  BOTH = "both"
}

// 定义配置项接口
interface RepetitionConfig {
  max_token_sequence_length: number;
  last_tokens_to_consider: number;
}

// 定义配置数组
const repetitionConfigs: RepetitionConfig[] = [
  {
    max_token_sequence_length: 1,
    last_tokens_to_consider: 10,
  },
  {
    max_token_sequence_length: 10,
    last_tokens_to_consider: 30,
  },
  {
    max_token_sequence_length: 20,
    last_tokens_to_consider: 45,
  },
  {
    max_token_sequence_length: 30,
    last_tokens_to_consider: 60,
  },
];

/**
 * 计算 KMP 算法的失配函数
 * @param tokens 输入的 token 数组
 * @returns 失配函数数组
 */
function computeKMPFailureFunction(tokens: string[]): number[] {
  const failureFunction = Array(tokens.length).fill(0);
  failureFunction[0] = -1;
  let prefixEnd = -1;

  for (let i = 1; i < tokens.length; i++) {
    while (prefixEnd >= 0 && tokens[prefixEnd + 1] !== tokens[i]) {
      prefixEnd = failureFunction[prefixEnd];
    }
    if (tokens[prefixEnd + 1] === tokens[i]) {
      prefixEnd++;
    }
    failureFunction[i] = prefixEnd;
  }

  return failureFunction;
}

/**
 * 检查给定的 token 序列是否重复
 * @param tokens 输入的 token 数组
 * @returns 是否重复
 */
function isSequenceRepetitive(tokens: string[]): boolean {
  const failureFunction = computeKMPFailureFunction(tokens);

  for (const config of repetitionConfigs) {
    if (
      tokens.length >= config.last_tokens_to_consider &&
      config.last_tokens_to_consider - 1 - failureFunction[config.last_tokens_to_consider - 1] <=
        config.max_token_sequence_length
    ) {
      return true;
    }
  }

  return false;
}

/**
 * 检查给定的 token 序列是否重复
 * @param tokens 输入的 token 数组
 * @param mode 重复过滤模式
 * @returns 是否重复
 */
export function isRepetitive(tokens: string[], mode: RepetitionFilterMode = RepetitionFilterMode.CLIENT): boolean {
  if (mode === RepetitionFilterMode.PROXY) {
    return false;
  }

  return (
    isSequenceRepetitive(tokens.slice().reverse()) ||
    isSequenceRepetitive(tokens.slice().reverse().filter((token) => token.trim().length > 0))
  );
}
