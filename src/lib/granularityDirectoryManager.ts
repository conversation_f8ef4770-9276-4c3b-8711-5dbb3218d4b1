/**
 * 9030
 * 粒度目录管理模块
 * 
 * 该模块定义了 GranularityDirectory 类，用于管理和操作不同粒度的时间相关功能。
 * 主要功能包括：
 * 1. 管理不同过滤器设置的粒度规格
 * 2. 根据给定的过滤器设置选择合适的粒度
 * 3. 更新粒度设置，包括调用桶数和时间周期
 * 4. 扩展过滤器，生成新的过滤器设置和预取设置
 * 
 * 该模块在 Copilot 扩展中用于处理时间相关的粒度操作，支持灵活的时间周期和桶划分策略。
 */

import { Filter, FilterSettings } from './filterSettings';
import { TimeBucketGranularity, DEFAULT_GRANULARITY, GranularityImplementation } from './granularityImplementation';
import { Clock } from './clock';

// 定义 CopilotClientTimeBucket 类型
type CopilotClientTimeBucket = typeof Filter.CopilotClientTimeBucket;

export class GranularityDirectory {
  private specs: Map<FilterSettings, TimeBucketGranularity>;
  private prefix: string;
  private clock: Clock;
  private defaultGranularity: GranularityImplementation;

  constructor(prefix: string, clock: Clock) {
    this.specs = new Map();
    this.prefix = prefix;
    this.clock = clock;
    this.defaultGranularity = DEFAULT_GRANULARITY(prefix);
  }

  // 选择合适的粒度
  selectGranularity(filterSettings: FilterSettings): GranularityImplementation {
    for (const [key, value] of this.specs.entries()) {
      if (filterSettings.extends(key)) return value;
    }
    return this.defaultGranularity;
  }

  // 更新粒度设置
  update(filterSettings: FilterSettings, callBuckets: number, timePeriodHours: number): void {
    callBuckets = callBuckets > 1 ? callBuckets : NaN;
    timePeriodHours = timePeriodHours > 0 ? timePeriodHours : NaN;

    if (isNaN(callBuckets) && isNaN(timePeriodHours)) {
      this.specs.delete(filterSettings);
    } else {
      const granularity = new TimeBucketGranularity(this.prefix);
      if (!isNaN(callBuckets)) granularity.setByCallBuckets(callBuckets);
      if (!isNaN(timePeriodHours)) granularity.setTimePeriod(3600 * timePeriodHours * 1000);
      this.specs.set(filterSettings, granularity);
    }
  }

  // 扩展过滤器
  extendFilters(filterSettings: FilterSettings): {
    newFilterSettings: FilterSettings;
    otherFilterSettingsToPrefetch: FilterSettings[];
  } {
    const granularity = this.selectGranularity(filterSettings);
    const [currentValue, upcomingValues] = granularity.getCurrentAndUpComingValues(new Date(this.clock.now()));

    return {
      newFilterSettings: filterSettings.withChange(Filter.CopilotClientTimeBucket, currentValue),
      otherFilterSettingsToPrefetch: upcomingValues.map((value) =>
        filterSettings.withChange(Filter.CopilotClientTimeBucket, value)
      ),
    };
  }
}
