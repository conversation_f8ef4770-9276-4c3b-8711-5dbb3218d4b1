/**
 * 3055876
 * treeNodeTypes.ts
 * 
 * 这个文件定义了一个树形结构的数据模型，用于表示和操作树形数据。
 * 
 * 主要功能：
 * 1. 定义了不同类型的树节点（虚拟节点、行节点、空白节点和顶级节点）
 * 2. 提供了创建各种类型节点的函数
 * 3. 包含了用于判断节点类型的辅助函数
 * 4. 实现了树操作功能，如裁剪和复制树结构
 */

// 定义节点类型
type NodeType = 'virtual' | 'line' | 'blank' | 'top';

// 定义 Label 接口
export interface Label {
  totalLength: number;
  firstLineAfter: number;
}

// 定义基本节点接口
export interface BaseNode {
  type: NodeType;
  subs: Node[];
  label?: Label | any;
}

// 定义虚拟节点接口
export interface VirtualNode extends BaseNode {
  type: 'virtual';
  indentation: number;
}

// 定义行节点接口
export interface LineNode extends BaseNode {
  type: 'line';
  indentation: number;
  lineNumber: number;
  sourceLine: string;
}

// 定义空白节点接口
export interface BlankNode extends BaseNode {
  type: 'blank';
  lineNumber: number;
  
}

// 定义顶级节点接口
export interface TopNode extends BaseNode {
  type: 'top';
  indentation: number;
}

// 定义节点类型联合
export type Node = VirtualNode | LineNode | BlankNode | TopNode;

// 判断节点是否为虚拟节点
export function isVirtual(node: Node): node is VirtualNode {
  return node.type === "virtual";
}

// 判断节点是否为顶级节点
export function isTop(node: Node): node is TopNode {
  return node.type === "top";
}

// 判断节点是否为空白节点
export function isBlank(node: Node): node is BlankNode {
  return node.type === "blank";
}

// 判断节点是否为行节点
export function isLine(node: Node): node is LineNode {
  return node.type === "line";
}

// 创建虚拟节点
export function virtualNode(indentation: number, children: Node[], label?: any): VirtualNode {
  return {
    type: "virtual",
    indentation,
    subs: children,
    label,
  };
}

// 创建行节点
export function lineNode(indentation: number, lineNumber: number, sourceLine: string, children: Node[], label?: any): LineNode {
  if (sourceLine === "") {
    throw new Error("Cannot create a line node with an empty source line");
  }
  return {
    type: "line",
    indentation,
    lineNumber,
    sourceLine,
    subs: children,
    label,
  };
}

// 创建空白节点
export function blankNode(lineNumber: number): BlankNode {
  return {
    type: "blank",
    lineNumber,
    subs: [],
  };
}

// 创建顶级节点
export function topNode(children: Node[] = []): TopNode {
  return {
    type: "top",
    indentation: -1,
    subs: children,
  };
}


// 在指定行号后裁剪树
export function cutTreeAfterLine(root: Node, targetLineNumber: number): void {
  function cut(node: Node): boolean {
    if (!isVirtual(node) && !isTop(node) && 'lineNumber' in node && node.lineNumber === targetLineNumber) {
      node.subs = [];
      return true;
    }
    for (let i = 0; i < node.subs.length; i++) {
      if (cut(node.subs[i])) {
        node.subs = node.subs.slice(0, i + 1);
        return true;
      }
    }
    return false;
  }
  cut(root);
}

// 复制树结构
export function duplicateTree(e: Node): Node {
  return JSON.parse(JSON.stringify(e));
}
