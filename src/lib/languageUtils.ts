/**
 * 3055417
 * @file languageUtils.ts
 * @description 这个文件提供了与编程语言相关的实用工具函数。
 * 主要功能包括：
 * 1. 定义了各种编程语言的注释标记
 * 2. 提供了检测语言标记的函数
 * 3. 生成特定语言的注释
 * 4. 获取语言和路径标记
 * 
 * 这些工具函数主要用于处理不同编程语言的注释和标记，
 * 可能在代码分析、生成或处理多语言项目时非常有用。
 */

// 定义注释标记接口
interface CommentMarker {
  start: string;
  end: string;
}

// 定义语言注释标记的类型
type LanguageCommentMarkers = {
  [key: string]: CommentMarker;
};

// 导出语言注释标记
export const languageCommentMarkers: LanguageCommentMarkers = {
  abap: {
    start: '"',
    end: "",
  },
  bat: {
    start: "REM",
    end: "",
  },
  bibtex: {
    start: "%",
    end: "",
  },
  blade: {
    start: "#",
    end: "",
  },
  c: {
    start: "//",
    end: "",
  },
  clojure: {
    start: ";",
    end: "",
  },
  coffeescript: {
    start: "//",
    end: "",
  },
  cpp: {
    start: "//",
    end: "",
  },
  csharp: {
    start: "//",
    end: "",
  },
  css: {
    start: "/*",
    end: "*/",
  },
  dart: {
    start: "//",
    end: "",
  },
  dockerfile: {
    start: "#",
    end: "",
  },
  elixir: {
    start: "#",
    end: "",
  },
  erb: {
    start: "<%#",
    end: "%>",
  },
  erlang: {
    start: "%",
    end: "",
  },
  fsharp: {
    start: "//",
    end: "",
  },
  go: {
    start: "//",
    end: "",
  },
  groovy: {
    start: "//",
    end: "",
  },
  haml: {
    start: "-#",
    end: "",
  },
  handlebars: {
    start: "{{!",
    end: "}}",
  },
  haskell: {
    start: "--",
    end: "",
  },
  html: {
    start: "\x3c!--",
    end: "--\x3e",
  },
  ini: {
    start: ";",
    end: "",
  },
  java: {
    start: "//",
    end: "",
  },
  javascript: {
    start: "//",
    end: "",
  },
  javascriptreact: {
    start: "//",
    end: "",
  },
  jsonc: {
    start: "//",
    end: "",
  },
  jsx: {
    start: "//",
    end: "",
  },
  julia: {
    start: "#",
    end: "",
  },
  kotlin: {
    start: "//",
    end: "",
  },
  latex: {
    start: "%",
    end: "",
  },
  less: {
    start: "//",
    end: "",
  },
  lua: {
    start: "--",
    end: "",
  },
  makefile: {
    start: "#",
    end: "",
  },
  markdown: {
    start: "[]: #",
    end: "",
  },
  "objective-c": {
    start: "//",
    end: "",
  },
  "objective-cpp": {
    start: "//",
    end: "",
  },
  perl: {
    start: "#",
    end: "",
  },
  php: {
    start: "//",
    end: "",
  },
  powershell: {
    start: "#",
    end: "",
  },
  pug: {
    start: "//",
    end: "",
  },
  python: {
    start: "#",
    end: "",
  },
  ql: {
    start: "//",
    end: "",
  },
  r: {
    start: "#",
    end: "",
  },
  razor: {
    start: "\x3c!--",
    end: "--\x3e",
  },
  ruby: {
    start: "#",
    end: "",
  },
  rust: {
    start: "//",
    end: "",
  },
  sass: {
    start: "//",
    end: "",
  },
  scala: {
    start: "//",
    end: "",
  },
  scss: {
    start: "//",
    end: "",
  },
  shellscript: {
    start: "#",
    end: "",
  },
  slim: {
    start: "/",
    end: "",
  },
  solidity: {
    start: "//",
    end: "",
  },
  sql: {
    start: "--",
    end: "",
  },
  stylus: {
    start: "//",
    end: "",
  },
  svelte: {
    start: "\x3c!--",
    end: "--\x3e",
  },
  swift: {
    start: "//",
    end: "",
  },
  terraform: {
    start: "#",
    end: "",
  },
  tex: {
    start: "%",
    end: "",
  },
  typescript: {
    start: "//",
    end: "",
  },
  typescriptreact: {
    start: "//",
    end: "",
  },
  vb: {
    start: "'",
    end: "",
  },
  verilog: {
    start: "//",
    end: "",
  },
  "vue-html": {
    start: "\x3c!--",
    end: "--\x3e",
  },
  vue: {
    start: "//",
    end: "",
  },
  xml: {
    start: "\x3c!--",
    end: "--\x3e",
  },
  xsl: {
    start: "\x3c!--",
    end: "--\x3e",
  },
  yaml: {
    start: "#",
    end: "",
  },
};

// 被排除的语言列表
const excludedLanguages: string[] = ["php", "plaintext"];

// 特定语言的标记
const languageSpecificMarkers: { [key: string]: string } = {
  html: "<!DOCTYPE html>",
  python: "#!/usr/bin/env python3",
  ruby: "#!/usr/bin/env ruby",
  shellscript: "#!/bin/sh",
  yaml: "# YAML data",
};

// 定义文件信息接口
interface FileInfo {
  source: string;
  languageId: string;
  relativePath?: string;
}

/**
 * 检查源代码是否包含语言标记
 * @param source - 源代码
 * @returns 是否包含语言标记
 */
export function hasLanguageMarker({ source }: { source: string }): boolean {
  return source.startsWith("#!") || source.startsWith("<!DOCTYPE");
}

/**
 * 为指定语言生成注释
 * @param content - 注释内容
 * @param languageId - 语言ID
 * @returns 格式化的注释
 */
export function comment(content: string, languageId: string): string {
  const commentMarker = languageCommentMarkers[languageId];
  if (commentMarker) {
    const endMarker = commentMarker.end === "" ? "" : " " + commentMarker.end;
    return `${commentMarker.start} ${content}${endMarker}`;
  }
  return "";
}

/**
 * 获取语言标记
 * @param fileInfo - 文件信息对象
 * @returns 语言标记
 */
export function getLanguageMarker(fileInfo: FileInfo): string {
  const { languageId } = fileInfo;
  return excludedLanguages.indexOf(languageId) !== -1 || hasLanguageMarker(fileInfo)
    ? ""
    : languageId in languageSpecificMarkers
    ? languageSpecificMarkers[languageId]
    : comment(`Language: ${languageId}`, languageId);
}

/**
 * 获取路径标记
 * @param fileInfo - 文件信息对象
 * @returns 路径标记
 */
export function getPathMarker(fileInfo: FileInfo): string {
  return fileInfo.relativePath ? comment(`Path: ${fileInfo.relativePath}`, fileInfo.languageId) : "";
}
