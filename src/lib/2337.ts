/**
 * 从 URL 中提取协议
 * @param url - 输入的 URL，可以是字符串或 URL 对象
 * @param index - 可选参数，指定返回协议数组中的特定索引
 * @returns 如果指定了索引，返回特定的协议字符串；否则返回协议字符串数组
 */
function extractProtocol(url: string | URL, index?: number | boolean): string | string[] {
  // 如果 index 为 true，将其设置为 0
  if (index === true) {
    index = 0;
  }

  let protocol = "";

  // 处理字符串类型的 URL
  if (typeof url === "string") {
    try {
      protocol = new URL(url).protocol;
    } catch (error) {
      // URL 解析失败，保持 protocol 为空字符串
    }
  } 
  // 处理 URL 对象
  else if (url instanceof URL) {
    protocol = url.protocol;
  }

  // 分割协议字符串并过滤空值
  const protocolParts = protocol.split(/\s+/).filter(Boolean);

  // 根据 index 参数返回结果
  return typeof index === "number" ? protocolParts[index] : protocolParts;
}

export default extractProtocol;
