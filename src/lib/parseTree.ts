/**
 * 3055469
 * treeParser.ts
 * 
 * 本模块提供了解析和操作树结构的功能，特别适用于代码或文本分析。主要功能包括：
 * 
 * 1. 将原始文本解析为树结构
 * 2. 根据自定义规则为节点添加标签
 * 3. 合并开放和闭合节点
 * 4. 对节点进行分组和扁平化处理
 * 5. 处理特定语言的解析
 * 
 * 该模块设计灵活且可扩展，允许自定义解析规则和特定语言的实现。
 */

import * as treeNodeTypes from './treeNodeTypes';
import * as treeOperations from './treeOperations';

// 定义解析原始文本的函数
export function parseRaw(input: string): treeNodeTypes.TopNode {
  const lines: string[] = input.split("\n");
  const indentations: number[] = lines.map((line) => {
    const match = line.match(/^\s*/);
    return match ? match[0].length : 0;
  });
  const trimmedLines: string[] = lines.map((line) => line.trimLeft());

  // 解析单行
  function parseLine(lineIndex: number): [treeNodeTypes.LineNode, number] {
    const [subNodes, nextIndex] = parseSubtree(lineIndex + 1, indentations[lineIndex]);
    return [treeNodeTypes.lineNode(indentations[lineIndex], lineIndex, trimmedLines[lineIndex], subNodes), nextIndex];
  }

  // 解析子树
  function parseSubtree(startIndex: number, parentIndentation: number): [treeNodeTypes.Node[], number] {
    let currentLine: treeNodeTypes.LineNode;
    const nodes: treeNodeTypes.Node[] = [];
    let blankLineStart: number | undefined;
    let currentIndex: number = startIndex;

    while (currentIndex < trimmedLines.length && ("" === trimmedLines[currentIndex] || indentations[currentIndex] > parentIndentation)) {
      if ("" === trimmedLines[currentIndex]) {
        if (blankLineStart === undefined) {
          blankLineStart = currentIndex;
        }
        currentIndex += 1;
      } else {
        if (blankLineStart !== undefined) {
          for (let i = blankLineStart; i < currentIndex; i++) nodes.push(treeNodeTypes.blankNode(i));
          blankLineStart = undefined;
        }
        [currentLine, currentIndex] = parseLine(currentIndex);
        nodes.push(currentLine);
      }
    }

    if (blankLineStart !== undefined) {
      currentIndex = blankLineStart;
    }
    return [nodes, currentIndex];
  }

  const [rootNodes, endIndex] = parseSubtree(0, -1);
  let currentIndex: number = endIndex;

  while (currentIndex < trimmedLines.length && "" === trimmedLines[currentIndex]) {
    rootNodes.push(treeNodeTypes.blankNode(currentIndex));
    currentIndex += 1;
  }

  if (currentIndex < trimmedLines.length)
    throw new Error(
      `Parsing did not go to end of file. Ended at ${currentIndex} out of ${trimmedLines.length}`
    );

  return treeNodeTypes.topNode(rootNodes);
}

// 定义标签规则接口
interface LabelRule {
  matches: (line: string) => boolean;
  label: string;
}

// 为行添加标签
export function labelLines(node: treeNodeTypes.Node, rules: LabelRule[]): void {
  treeOperations.visitTree(
    node,
    function (currentNode: treeNodeTypes.Node) {
      // 检查当前节点是否为行节点
      if (treeNodeTypes.isLine(currentNode)) {
        // 查找匹配当前行的规则
        const matchingRule = rules.find((rule) => rule.matches(currentNode.sourceLine));
        // 如果找到匹配的规则，则为当前节点添加标签
        if (matchingRule) {
          currentNode.label = matchingRule.label;
        }
      }
    },
    "bottomUp"
  );
}

// 构建标签规则
export function buildLabelRules(ruleDefinitions: { [key: string]: RegExp | ((input: string) => boolean) }): LabelRule[] {
  return Object.keys(ruleDefinitions).map((label) => {
    let matchFunction: (input: string) => boolean;
    matchFunction = typeof ruleDefinitions[label] === 'object' ? 
      (input) => (ruleDefinitions[label] as RegExp).test(input) : 
      ruleDefinitions[label] as (input: string) => boolean;
    return {
      matches: matchFunction,
      label: label,
    };
  });
}

// 合并闭合和开放标签
export function combineClosersAndOpeners(rootNode: treeNodeTypes.Node): treeNodeTypes.Node {
  const newTree = treeOperations.rebuildTree(rootNode, function (node: treeNodeTypes.Node): treeNodeTypes.Node {
    if (
      0 === node.subs.length ||
      -1 ===
        node.subs.findIndex((sub) => "closer" === sub.label || "opener" === sub.label)
    )
      return node;

    const newSubs: treeNodeTypes.Node[] = [];
    let lastNonBlankNode: treeNodeTypes.Node | undefined;

    for (let i = 0; i < node.subs.length; i++) {
      const currentNode = node.subs[i];
      const previousNode = node.subs[i - 1];

      if ("opener" === currentNode.label && previousNode !== undefined && treeNodeTypes.isLine(previousNode)) {
        previousNode.subs.push(currentNode);
        currentNode.subs.forEach((sub) => previousNode.subs.push(sub));
        currentNode.subs = [];
      } else if (
        "closer" === currentNode.label &&
        lastNonBlankNode !== undefined &&
        (treeNodeTypes.isLine(currentNode) || treeNodeTypes.isVirtual(currentNode)) &&
        'indentation' in currentNode &&
        'indentation' in lastNonBlankNode &&
        currentNode.indentation >= lastNonBlankNode.indentation
      ) {
        let lastNonBlankIndex = newSubs.length - 1;
        while (lastNonBlankIndex > 0 && treeNodeTypes.isBlank(newSubs[lastNonBlankIndex])) lastNonBlankIndex -= 1;
        lastNonBlankNode.subs.push(...newSubs.splice(lastNonBlankIndex + 1));
        if (currentNode.subs.length > 0) {
          const newVirtualIndex = lastNonBlankNode.subs.findIndex((sub) => "newVirtual" !== sub.label),
            beforeNewVirtual = lastNonBlankNode.subs.slice(0, newVirtualIndex),
            afterNewVirtual = lastNonBlankNode.subs.slice(newVirtualIndex),
            newVirtualNode =
              afterNewVirtual.length > 0
                ? [treeNodeTypes.virtualNode(currentNode.indentation, afterNewVirtual, "newVirtual")]
                : [];
          lastNonBlankNode.subs = [...beforeNewVirtual, ...newVirtualNode, currentNode];
        } else lastNonBlankNode.subs.push(currentNode);
      } else {
        newSubs.push(currentNode);
      }
      if (treeNodeTypes.isBlank(currentNode)) {
        lastNonBlankNode = currentNode;
      }
    }
    node.subs = newSubs;
    return node;
  });

  treeOperations.clearLabelsIf(rootNode, (node) => "newVirtual" === node.label);
  return newTree;
}

// 为虚拟节点继承标签
export function labelVirtualInherited(node: treeNodeTypes.Node): void {
  treeOperations.visitTree(
    node,
    function (currentNode: treeNodeTypes.Node) {
      if (treeNodeTypes.isVirtual(currentNode) && currentNode.label === undefined) {
        const nonBlankSubs = currentNode.subs.filter((sub) => !treeNodeTypes.isBlank(sub));
        if (1 === nonBlankSubs.length) {
          currentNode.label = nonBlankSubs[0].label;
        }
      }
    },
    "bottomUp"
  );
}

// 分组块
export function groupBlocks(rootNode: treeNodeTypes.Node, isGroupNode: (node: treeNodeTypes.Node) => boolean = treeNodeTypes.isBlank, groupLabel?: string): treeNodeTypes.Node {
  return treeOperations.rebuildTree(rootNode, function (node: treeNodeTypes.Node): treeNodeTypes.Node {
    if (node.subs.length <= 1) return node;

    const newSubs: treeNodeTypes.Node[] = [];
    let groupIndentation: number | undefined;
    let currentGroup: treeNodeTypes.Node[] = [];
    let lastNodeWasGroup = false;

    function finalizeGroup(isLastGroup = false) {
      if (groupIndentation !== undefined && (newSubs.length > 0 || !isLastGroup)) {
        const groupNode = treeNodeTypes.virtualNode(groupIndentation, currentGroup, groupLabel);
        newSubs.push(groupNode);
      } else currentGroup.forEach((node) => newSubs.push(node));
    }

    for (let i = 0; i < node.subs.length; i++) {
      const currentNode = node.subs[i];
      const shouldGroup = isGroupNode(currentNode);
      if (!shouldGroup && lastNodeWasGroup) {
        finalizeGroup();
        currentGroup = [];
      }
      lastNodeWasGroup = shouldGroup;
      currentGroup.push(currentNode);
      if (treeNodeTypes.isBlank(currentNode) && 'indentation' in currentNode) {
        groupIndentation = groupIndentation != null ? groupIndentation : (currentNode.indentation as number);
      }
    }
    finalizeGroup(true);
    node.subs = newSubs;
    return node;
  });
}

// 扁平化虚拟节点
export function flattenVirtual(node: treeNodeTypes.Node): treeNodeTypes.Node {
  return treeOperations.rebuildTree(node, function (currentNode: treeNodeTypes.Node): treeNodeTypes.Node | undefined {
    if (treeNodeTypes.isVirtual(currentNode) && currentNode.label === undefined && currentNode.subs.length <= 1)
      return currentNode.subs.length === 0 ? undefined : currentNode.subs[0];
    
    if (
      currentNode.subs.length === 1 &&
      treeNodeTypes.isVirtual(currentNode.subs[0]) &&
      currentNode.subs[0].label === undefined
    ) {
      currentNode.subs = currentNode.subs[0].subs;
    }
    return currentNode;
  });
}

const defaultLabelRules: LabelRule[] = buildLabelRules({
  opener: /^[\[({]/,
  closer: /^[\])}]/,
});

const languageSpecificParsers: { [key: string]: (node: treeNodeTypes.Node) => treeNodeTypes.Node } = {};

// 注册特定语言的解析器
export function registerLanguageSpecificParser(language: string, parser: (node: treeNodeTypes.Node) => treeNodeTypes.Node): void {
  languageSpecificParsers[language] = parser;
}

// 解析树
export function parseTree(input: string, language?: string): treeNodeTypes.Node {
  const parsedTree = parseRaw(input);
  const languageSpecificParser = languageSpecificParsers[language ?? ""];
  return languageSpecificParser ? languageSpecificParser(parsedTree) : (labelLines(parsedTree, defaultLabelRules), combineClosersAndOpeners(parsedTree));
}
