/**
 * id： 3197
 * Ghost Text Provider Module
 * 
 * 这个模块实现了VS Code的幽灵文本（Ghost Text）功能，主要用于提供内联代码完成建议。
 * 
 * 主要功能：
 * 1. 提供内联完成项：根据当前上下文生成并提供代码完成建议。
 * 2. 处理幽灵文本显示：管理幽灵文本的显示逻辑和用户交互。
 * 3. 处理幽灵文本插入：处理用户选择插入幽灵文本后的相关操作。
 * 4. 注册幽灵文本功能：将幽灵文本功能注册到VS Code编辑器中。
 * 
 * 这个模块与其他模块（如telemetry, logger等）协同工作，
 * 以提供流畅的代码补全体验，同时收集必要的遥测数据用于功能改进。
 */

import * as vscode from 'vscode';
import {getGhostText,ResultType}  from './ghostTextManager';
import {getConfig,ConfigKey} from './configurationManager';
import { logger } from './loggerManager';
import { My_Context,Document,Position,Token,Context } from './contextType';
import {ignoreDocument} from './documentIgnoreManager'
import {postRejectionTasks}  from './postCompletionTasks';
import {completionsFromGhostTextResults}  from './completionsFromGhostTextResults';

const GHOST_TEXT_POST_INSERT = "_ghostTextPostInsert";

interface Completion {
  insertText: string;
  [key: string]: any;
}

interface GhostTextResult {
  type: string;
  reason?: string;
  value?: any;
  telemetryData?: any;
  telemetryBlob?: any;
}

interface ExtendedInlineCompletionItem extends vscode.InlineCompletionItem {
  index?: number;
  telemetry?: any;
  displayText?: string;
  resultType?: any;
  uri?: vscode.Uri;
  insertOffset?: number;
}

/**
 * 从完成项中获取插入文本
 * @param completion - 完成项对象
 * @returns 插入文本
 */
export function getInsertionTextFromCompletion(completion: Completion): string {
  return completion.insertText;
}

/**
 * 提供内联完成项
 * @param context - 上下文对象，包含扩展的上下文信息
 * @param document - 文档对象，表示当前正在编辑的文档
 * @param position - 位置对象，表示光标在文档中的位置
 * @param context_ - 上下文信息对象，包含触发内联完成的上下文
 * @param token - 取消令牌对象，用于取消操作
 * @returns 内联完成项结果
 * 
 *  Document,Position,Token,Context
 */
let lastPosition:Position |undefined ;
let lastUri:any; 
let lastChoiceIndex:any;
let shownCompletions: any[] = [];
export async function provideInlineCompletions(
  myContext: My_Context,
  document: Document,
  position: Position,
  context_: Context,
  token: Token
): Promise<any> {
  logger.debug(myContext, "开始提供内联完成项");
  const result = await (async function (myContext, document, position, context_, token) {
   
    //TODO 检查内联建议功能是否启用，如果禁用则提前返回
    if (
      !(function (myContext :My_Context) {
        return getConfig(myContext, ConfigKey.InlineSuggestEnable);
      })(myContext)
    ){
      return {
        type: "abortedBeforeIssued",
        reason: "ghost text is disabled",
      };
    }

    // 检查当前文档是否应被忽略（例如在忽略列表中）
    // if (ignoreDocument(myContext, document)){
    //   return {
    //     type: "abortedBeforeIssued",
    //     reason: "document is ignored",
    //   };
    // }
    logger.debug(
      myContext,
      `Ghost text called at [${position.line}, ${position.character}], with triggerKind ${context_.triggerKind}`
    );

    // 检查操作是否已被取消
    if (token.isCancellationRequested){
      return (
        logger.info(myContext, "Cancelled before extractPrompt"),
        {
          type: "abortedBeforeIssued",
          reason: "cancelled before extractPrompt",
        }
      );
    }
       // 如果自动完成小部件已显示，则不显示幽灵文本
       if (context_.selectedCompletionInfo) {
        logger.debug(
          myContext,
          "Not showing ghost text because autocomplete widget is displayed"
        );
        return {
          type: "abortedBeforeIssued",
          reason: "autocomplete widget is displayed",
        };
      }
   

    // 从ghostTextManager获取幽灵文本结果
    // 参数包括：上下文、文档、位置、是否由用户手动触发、遥测数据和取消令牌
    const ghostTextResult = await getGhostText(
      myContext,
      document,
      position,
      context_.triggerKind === vscode.InlineCompletionTriggerKind.Invoke,
      undefined,
      token
    );
    logger.debug(myContext, "开始提供内联完成项结束: ", JSON.stringify(ghostTextResult));

    // 如果获取幽灵文本失败，记录日志并返回结果
    if ("success" !== ghostTextResult.type) {
      logger.debug(
        myContext,
        "Breaking, no results from getGhostText -- " + ghostTextResult.type + ": " + ghostTextResult.reason
      );
      return ghostTextResult;
    }
    // 解构获取幽灵文本选项和结果类型
    const [ghostTextOptions, resultType] = ghostTextResult.value;
 // 处理被拒绝的完成项
    // 如果位置或文档已更改，且不是用户按照建议输入，则认为之前显示的建议被拒绝
    if (
      lastPosition &&
      lastUri &&
      (!lastPosition.isEqual(position) || lastUri !== document.uri) &&
      resultType !== ResultType.TypingAsSuggested
    ) {
      
      // 收集被拒绝的完成项
      const rejectedCompletions = shownCompletions.flatMap((completion) =>
        completion.displayText && completion.telemetry
          ? [
              {
                completionText: completion.displayText,
                completionTelemetryData: completion.telemetry,
              },
            ]
          : []
      );
      // 如果有被拒绝的完成项，执行拒绝后的任务（如遥测记录）
      if (rejectedCompletions.length > 0) {
        postRejectionTasks(myContext, "ghostText", document.offsetAt(lastPosition), lastUri, rejectedCompletions);
      }
    }
    // 更新最后位置、URI和显示的完成项列表
    lastPosition = position;
    lastUri = document.uri;
    shownCompletions = [];
    // 再次检查操作是否已被取消
    if (token.isCancellationRequested){
      return (
        logger.info(myContext, "Cancelled after getGhostText"),
        {
          type: "canceled",
          reason: "after getGhostText",
          telemetryData: {
            telemetryBlob: undefined,  // 遥测数据
          },
        }
      );
    }
    // 从幽灵文本结果生成内联完成项
    // 获取编辑器选项以确保格式一致性
    const inlineCompletions = completionsFromGhostTextResults(
      myContext,
      ghostTextOptions,
      resultType,
      document,
      position,
      (function (document) {
        // 查找与当前文档关联的可见编辑器
        const editor = vscode.window.visibleTextEditors.find((editor) => editor.document === document);
        return null == editor ? undefined : editor.options;
      })(document),
      lastChoiceIndex
    );
    logger.debug(myContext, "inlineCompletionProvider.Completions", inlineCompletions);
    // 将内联完成项转换为VSCode的InlineCompletionItem对象
    const inlineCompletionItems = inlineCompletions.map((completion) => {
      const { text, range } = completion;
      // 创建VSCode范围对象
      const vscodeRange = new vscode.Range(
        new vscode.Position(range.start.line, range.start.character),
        new vscode.Position(range.end.line, range.end.character)
      );
      // 创建内联完成项
      const inlineCompletionItem = new vscode.InlineCompletionItem(text, vscodeRange) as ExtendedInlineCompletionItem;
      // 设置完成项的各种属性
      inlineCompletionItem.index = completion.index;
      inlineCompletionItem.telemetry = completion.telemetry;
      inlineCompletionItem.displayText = completion.displayText;
      inlineCompletionItem.resultType = completion.resultType;
      inlineCompletionItem.uri = document.uri;
      // 计算插入偏移量
      inlineCompletionItem.insertOffset = document.offsetAt(
        new vscode.Position(completion.position.line, completion.position.character)
      );
      // 设置插入后执行的命令
      inlineCompletionItem.command = {
        title: "PostInsertTask",
        command: GHOST_TEXT_POST_INSERT,
        arguments: [inlineCompletionItem],
      };
      return inlineCompletionItem;
    });
    // 返回结果：如果没有完成项则返回空结果，否则返回完成项列表
    return 0 === inlineCompletionItems.length
      ? {
          type: "empty",
          reason: "no completionsghostTextResult in final result",
          telemetryData: undefined
        }
      : {
          ...ghostTextResult,
          value: inlineCompletionItems,
        };


  })(myContext, document, position, context_, token);
  
  if ("success" === result.type) {
    return result.value;
  }
}
/**
 * 幽灵文本提供者类
 */
class GhostTextProvider implements vscode.InlineCompletionItemProvider {
  constructor(private myContext: My_Context) {
    logger.debug(myContext, "初始化GhostTextProvider");
  }

  async provideInlineCompletionItems(
    document: vscode.TextDocument,
    position: vscode.Position,
    context: vscode.InlineCompletionContext,
    token: vscode.CancellationToken
  ): Promise<vscode.InlineCompletionItem[] | vscode.InlineCompletionList | null | undefined> {
    logger.debug(this.myContext, "GhostTextProvider: 开始提供内联完成项");
     
    try{
      return await provideInlineCompletions(this.myContext, document, position, context, token);
  
    }catch(e){
      logger.error(this.myContext, "Completions error",e);

    }
  }

  handleDidShowCompletionItem(completionItem: any): void {
    handleGhostTextShown(this.myContext, completionItem);
  }
}

/**
 * 处理幽灵文本显示
 * @param context - 上下文对象
 * @param completionItem - 完成项对象
 */
function handleGhostTextShown(context: My_Context, completionItem: any): void {
  logger.debug(context, "处理幽灵文本显示");

  lastChoiceIndex = completionItem.index;
  if (!shownCompletions.some(item => item.index === completionItem.index)) {
    shownCompletions.push(completionItem);
    
    if (completionItem.telemetry) {
        const isFromCache = completionItem.resultType !== ResultType.Network;
        logger.debug(
            context, 
            `shown choiceIndex: ${completionItem}, fromCache ${isFromCache}`
        );
    }
}


}

/**
 * 处理幽灵文本插入后的操作
 * @param context - 上下文对象
 * @param completionItem - 完成项对象
 */
async function handleGhostTextPostInsert(context: My_Context, completionItem: any): Promise<void> {
  logger.debug(context, "处理幽灵文本插入后的操作");
}

export { handleGhostTextShown };

/**
 * 注册幽灵文本功能
 * @param context - 上下文对象
 * @returns 注册的事件处理器数组
 */
export function registerGhostText(context: My_Context): vscode.Disposable[] {
  console.log('registerGhostText start');
  try {
    const provider = new GhostTextProvider(context);
    console.log('GhostTextProvider created successfully');

    const inlineCompletionProvider = vscode.languages.registerInlineCompletionItemProvider(
      { pattern: '**' },
      provider
    );

    const postInsertCommand = vscode.commands.registerCommand(
      GHOST_TEXT_POST_INSERT,
      (completionItem: any) => handleGhostTextPostInsert(context, completionItem)
    );

    return [inlineCompletionProvider, postInsertCommand];
  } catch (error) {
    console.error('Error in registerGhostText:', error);
    throw error; // 重新抛出错误，以便在 extension.ts 中捕获
  }
}
