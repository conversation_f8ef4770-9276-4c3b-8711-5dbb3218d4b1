/**
 * 3055467
 * 文件名: windowedMatcher.ts
 * 
 * 主要功能:
 * 该文件实现了一个基于窗口的文本匹配算法，用于在源代码中查找相似的文本片段。
 * 主要包含以下组件：
 * 1. Tokenizer: 用于将文本分词
 * 2. Cache: 用于缓存分词结果，提高性能
 * 3. WindowedMatcher: 实现了窗口匹配算法，用于查找最佳匹配或前K个匹配
 * 
 * 该实现使用了分词、缓存和相似度评分等技术来优化匹配过程，
 * 适用于代码补全、相似代码片段查找等场景。
 */
import { Context } from './currentContext'
import { SnippetSelectionOption } from './promptManager';

// 定义排序选项枚举
export enum SortOptions {
  Ascending = "ascending",
  Descending = "descending",
  None = "none"
}

// 定义分词器类
class Tokenizer {
  private stopsForLanguage: Set<string>;

  constructor(context :Context) {
    this.stopsForLanguage = languageStops.get(context.languageId) ?? defaultStops;
  }

  tokenize(e: string): Set<string> {
    return new Set(
      splitIntoWords(e).filter((e) => !this.stopsForLanguage.has(e))
    );
  }
}

// 定义缓存类
class Cache<T> {
  private keys: string[] = [];
  private cache: { [key: string]: T } = {};
  private size: number;

  constructor(size: number) {
    this.size = size;
  }

  put(key: string, value: T): void {
    this.cache[key] = value;
    if (this.keys.length > this.size) {
      this.keys.push(key);
      const oldestKey = this.keys.shift() ?? "";
      delete this.cache[oldestKey];
    }
  }

  get(key: string): T | undefined {
    return this.cache[key];
  }
}

// 创建缓存实例
const cache = new Cache<Set<string>[]>(20);

// 分词函数
export function splitIntoWords(text: string): string[] {
  return text.split(/[^a-zA-Z0-9]/).filter((word) => word.length > 0);
}

// 定义抽象窗口匹配器类
export abstract class WindowedMatcher {
  protected tokenizer: Tokenizer;
  protected referenceTokens: Set<string>;

  constructor(protected context: Context) {
    this.tokenizer = new Tokenizer(context);
    this.referenceTokens = this.tokenizer.tokenize(this.trimDocument(context));
  }

  // 排序评分片段
  sortScoredSnippets(snippets: ScoredSnippet[], sortOption: SortOptions = SortOptions.Descending): ScoredSnippet[] {
    if (sortOption === SortOptions.Ascending) {
      return snippets.sort((a, b) => (a.score > b.score ? 1 : -1));
    } else if (sortOption === SortOptions.Descending) {
      return snippets.sort((a, b) => (a.score > b.score ? -1 : 1));
    }
    return snippets;
  }

  // 检索所有片段
  private retrieveAllSnippets(source: { source: string }, sortOption: SortOptions = SortOptions.Descending): ScoredSnippet[] {
    const snippets: ScoredSnippet[] = [];
    if (source.source.length === 0 || this.referenceTokens.size === 0) return snippets;

    const lines = source.source.split("\n");
    const cacheKey = `${this.id()}:${source.source}`;
    const cachedTokens = cache.get(cacheKey) ?? [];
    const shouldCache = cachedTokens.length === 0;
    const lineTokens = shouldCache ? lines.map(line => this.tokenizer.tokenize(line)) : [];

    for (const [index, [startLine, endLine]] of this.getWindowsDelineations(lines).entries()) {
      if (shouldCache) {
        const windowTokens = new Set<string>();
        lineTokens.slice(startLine, endLine).forEach(tokens => tokens.forEach(token => windowTokens.add(token)));
        cachedTokens.push(windowTokens);
      }

      const windowTokens = cachedTokens[index];
      const score = this.similarityScore(windowTokens, this.referenceTokens);
      snippets.push({ score, startLine, endLine });
    }

    if (shouldCache) {
      cache.put(cacheKey, cachedTokens);
    }

    return this.sortScoredSnippets(snippets, sortOption);
  }

  // 查找匹配
  findMatches(source: { source: string }, selectionOption: SnippetSelectionOption = SnippetSelectionOption.BestMatch, topK?: number): MatchResult[] {
    if (selectionOption === SnippetSelectionOption.BestMatch) {
      const bestMatch = this.findBestMatch(source);
      return bestMatch ? [bestMatch] : [];
    }
    return this.findTopKMatches(source, topK) ?? [];
  }

  // 查找最佳匹配
  private findBestMatch(source: { source: string }): MatchResult | undefined {
    if (source.source.length === 0 || this.referenceTokens.size === 0) return undefined;

    const lines = source.source.split("\n");
    const snippets = this.retrieveAllSnippets(source, SortOptions.Descending);

    if (snippets.length > 0 && snippets[0].score !== 0) {
      return {
        snippet: lines.slice(snippets[0].startLine, snippets[0].endLine).join("\n"),
        ...snippets[0]
      };
    }

    return undefined;
  }

  // 查找前K个匹配
  private findTopKMatches(source: { source: string }, topK: number = 1): MatchResult[] | undefined {
    if (source.source.length === 0 || this.referenceTokens.size === 0 || topK < 1) return undefined;

    const lines = source.source.split("\n");
    const snippets = this.retrieveAllSnippets(source, SortOptions.Descending);

    if (snippets.length === 0 || snippets[0].score === 0) return undefined;

    const result: ScoredSnippet[] = [snippets[0]];
    for (let i = 1; i < snippets.length && result.length < topK; i++) {
      if (result.findIndex(s => snippets[i].startLine < s.endLine && snippets[i].endLine > s.startLine) === -1) {
        result.push(snippets[i]);
      }
    }

    return result.map(s => ({
      snippet: lines.slice(s.startLine, s.endLine).join("\n"),
      ...s
    }));
  }

  // 以下方法需要在子类中实现
  abstract trimDocument(context: Context): string;

  abstract id(): string;

  abstract getWindowsDelineations(lines: string[]): number[][];

  abstract similarityScore(set1: Set<string>, set2: Set<string>): number;
}

// 定义停用词集合
const commonStopWords = new Set([
  "we", "our", "you", "it", "its", "they", "them", "their", "this", "that",
  "these", "those", "is", "are", "was", "were", "be", "been", "being",
  "have", "has", "had", "having", "do", "does", "did", "doing", "can",
  "don", "t", "s", "will", "would", "should", "what", "which", "who",
  "when", "where", "why", "how", "a", "an", "the", "and", "or", "not",
  "no", "but", "because", "as", "until", "again", "further", "then",
  "once", "here", "there", "all", "any", "both", "each", "few", "more",
  "most", "other", "some", "such", "above", "below", "to", "during",
  "before", "after", "of", "at", "by", "about", "between", "into",
  "through", "from", "up", "down", "in", "out", "on", "off", "over",
  "under", "only", "own", "same", "so", "than", "too", "very", "just", "now"
]);

const defaultStops = new Set([
  "if", "then", "else", "for", "while", "with", "def", "function", "return",
  "TODO", "import", "try", "catch", "raise", "finally", "repeat", "switch",
  "case", "match", "assert", "continue", "break", "const", "class", "enum",
  "struct", "static", "new", "super", "this", "var",
  ...commonStopWords
]);

const languageStops = new Map<string, Set<string>>();

// 定义接口
interface ScoredSnippet {
  score: number;
  startLine: number;
  endLine: number;
}

interface MatchResult extends ScoredSnippet {
  snippet: string;
}
