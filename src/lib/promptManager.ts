/**
 * 3055312.js - GitHub Copilot 提示词管理模块
 * promptManager.js - GitHub Copilot 提示词管理模块
 *
 * 该模块负责管理和生成 GitHub Copilot 的提示词（Prompts）。
 * 主要功能包括：
 * 1. 提示词选项的配置和验证
 * 2. 语言标记和路径标记的处理
 * 3. 上下文代码片段的收集和组织
 * 4. 相似代码片段的匹配和排序
 * 5. 后缀文本的处理和编辑距离计算
 */
// 导入所需的模块
// 导入所需的模块
import * as promptUtils from "./languageUtils";
import * as importContext from "./importContextExtractor";
import * as neighborSnippets from "./neighborSnippetManager";
import * as siblingFunctions from "./siblingFunctionManager";

import tokenUtil from "./tokenizer2";
//import * as tokenUtil2 from 'tokenizers';

import * as priorities from "./promptElementManager";
import * as editDistance from "./editDistanceCalculator";
import { MyFileSystem } from "./fileSystemOperations";
import { Context, NeighboringFiles } from "./currentContext";

// 定义 lastSuffix 变量
let lastSuffix: { text: string; tokens: number[] } = {
  text: "",
  tokens: [],
};

// 常量定义
export const MAX_PROMPT_LENGTH = 15000;
export const MAX_EDIT_DISTANCE_LENGTH = 50;
export const TOKENS_RESERVED_FOR_SUFFIX_ENCODING = 5;

// 枚举定义
export enum LanguageMarkerOption {
  NoMarker = "nomarker",
  Top = "top",
  Always = "always",
}

export enum PathMarkerOption {
  NoMarker = "nomarker",
  Top = "top",
  Always = "always",
}

export enum SiblingOption {
  NoSiblings = "nosiblings",
  SiblingsOverContext = "siblingabove",
  ContextOverSiblings = "contextabove",
}

export enum NeighboringTabsOption {
  None = "none",
  Conservative = "conservative",
  Medium = "medium",
  Eager = "eager",
  EagerButLittle = "eagerButLittle",
}

export enum NeighboringTabsPositionOption {
  TopOfText = "top",
  DirectlyAboveCursor = "aboveCursor",
  AfterSiblings = "afterSiblings",
}

export enum SnippetSelectionOption {
  BestMatch = "bestMatch",
  TopK = "topK",
}

export enum LocalImportContextOption {
  NoContext = "nocontext",
  Declarations = "declarations",
}

export enum LineEndingOptions {
  ConvertToUnix = "unix",
  KeepOriginal = "keep",
}

export enum SuffixOption {
  None = "none",
  FifteenPercent = "fifteenPercent",
}

export enum SuffixMatchOption {
  Equal = "equal",
  Levenshtein = "levenshteineditdistance",
}

export enum SuffixStartMode {
  Cursor = "cursor",
  CursorTrimStart = "cursortrimstart",
  SiblingBlock = "siblingblock",
  SiblingBlockTrimStart = "siblingblocktrimstart",
}

interface PromptOptionsInput {
  [key: string]: any;
  // TODO: 根据实际使用情况添加具体的选项
}

// PromptOptions 类定义
export class PromptOptions {
  fs: MyFileSystem;
  maxPromptLength: number = MAX_PROMPT_LENGTH;
  languageMarker: LanguageMarkerOption = LanguageMarkerOption.Top;
  pathMarker: PathMarkerOption = PathMarkerOption.Top;
  includeSiblingFunctions: SiblingOption = SiblingOption.ContextOverSiblings;
  localImportContext: LocalImportContextOption =
    LocalImportContextOption.Declarations;
  neighboringTabs: NeighboringTabsOption = NeighboringTabsOption.Eager;
  neighboringTabsPosition: NeighboringTabsPositionOption =
    NeighboringTabsPositionOption.TopOfText;
  lineEnding: LineEndingOptions = LineEndingOptions.ConvertToUnix;
  suffixPercent: number = 0;
  suffixStartMode: SuffixStartMode = SuffixStartMode.Cursor;
  suffixMatchThreshold: number = 0;
  suffixMatchCriteria: SuffixMatchOption = SuffixMatchOption.Levenshtein;
  fimSuffixLengthThreshold: number = 0;
  indentationMinLength?: number;
  indentationMaxLength?: number;
  snippetSelection?: SnippetSelectionOption;
  snippetSelectionK?: number;

  constructor(fileSystem: MyFileSystem, options?: PromptOptionsInput) {
    this.fs = fileSystem;
    if (options) {
      for (const key in options) {
        if (Object.prototype.hasOwnProperty.call(options, key)) {
          (this as any)[key] = options[key];
        }
      }
    }
    this.validateOptions();
  }

  private validateOptions(): void {
    if (this.suffixPercent < 0 || this.suffixPercent > 100) {
      throw new Error(
        `suffixPercent must be between 0 and 100, but was ${this.suffixPercent}`
      );
    }
    if (
      this.suffixPercent > 0 &&
      this.includeSiblingFunctions != SiblingOption.NoSiblings
    ) {
      throw new Error(
        `Invalid option combination. Cannot set suffixPercent > 0 (${this.suffixPercent}) and includeSiblingFunctions ${this.includeSiblingFunctions}`
      );
    }
    if (this.suffixMatchThreshold < 0 || this.suffixMatchThreshold > 100) {
      throw new Error(
        `suffixMatchThreshold must be between 0 and 100, but was ${this.suffixMatchThreshold}`
      );
    }
    if (this.fimSuffixLengthThreshold < -1) {
      throw new Error(
        `fimSuffixLengthThreshold must be at least -1, but was ${this.fimSuffixLengthThreshold}`
      );
    }
    if (
      this.indentationMinLength != null &&
      this.indentationMaxLength != null &&
      this.indentationMinLength > this.indentationMaxLength
    ) {
      throw new Error(
        `indentationMinLength must be less than or equal to indentationMaxLength, but was ${this.indentationMinLength} and ${this.indentationMaxLength}`
      );
    }
    if (
      this.snippetSelection === SnippetSelectionOption.TopK &&
      this.snippetSelectionK === undefined
    ) {
      throw new Error(
        "snippetSelectionK must be defined when snippetSelection is TopK."
      );
    }
    if (
      this.snippetSelection === SnippetSelectionOption.TopK &&
      this.snippetSelectionK !== undefined &&
      this.snippetSelectionK <= 0
    ) {
      throw new Error(
        `snippetSelectionK must be greater than 0, but was ${this.snippetSelectionK}`
      );
    }
  }
}

// 语言ID映射
const languageIdMap: { [key: string]: string } = {
  javascriptreact: "javascript",
  jsx: "javascript",
  typescriptreact: "typescript",
  jade: "pug",
  cshtml: "razor",
};

// 辅助函数
export function normalizeLanguageId(languageId: string): string {
  const normalizedId = languageIdMap[languageId.toLowerCase()];
  return normalizedId !== undefined ? normalizedId : languageId.toLowerCase();
}

export function newLineEnded(text: string): string {
  return text === "" || text.endsWith("\n") ? text : text + "\n";
}

// 主要的 getPrompt 函数
export async function getPrompt(
  fileSystem: MyFileSystem,
  context: Context,
  options: any,
  neighboringFiles: NeighboringFiles[] = []
): Promise<any> {
  const promptOptions = new PromptOptions(fileSystem, options);
  let isSuffixMatch = false;
  const { source: sourceCode, offset: cursorOffset } = context;

  if (cursorOffset < 0 || cursorOffset > sourceCode.length) {
    throw new Error(`Offset ${cursorOffset} is out of range.`);
  }

  context.languageId = normalizeLanguageId(context.languageId);

  const priorityManager = new priorities.Priorities();

  // 创建一个top的优先级，如果
  const topPriority = priorityManager.justBelow(priorities.Priorities.TOP); // 0.5

  //语言标记
  const languageMarkerPriority =
    promptOptions.languageMarker == LanguageMarkerOption.Always
      ? priorityManager.justBelow(priorities.Priorities.TOP)
      : priorityManager.justBelow(topPriority); // 0.25

  //路径标记
  const pathMarkerPriority =
    promptOptions.pathMarker == PathMarkerOption.Always
      ? priorityManager.justBelow(priorities.Priorities.TOP)
      : priorityManager.justBelow(topPriority); // 0.375

  // 兄弟函数
  const siblingPriority =
    promptOptions.includeSiblingFunctions == SiblingOption.ContextOverSiblings
      ? priorityManager.justBelow(topPriority)
      : priorityManager.justAbove(topPriority); //0.75

  //导入上下文
  const importContextPriority = priorityManager.justBelow(
    topPriority,
    siblingPriority
  ); //0.4375
  // 相邻标签
  const neighboringTabsPriority = priorityManager.justBelow(
    importContextPriority
  ); //

  const promptWishlist = new priorities.PromptWishlist(
    promptOptions.lineEnding
  );

  let languageMarkerElement: any;
  let pathMarkerElement: any;

  if (promptOptions.languageMarker != LanguageMarkerOption.NoMarker) {
    const languageMarkerText = newLineEnded(
      promptUtils.getLanguageMarker(context)
    );
    languageMarkerElement = promptWishlist.append(
      languageMarkerText,
      priorities.PromptElementKind.LanguageMarker,
      languageMarkerPriority
    );
  }

  if (promptOptions.pathMarker != PathMarkerOption.NoMarker) {
    const pathMarkerText = newLineEnded(promptUtils.getPathMarker(context));
    if (pathMarkerText.length > 0) {
      pathMarkerElement = promptWishlist.append(
        pathMarkerText,
        priorities.PromptElementKind.PathMarker,
        pathMarkerPriority
      );
    }
  }
  //跨文件感知能力
  if (promptOptions.localImportContext != LocalImportContextOption.NoContext) {
    for (const file of await importContext.extractLocalImportContext(
      context,
      promptOptions.fs
    )) {
      promptWishlist.append(
        newLineEnded(file),
        priorities.PromptElementKind.ImportedFile,
        importContextPriority
      );
    }
  }

  const neighboringTabs =
    promptOptions.neighboringTabs == NeighboringTabsOption.None ||
    neighboringFiles.length == 0
      ? []
      : await neighborSnippets.getNeighborSnippets(
          context,
          neighboringFiles,
          promptOptions.neighboringTabs,
          promptOptions.indentationMinLength,
          promptOptions.indentationMaxLength,
          promptOptions.snippetSelection,
          promptOptions.snippetSelectionK
        );

  function snippetMatches() {
    neighboringTabs.forEach((snippet: any) =>
      promptWishlist.append(
        snippet.snippet,
        priorities.PromptElementKind.SimilarFile,
        neighboringTabsPriority,
        tokenUtil.tokenLength(snippet.snippet),
        snippet.score
      )
    );
  }

  if (
    promptOptions.neighboringTabsPosition ==
    NeighboringTabsPositionOption.TopOfText
  ) {
    snippetMatches();
  }

  const snippetMatchesArray: any[] = [];
  let snippetMatchesText: string;

  if (promptOptions.includeSiblingFunctions != SiblingOption.NoSiblings) {
    snippetMatchesText = sourceCode.substring(0, cursorOffset);
  } else {
    const {
      siblings: siblingFunctionsArray,
      beforeInsertion: beforeInsertionText,
      afterInsertion: afterInsertionText,
    } = await siblingFunctions.getSiblingFunctions(context);

    promptWishlist
      .appendLineForLine(
        beforeInsertionText,
        priorities.PromptElementKind.BeforeCursor,
        topPriority
      )
      .forEach((element: any) => snippetMatchesArray.push(element));

    let siblingPriorityPointer = siblingPriority;
    siblingFunctionsArray.forEach((element: string) => {
      promptWishlist.append(
        element,
        priorities.PromptElementKind.AfterCursor,
        siblingPriorityPointer
      );
      siblingPriorityPointer = priorityManager.justBelow(
        siblingPriorityPointer
      );
    });

    if (
      promptOptions.neighboringTabsPosition ==
      NeighboringTabsPositionOption.AfterSiblings
    ) {
      snippetMatches();
    }
    snippetMatchesText = afterInsertionText;
  }

  if (
    promptOptions.neighboringTabsPosition ==
    NeighboringTabsPositionOption.DirectlyAboveCursor
  ) {
    const cursorPosition = snippetMatchesText.lastIndexOf("\n") + 1;
    const beforeCursorText = snippetMatchesText.substring(0, cursorPosition);
    const afterCursorText = snippetMatchesText.substring(cursorPosition);

    promptWishlist
      .appendLineForLine(
        beforeCursorText,
        priorities.PromptElementKind.BeforeCursor,
        topPriority
      )
      .forEach((element: any) => snippetMatchesArray.push(element));

    snippetMatches();

    if (afterCursorText.length > 0) {
      snippetMatchesArray.push(
        promptWishlist.append(
          afterCursorText,
          priorities.PromptElementKind.AfterCursor,
          topPriority
        )
      );
      if (snippetMatchesArray.length > 1) {
        promptWishlist.require(
          snippetMatchesArray[snippetMatchesArray.length - 2],
          snippetMatchesArray[snippetMatchesArray.length - 1]
        );
      }
    }
  } else {
    promptWishlist
      .appendLineForLine(
        snippetMatchesText,
        priorities.PromptElementKind.BeforeCursor,
        topPriority
      )
      .forEach((element: any) => snippetMatchesArray.push(element));
  }

  if (
    LanguageMarkerOption.Top == promptOptions.languageMarker &&
    snippetMatchesArray.length > 0 &&
    languageMarkerElement !== undefined
  ) {
    promptWishlist.require(languageMarkerElement, snippetMatchesArray[0]);
  }

  if (
    PathMarkerOption.Top == promptOptions.pathMarker &&
    snippetMatchesArray.length > 0 &&
    pathMarkerElement !== undefined
  ) {
    if (languageMarkerElement) {
      promptWishlist.require(pathMarkerElement, languageMarkerElement);
    } else {
      promptWishlist.require(pathMarkerElement, snippetMatchesArray[0]);
    }
  }

  if (languageMarkerElement !== undefined && pathMarkerElement !== undefined) {
    promptWishlist.exclude(pathMarkerElement, languageMarkerElement);
  }

  let snippetMatchesTextToProcess = sourceCode.slice(cursorOffset);

  if (
    promptOptions.suffixPercent == 0 ||
    snippetMatchesTextToProcess.length <= promptOptions.fimSuffixLengthThreshold
  ) {
    return promptWishlist.fulfill(promptOptions.maxPromptLength);
  } else {
    // 处理后缀匹配逻辑
    let snippetMatchesTextOffset = cursorOffset;
    if (
      promptOptions.suffixStartMode !== SuffixStartMode.Cursor &&
      promptOptions.suffixStartMode !== SuffixStartMode.CursorTrimStart
    ) {
      snippetMatchesTextOffset = await siblingFunctions.getSiblingFunctionStart(
        context
      );
    }

    const snippetMatchesTextLength = promptOptions.maxPromptLength; //- TOKENS_RESERVED_FOR_SUFFIX_ENCODING;
    let snippetMatchesTextPercent = Math.floor(
      (snippetMatchesTextLength * (100 - promptOptions.suffixPercent)) / 100
    );
    let snippetMatchesTextResult = promptWishlist.fulfill(
      snippetMatchesTextPercent
    );

    const snippetMatchesTextLengthDifference =
      snippetMatchesTextLength - snippetMatchesTextResult.prefixLength;

    let snippetMatchesTextToProcessTrimmed = sourceCode.slice(
      snippetMatchesTextOffset
    );
    if (
      promptOptions.suffixStartMode != SuffixStartMode.SiblingBlockTrimStart &&
      promptOptions.suffixStartMode != SuffixStartMode.CursorTrimStart
    ) {
      snippetMatchesTextToProcessTrimmed =
        snippetMatchesTextToProcessTrimmed.trimStart();
    }

    const snippetMatchesTextToProcessTokens = tokenUtil.takeFirstTokens(
      snippetMatchesTextToProcessTrimmed,
      snippetMatchesTextLengthDifference
    );

    if (
      snippetMatchesTextToProcessTokens.tokens.length <=
      snippetMatchesTextLengthDifference - 3
    ) {
      snippetMatchesTextPercent =
        snippetMatchesTextLength -
        snippetMatchesTextToProcessTokens.tokens.length;
      snippetMatchesTextResult = promptWishlist.fulfill(
        snippetMatchesTextPercent
      );
    }

    // 根据匹配标准检查后缀是否匹配
    if (promptOptions.suffixMatchCriteria == SuffixMatchOption.Equal) {
      // 使用完全相等的方式匹配
      if (
        snippetMatchesTextToProcessTokens.tokens.length ===
          lastSuffix.tokens.length &&
        snippetMatchesTextToProcessTokens.tokens.every(
          (element, index) => element === lastSuffix.tokens[index]
        )
      ) {
        isSuffixMatch = true;
      }
    } else if (
      promptOptions.suffixMatchCriteria == SuffixMatchOption.Levenshtein
    ) {
      // 使用编辑距离的方式匹配
      if (
        snippetMatchesTextToProcessTokens.tokens.length > 0 &&
        promptOptions.suffixMatchThreshold > 0
      ) {
        const score = editDistance.findEditDistanceScore(
          snippetMatchesTextToProcessTokens.tokens.slice(
            0,
            MAX_EDIT_DISTANCE_LENGTH
          ),
          lastSuffix.tokens.slice(0, MAX_EDIT_DISTANCE_LENGTH)
        );
        if (
          score &&
          100 * score.score <
            promptOptions.suffixMatchThreshold *
              Math.min(
                MAX_EDIT_DISTANCE_LENGTH,
                snippetMatchesTextToProcessTokens.tokens.length
              )
        ) {
          isSuffixMatch = true;
        }
      }
    }

    // 根据匹配结果设置返回值
    if (
      isSuffixMatch &&
      lastSuffix.tokens.length <= snippetMatchesTextLengthDifference
    ) {
      // 如果找到匹配的后缀，使用上一次的后缀
      if (lastSuffix.tokens.length <= snippetMatchesTextLengthDifference - 3) {
        snippetMatchesTextPercent =
          snippetMatchesTextLength - lastSuffix.tokens.length;
        snippetMatchesTextResult = promptWishlist.fulfill(
          snippetMatchesTextPercent
        );
      }
      snippetMatchesTextResult.suffix = lastSuffix.text;
      snippetMatchesTextResult.suffixLength = lastSuffix.tokens.length;
    } else {
      // 如果没有找到匹配的后缀，使用当前的后缀
      snippetMatchesTextResult.suffix = snippetMatchesTextToProcessTokens.text;
      snippetMatchesTextResult.suffixLength =
        snippetMatchesTextToProcessTokens.tokens.length;
      lastSuffix = snippetMatchesTextToProcessTokens;
    }

    // 返回最终的提示词结果
    return snippetMatchesTextResult;
  }
}
