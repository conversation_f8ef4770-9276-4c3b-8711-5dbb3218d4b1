/**
 * @fileoverview 此文件定义了扩展所需的文件系统操作类。
 * 它提供了读取文件内容、获取文件修改时间和文件状态的功能。
 */

import * as vscode from 'vscode';
import { MyFileSystem, FileStats } from './fileSystemOperations';

export class ExtensionFileSystem extends MyFileSystem {
  // 读取文件内容
  async readFile(path: string): Promise<Buffer> {
    const uint8Array = await vscode.workspace.fs.readFile(vscode.Uri.file(path));
    return Buffer.from(uint8Array);
  }

  // 获取文件的最后修改时间
  async mtime(path: string): Promise<number> {
    const stat = await vscode.workspace.fs.stat(vscode.Uri.file(path));
    return stat.mtime;
  }

  // 获取文件的状态信息
  async stat(path: string): Promise<FileStats> {
    const vscodeFileStat = await vscode.workspace.fs.stat(vscode.Uri.file(path));
    return {
      ctime: vscodeFileStat.ctime,
      mtime: vscodeFileStat.mtime,
      size: vscodeFileStat.size
    };
  }
}

// 导出文件系统对象
export const extensionFileSystem: MyFileSystem = new ExtensionFileSystem();
