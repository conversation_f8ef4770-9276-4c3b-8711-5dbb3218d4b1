// 引入模块
import * as vscode from 'vscode';
import { debounce } from './debounceUtils';
import * as configurationManager from './configurationManager';
//import * as telemetryModule from './6333';
import * as commandsModule from './3060';
import { My_Context } from './contextType';

// 导出 CopilotStatusBar 类
export class CopilotStatusBar {
  // 上下文对象
  private ctx: My_Context;
  // 是否正在显示消息
  private showingMessage: boolean;
  // Copilot 状态
  private status: 'Normal' | 'Error' | 'Warning' | 'InProgress';
  // 错误消息
  private errorMessage: string;
  // 重试函数
  private errorRetry?: () => void;
  // Copilot 是否启用
  private enabled: boolean;
  // 禁用时的颜色
  private disabledColor: vscode.ThemeColor;
  // 状态栏项
  private item: vscode.StatusBarItem;
  // 延迟更新显示的方法（防抖）
  private delayedUpdateDisplay: () => void;

  constructor(ctx: My_Context) {
    this.ctx = ctx;
    this.showingMessage = false;
    this.status = 'Normal';
    this.errorMessage = '';
    this.disabledColor = new vscode.ThemeColor('statusBarItem.warningBackground');
    // 使用防抖函数，延迟更新状态栏
    this.delayedUpdateDisplay = debounce(100, () => {
      this.updateDisplay();
    });
    this.enabled = this.checkEnabledForLanguage();
    this.item = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 0);

    this.updateDisplay();
    this.item.show();

    // 监听编辑器和文档变化，实时更新状态栏
    vscode.window.onDidChangeActiveTextEditor(() => {
      this.updateStatusBarIndicator();
    });
    vscode.workspace.onDidCloseTextDocument(() => {
      this.updateStatusBarIndicator();
    });
    vscode.workspace.onDidOpenTextDocument(() => {
      this.updateStatusBarIndicator();
    });
  }

  // 更新状态栏使其反映当前启用状态
  private updateStatusBarIndicator(): void {
    this.enabled = this.checkEnabledForLanguage();
    this.updateDisplay();
  }

  // 检查当前语言是否启用 Copilot
  private checkEnabledForLanguage(): boolean {
    return configurationManager.getEnabledConfig(this.ctx) || false;
  }

  // 根据当前 Copilot 状态更新状态栏显示
  private updateDisplay(): void {
    switch (this.status) {
      case 'Error':
        this.item.text = '$(copilot-notconnected)';
        this.item.command = commandsModule.CMDShowActivationErrors;
        this.item.tooltip = 'Copilot activation failed';
        break;
      case 'Warning':
        this.item.text = '$(copilot-warning)';
        this.item.command = undefined;
        this.item.tooltip = 'Copilot is encountering temporary issues';
        break;
      case 'InProgress':
        this.item.text = '$(loading~spin)';
        break;
      case 'Normal':
        this.item.text = '$(copilot-logo)';
        this.item.command = commandsModule.CMDToggleCopilot;
        this.item.tooltip = this.enabled ? 'Deactivate Copilot' : 'Activate Copilot';
        this.item.backgroundColor = this.enabled ? undefined : this.disabledColor;
        break;
    }
  }

  // 获取状态栏项
  public getStatusBarItem(): vscode.StatusBarItem {
    return this.item;
  }

  // 设置为进度状态
  public setProgress(): void {
    if (this.status !== 'Error') {
      this.status = 'InProgress';
      this.delayedUpdateDisplay();
    }
  }

  // 结束进度状态，恢复正常
  public removeProgress(): void {
    if (this.status !== 'Error' && this.status !== 'Warning') {
      this.status = 'Normal';
      this.delayedUpdateDisplay();
    }
  }

  // 设置为警告状态
  public setWarning(): void {
    if (this.status !== 'Error') {
      this.status = 'Warning';
      this.updateDisplay();
    }
  }

  // 设置为错误状态，并记录错误信息和重试函数
  public setError(errorMsg: string, retry?: () => void): void {
    this.status = 'Error';
    this.errorMessage = errorMsg;
    this.errorRetry = retry;
    this.updateDisplay();
  }

  // 强制恢复到正常状态
  public forceNormal(): void {
    this.status = 'Normal';
    this.errorMessage = '';
    this.errorRetry = undefined;
    this.updateDisplay();
  }

  // 切换 Copilot 状态栏的启用/禁用状态
  public toggleStatusBar(): void {
    const configProvider = this.ctx.get(configurationManager.ConfigProvider);
    const currentEnabled = this.enabled;
    const activeEditor = vscode.window.activeTextEditor;
    const languageId = activeEditor?.document.languageId;
    const hideInlineSuggestCmd = 'editor.action.inlineSuggest.hide';

    if (this.showingMessage) return;

    // const telemetryData = telemetryModule.TelemetryData.createAndMarkAsIssued({
    //   languageId: languageId || '*',
    // });

    // 全局和当前语言配置一致时，提示用户选择作用域
    if (
      configurationManager.getEnabledConfig(this.ctx, '*') ===
      configurationManager.getEnabledConfig(this.ctx, languageId)
    ) {
      this.showingMessage = true;
      setTimeout(() => {
        this.showingMessage = false;
      }, 15000);

      const action = currentEnabled ? 'Disable' : 'Enable';
      const globalOption = `${action} Globally`;
      const languageOption = `${action} for ${languageId}`;
      const options = languageId ? [globalOption, languageOption] : [globalOption];

      vscode.window
        .showInformationMessage(
          `Would you like to ${currentEnabled ? 'disable' : 'enable'} Copilot?`,
          ...options
        )
        .then((selection) => {
          const isGlobal = selection === globalOption;
          this.showingMessage = false;
          if (selection === undefined) {
            //telemetryModule.telemetry(this.ctx, 'statusBar.cancelToggle');
            return;
          }
          // telemetryModule.telemetry(
          //   this.ctx,
          //   `statusBar${isGlobal ? '.global' : '.language'}${currentEnabled ? 'Off' : 'On'}`,
          //   telemetryData
          // );
          if (currentEnabled) {
            vscode.commands.executeCommand(hideInlineSuggestCmd);
          }
          const configKey = isGlobal ? '*' : languageId;
          configProvider.updateEnabledConfig(this.ctx, configKey, !currentEnabled).then(() => {
            this.enabled = !currentEnabled;
            this.updateDisplay();
          });
        });
    } else {
      // telemetryModule.telemetry(
      //   this.ctx,
      //   `statusBar.language${currentEnabled ? 'Off' : 'On'}`,
      //   telemetryData
      // );
      if (currentEnabled) {
        vscode.commands.executeCommand(hideInlineSuggestCmd);
      }
      configProvider.updateEnabledConfig(this.ctx, languageId || '*', !currentEnabled).then(() => {
        this.enabled = !currentEnabled;
        this.updateDisplay();
      });
    }
    this.updateDisplay();
  }

  // 展示 Copilot 激活错误
  public showActivationErrors(outputChannel: vscode.OutputChannel): void {
    if (this.showingMessage) return;
    this.showingMessage = true;
    const actions = ['Show output log'];
    if (this.errorRetry) {
      actions.push('Retry');
    }
    vscode.window.showWarningMessage(this.errorMessage, ...actions).then((selected) => {
      this.showingMessage = false;
      if (selected === 'Show output log') {
        outputChannel.show();
      }
      if (selected === 'Retry' && this.errorRetry) {
        this.errorRetry();
      }
    });
  }
}
