
import * as vscode from 'vscode';
import {MyContext} from './myContext';
// 定义 Context 类的接口
export type My_Context =  MyContext;
export type Document =  vscode.TextDocument;
export type Position = vscode.Position;
export type Token = vscode.CancellationToken;
export type Context =  vscode.InlineCompletionContext;
export type Uri = vscode.Uri;
export type TextDocumentChangeEvent = vscode.TextDocumentChangeEvent
export type Notebook= vscode.NotebookDocument;
export type VSRange = vscode.Range;

export type Cell = vscode.NotebookCell;

// 定义缩进信息接口
export interface IndentationInfo {
  prev?: number;
  current: number;
  next?: number;
}