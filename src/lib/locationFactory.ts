/**
 * 6403
 * 文件名: locationFactory.ts
 * 
 * 主要功能：
 * 这个文件定义了一个抽象的 LocationFactory 类，用于创建和管理位置相关的对象。
 * 该类提供了创建 Range 和 Position 对象的抽象方法。
 */

import { VSRange, Position } from './contextType';

/**
 * LocationFactory 抽象类
 * 
 * @class
 * @abstract
 * @description 用于创建和管理位置相关的对象。定义了创建 Range 和 Position 对象的抽象方法。
 */
export abstract class LocationFactory {
  /**
   * 创建一个 Range 对象
   * @param startLine 起始行或起始 Position
   * @param startCharacter 起始字符或结束 Position
   * @param endLine 结束行（可选）
   * @param endCharacter 结束字符（可选）
   * @returns VSRange 对象
   */
  abstract range(startLine: number | Position, startCharacter: number | Position, endLine?: number, endCharacter?: number): VSRange;

  /**
   * 创建一个 Position 对象
   * @param line 行号
   * @param character 字符位置
   * @returns Position 对象
   */
  abstract position(line: number, character: number): Position;
}
