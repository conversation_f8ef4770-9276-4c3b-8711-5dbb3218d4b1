/**
 * 8142
 * 过滤器和设置管理模块
 * 
 * 该模块定义了用于管理过滤器和设置的枚举、类和常量。
 * 主要功能包括：
 * 1. 定义过滤器和目标人群的枚举
 * 2. 提供遥测名称映射
 * 3. 实现FilterSettings类，用于管理和操作过滤器设置
 * 
 * 该模块在Copilot扩展中用于处理各种配置和过滤设置，
 * 支持灵活的过滤器管理和遥测数据收集。
 */

// 导出模块
export enum Filter {
  Market = "X-MSEdge-Market",
  CorpNet = "X-FD-Corpnet",
  ApplicationVersion = "X-VSCode-AppVersion",
  Build = "X-VSCode-Build",
  ClientId = "X-MSEdge-ClientId",
  ExtensionName = "X-VSCode-ExtensionName",
  ExtensionVersion = "X-VSCode-ExtensionVersion",
  Language = "X-VSCode-Language",
  TargetPopulation = "X-VSCode-TargetPopulation",
  CopilotClientTimeBucket = "X-Copilot-ClientTimeBucket",
  CopilotOverrideEngine = "X-Copilot-OverrideEngine",
  CopilotRepository = "X-Copilot-Repository",
  CopilotFileType = "X-Copilot-FileType",
  CopilotUserKind = "X-Copilot-UserKind",
  CopilotDogfood = "X-Copilot-Dogfood"
}

// 目标人群枚举
export enum TargetPopulation {
  Team = "team",
  Internal = "internal",
  Insiders = "insider",
  Public = "public"
}

// 遥测名称映射
export const telmetryNames: { [key in Filter]?: string } = {
  [Filter.CopilotClientTimeBucket]: "timeBucket",
  [Filter.CopilotOverrideEngine]: "engine",
  [Filter.CopilotRepository]: "repo",
  [Filter.CopilotFileType]: "fileType",
  [Filter.CopilotUserKind]: "userKind",
};

// 过滤器设置类
export class FilterSettings {
  filters: { [key in Filter]?: string };

  constructor(filters: { [key in Filter]?: string }) {
    this.filters = filters;
    // 删除空值过滤器
    for (const [key, value] of Object.entries(this.filters)) {
      if (value === "") {
        delete this.filters[key as Filter];
      }
    }
  }

  // 检查是否包含另一个FilterSettings
  extends(other: FilterSettings): boolean {
    for (const [key, value] of Object.entries(other.filters)) {
      if (this.filters[key as Filter] !== value) return false;
    }
    return true;
  }

  // 添加遥测数据
  addToTelemetry(telemetry: { properties: { [key: string]: string } }): void {
    for (const [key, value] of Object.entries(this.filters)) {
      const telemetryKey = telmetryNames[key as Filter];
      if (telemetryKey !== undefined) {
        telemetry.properties[telemetryKey] = value;
      }
    }
  }

  // 将过滤器转换为字符串
  stringify(): string {
    const keys = Object.keys(this.filters);
    keys.sort();
    return keys.map((key) => `${key}:${this.filters[key as Filter]}`).join(";");
  }

  // 将过滤器转换为头部信息
  toHeaders(): { [key: string]: string } {
    return { ...this.filters };
  }

  // 创建新的FilterSettings并更改指定过滤器
  withChange(key: Filter, value: string): FilterSettings {
    return new FilterSettings({
      ...this.filters,
      [key]: value,
    });
  }
}
