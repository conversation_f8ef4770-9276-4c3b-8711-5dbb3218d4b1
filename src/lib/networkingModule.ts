/**
 * 2279 networkingModule.ts
 * 
 * 该模块提供了网络请求相关的功能，主要用于处理GitHub Copilot的HTTP请求和响应。
 * 
 * 主要功能：
 * 1. 初始化网络模块
 * 2. 提供Fetcher基类和HelixFetcher实现类，用于执行网络请求
 * 3. 实现Response类，用于处理HTTP响应
 * 4. 提供postRequest函数，用于执行POST请求
 * 5. 支持代理设置和错误处理
 * 6. 集成了遥测功能，用于监控和分析网络操作
 * 
 * 该模块是GitHub Copilot插件的核心组件之一，负责与后端服务进行通信，
 * 确保数据的安全传输和高效处理。
 */

import {AbortError, AbortController, context as calContext} from '@adobe/helix-fetch';
import * as httpOverHttp from 'tunnel';
import * as util from 'util';
import * as configManager from './configurationManager';
import * as ghostTextDebounce from './ghostTextDebounceManager';
import { My_Context } from './contextType';
import * as events from  "events";
import { logger } from './loggerManager';

//import * as telemetryManager from './telemetryManager';

let networkingVersion: string;
let isInitialized: boolean = false;

/**
 * 初始化网络模块
 * @param newVersion - 新的版本号
 * @throws {Error} 如果重新初始化时版本不匹配
 */
export function init(newVersion: string): void {
  if (isInitialized) {
    if (newVersion !== networkingVersion)
      throw new Error(
        `Networking re-initialized with mismatched version (old: ${networkingVersion}, new: ${newVersion})`
      );
  } else {
    networkingVersion = newVersion;
    isInitialized = true;
  }
}

/**
 * 基础Fetcher类
 */
export abstract class Fetcher {
  abstract fetch(url: string, options: any): Promise<Response>;
  abstract disconnectAll(): Promise<any>;
  abstract makeAbortController(): AbortController;
}

/**
 * 检查错误是否为中止错误
 * @param error - 要检查的错误
 * @returns 是否为中止错误
 */
export function isAbortError(error: Error): boolean {
  return error instanceof AbortError;
}

/**
 * HelixFetcher类，继承自Fetcher
 * 实现了具体的网络请求功能，支持代理设置
 */
export class HelixFetcher extends Fetcher {
  private ctx: My_Context;
  private _proxySettings: any;
  private fetchApi: any;
  private createSocketFactory: (proxySettings: any) => (options: any) => Promise<any>;

  /**
   * 构造函数
   * @param context - 上下文对象
   */
  constructor(context: My_Context) {
    super();
    this.ctx = context;
    this.createSocketFactory = (proxySettings) => {
      const httpOverHttpAgent = httpOverHttp.httpOverHttp({
        proxy: proxySettings,
      });
      return (options) => this.createSocket(httpOverHttpAgent, options);
    };
    this.fetchApi = this.createFetchApi(context);
  }

  /**
   * TOTO
   * 创建Socket
   * @param httpOverHttpAgent - HTTP代理代理
   * @param options - Socket选项
   * @returns Promise<any> - 解析为创建的Socket
   */
  private createSocket(httpOverHttpAgent: any, options: any): Promise<any> {
    return new Promise((resolve, reject) => {
      httpOverHttpAgent.createSocket(options, (error: Error | null, socket: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(socket);
        }
      });
    });
  }

  /**
   * 设置代理设置
   */
  set proxySettings(settings: any) {
    this._proxySettings = settings;
    this.fetchApi = this.createFetchApi(this.ctx);
  }

  /**
   * 获取代理设置
   */
  get proxySettings(): any {
    return this._proxySettings;
  }

  /**
   * 创建FetchAPI
   * @param context - 上下文对象
   * @returns FetchAPI对象
   */
  private createFetchApi(context: My_Context): any {
    const buildInfo = context.get(configManager.BuildInfo);
    if (this._proxySettings?.rejectUnauthorized === false) {
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";
    }
    return calContext({
      userAgent: `GithubCopilot/${buildInfo.getVersion()}`,
      socketFactory: this._proxySettings
        ? this.createSocketFactory(this._proxySettings)
        : undefined,
    });
  }

  /**
   * 执行fetch请求
   * @param url - 请求URL
   * @param options - 请求选项
   * @returns 响应对象
   */
  async fetch(url: string, options: any): Promise<Response> {
    const fetchOptions = {
      ...options,
      body: options.body ? options.body : options.json,
      signal: options.signal,
    };
    const response = await this.fetchApi.fetch(url, fetchOptions);
    return new Response(
      response.status,
      response.statusText,
      response.headers,
      () => response.text(),
      () => response.json(),
      async () => response.body
    );
  }

  /**
   * 断开所有连接
   * @returns 重置结果
   */
  disconnectAll(): Promise<any> {
    return this.fetchApi.reset();
  }

  /**
   * 创建AbortController
   * @returns AbortController实例
   */
  makeAbortController(): AbortController {
    return new AbortController();
  }
}

/**
 * Response类，表示HTTP响应
 */
export class Response {
  public ok: boolean;

  /**
   * 构造函数
   * @param status - 状态码
   * @param statusText - 状态文本
   * @param headers - 响应头
   * @param getText - 获取文本内容的函数
   * @param getJson - 获取JSON内容的函数
   * @param getBody - 获取响应体的函数
   */
  constructor(
    public status: number,
    public statusText: string,
    public headers: any,
    private getText: () => Promise<string>,
    private getJson: () => Promise<any>,
    private getBody: () => Promise<any>
  ) {
    this.ok = this.status >= 200 && this.status < 300;
  }

  /**
   * 获取响应文本
   * @returns 响应文本
   */
  async text(): Promise<string> {
    return this.getText();
  }

  /**
   * 获取响应JSON
   * @returns 响应JSON
   */
  async json(): Promise<any> {
    return this.getJson();
  }

  /**
   * 获取响应体
   * @returns 响应体
   */
  async body(): Promise<any> {
    return this.getBody();
  }
}

/**
 * 执行POST请求
 * @param context - 上下文对象
 * @param url - 请求URL
 * @param token - 认证令牌
 * @param intent - OpenAI意图
 * @param requestId - 请求ID
 * @param jsonBody - 请求体JSON
 * @param cancellationToken - 取消令牌
 * @returns 响应对象
 * @throws {Error} 如果网络未初始化
 */
export async function postRequest(
  context: any,
  url: string,
  token: string,
  intent: string,
  requestId: string,
  jsonBody: any,
  cancellationToken?: any
): Promise<Response> {
  if (!isInitialized){
    throw new Error("Networking must be initialized before being used");
  } 

  const headers: Record<string, string> = {
    Authorization: util.format("Bearer %s", token),
    "X-Request-Id": requestId,
    "Openai-Organization": "github-copilot",
    "VScode-SessionId": context.get(configManager.VscInfo).sessionId,
    "VScode-MachineId": context.get(configManager.VscInfo).machineId,
    ...configManager.editorVersionHeaders(context),
  };

  if (intent) {
    headers["OpenAI-Intent"] = intent;
  }

  const forceDelayMs = context.get(ghostTextDebounce.GhostTextDebounceManager).forceDelayMs;
  if (forceDelayMs) {
    headers["X-Copilot-Force-Delay"] = forceDelayMs.toString();
  }

  const fetchOptions: any = {
    method: "POST",
    //headers: headers,
    json: jsonBody,
    timeout: 30000,
  };

  const fetcher = context.get(Fetcher);

  if (cancellationToken) {
    const abortControllerInstance = fetcher.makeAbortController();
    cancellationToken.onCancellationRequested(() => {
      // telemetryManager.telemetry(
      //   context,
      //   "networking.cancelRequest",
      //   telemetryManager.TelemetryData.createAndMarkAsIssued({
      //     headerRequestId: requestId,
      //   })
      // );
      abortControllerInstance.abort();
    });
    fetchOptions.signal = abortControllerInstance.signal;
  }
  logger.debug(context, "FetchOptions:"+JSON.stringify(fetchOptions));
  return fetcher.fetch(url, fetchOptions).catch((error: any) => {
    if (
      error.code === "ECONNRESET" ||
      error.code === "ETIMEDOUT" ||
      error.code === "ERR_HTTP2_INVALID_SESSION" ||
      error.message === "ERR_HTTP2_GOAWAY_SESSION"
    ) {
      //telemetryManager.telemetry(context, "networking.disconnectAll");
      return fetcher.disconnectAll().then(() => fetcher.fetch(url, fetchOptions));
    }
    throw error;
  });
}
