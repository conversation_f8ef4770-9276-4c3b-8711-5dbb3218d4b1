/**
 * 70
 * 文件名: runtimeModeManager.ts
 * 描述: 该文件定义了运行时模式和标志的管理系统。它包含 RuntimeFlags 接口和 RuntimeMode 类，
 * 以及用于检查各种运行时设置（如调试模式、详细日志记录、测试模式和输入记录）的实用函数。
 * 这个模块主要用于管理和控制应用程序的运行时行为和配置。
 */

import {My_Context} from './contextType'

// 定义 RuntimeFlags 接口
interface RuntimeFlags {
  debug: boolean;
  verboseLogging: boolean;
  testMode: boolean;
  recordInput: boolean;
}

// 定义 RuntimeMode 类
export class RuntimeMode {
  constructor(public flags: RuntimeFlags) {}

  // 从环境变量创建 RuntimeMode 实例
  static fromEnvironment(testMode: boolean): RuntimeMode {
    return new RuntimeMode({
      debug: isDebugEnabled2(process.argv, process.env),
      verboseLogging: isVerboseLoggingEnabledFromEnv(process.env),
      testMode: testMode,
      recordInput: isRecordInputEnabled(process.argv, process.env),
    });
  }
}

// 检查是否在测试模式下运行
export function isRunningInTest(context: My_Context): boolean {
  return context.get(RuntimeMode).flags.testMode;
}

// 检查是否启用详细日志
function isVerboseLoggingEnabledFromEnv(env: NodeJS.ProcessEnv): boolean {
  if ("COPILOT_AGENT_VERBOSE" in env) {
    const verboseFlag = env.COPILOT_AGENT_VERBOSE;
    return verboseFlag === "1" || verboseFlag === "true";
  }
  return false;
}

// 检查是否启用记录输入
function isRecordInputEnabled(argv: string[], env: NodeJS.ProcessEnv): boolean {
  return (
    argv.includes("--record") ||
    env.GITHUB_COPILOT_RECORD?.toLowerCase() === "true"
  );
}

// 检查是否启用调试模式
function isDebugEnabled2(argv: string[], env: NodeJS.ProcessEnv): boolean {
  return (
    argv.includes("--debug") ||
    env.GITHUB_COPILOT_DEBUG?.toLowerCase() === "true"
  );
}

// 用于调试目的的失败检查
export function shouldFailForDebugPurposes(context: My_Context): boolean {
  return isRunningInTest(context);
}

// 检查是否启用调试模式
export function isDebugEnabled(context: My_Context): boolean {
  return context.get(RuntimeMode).flags.debug;
}

// 检查是否启用详细日志
export function isVerboseLoggingEnabled(context: My_Context): boolean {
  return context.get(RuntimeMode).flags.verboseLogging;
}
