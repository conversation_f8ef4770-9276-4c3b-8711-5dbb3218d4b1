/**
 * 3076  cacheUtils.ts
 * 
 * 此模块提供缓存实用函数，包括提示的键生成函数和LRU（最近最少使用）缓存实现。
 * 它用于根据使用模式高效地存储和检索数据。
 */

import SHA256 from 'sha256';

/**
 * 提示对象的接口定义
 */
interface Prompt {
  prefix: string;
  suffix: string;
}

/**
 * 使用SHA256哈希为给定提示生成唯一键。
 * @param {Prompt} prompt - 包含前缀和后缀的提示对象。
 * @returns {string} 组合前缀和后缀的SHA256哈希。
 */
export const keyForPrompt = (prompt: Prompt): string => {
  return SHA256(prompt.prefix + prompt.suffix).toString();
};

/**
 * LRU（最近最少使用）缓存实现。
 */
export class LRUCache<T> {
  private values: Map<string, T>;
  private lruKeys: string[];
  private size: number;

  /**
   * 创建LRUCache的实例。
   * @param {number} maxSize - 缓存中存储的最大项目数。
   */
  constructor(maxSize: number = 10) {
    this.values = new Map<string, T>();
    this.lruKeys = [];
    this.size = maxSize;
  }

  /**
   * 从LRU跟踪数组中删除键。
   * @param {string} key - 要删除的键。
   */
  private removeKeyFromLRU(key: string): void {
    const index = this.lruKeys.indexOf(key);
    if (index !== -1) {
      this.lruKeys.splice(index, 1);
    }
  }

  /**
   * 更新LRU跟踪数组中键的位置。
   * @param {string} key - 要更新的键。
   */
  private touchKeyInLRU(key: string): void {
    this.removeKeyFromLRU(key);
    this.lruKeys.push(key);
  }

  /**
   * 清除缓存中的所有项目。
   */
  public clear(): void {
    this.values.clear();
    this.lruKeys = [];
  }

  /**
   * 从缓存中删除特定键。
   * @param {string} key - 要删除的键。
   */
  public deleteKey(key: string): void {
    this.removeKeyFromLRU(key);
    if (this.values.get(key) !== undefined) {
      this.values.delete(key);
    }
  }

  /**
   * 从缓存中检索值。
   * @param {string} key - 要检索的键。
   * @returns {T | undefined} 与键关联的值，如果未找到则为undefined。
   */
  public get(key: string): T | undefined {
    if (this.values.has(key)) {
      const value = this.values.get(key);
      this.touchKeyInLRU(key);
      return value;
    }
    return undefined;
  }

  /**
   * 在缓存中添加或更新键值对。
   * @param {string} key - 要添加或更新的键。
   * @param {T} value - 要与键关联的值。
   */
  public put(key: string, value: T): void {
    let keysToRemove: string[] = [];
    if (this.values.has(key)) {
      keysToRemove = [key];
    } else {
      if (this.lruKeys.length >= this.size) {
        keysToRemove = this.lruKeys.splice(0, 1);
      }
    }
    for (const keyToRemove of keysToRemove) {
      this.deleteKey(keyToRemove);
    }
    this.values.set(key, value);
    this.touchKeyInLRU(key);
  }
}
