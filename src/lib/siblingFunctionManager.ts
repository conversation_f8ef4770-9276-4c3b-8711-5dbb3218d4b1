/**
 * 3055670  siblingFunctionManager
 * 兄弟函数管理器
 * 
 * 该模块提供了用于处理源代码中兄弟函数的功能。
 * 主要包括两个核心功能：
 * 1. 获取指定位置的兄弟函数
 * 2. 获取下一个兄弟函数的起始位置
 * 
 * 这些功能对于代码分析、自动完成和重构工具非常有用，
 * 可以帮助开发者更好地理解和操作代码结构。
 * 
 * @module siblingFunctionManager
 */

import * as promptManager from './promptManager';
import * as languageParserAndQuerier from './languageParserAndQuerier';

// 定义接口
interface GetSiblingFunctionsParams {
  source: string;
  offset: number;
  languageId: string;
}

interface GetSiblingFunctionsResult {
  siblings: string[];
  beforeInsertion: string;
  afterInsertion: string;
}

interface GetSiblingFunctionStartParams {
  source: string;
  offset: number;
  languageId: string;
}

/**
 * 获取兄弟函数
 * 
 * 此方法用于获取给定源代码中指定位置的兄弟函数。
 * 它分析源代码，找到当前位置的祖先节点，然后收集其兄弟函数。
 * 
 * @param params - 输入参数
 * @returns 包含兄弟函数、插入前内容和插入后内容的对象
 */
export async function getSiblingFunctions({
  source,
  offset,
  languageId,
}: GetSiblingFunctionsParams): Promise<GetSiblingFunctionsResult> {
  const siblingFunctions: string[] = [];
  let contentBeforeInsertion = "";
  let contentAfterInsertion = source.substring(0, offset);

  if (languageParserAndQuerier.isSupportedLanguageId(languageId)) {
    const parsedTree = await languageParserAndQuerier.parseTree(languageId, source);
    if(parsedTree){ 
      try {
          let currentIndex = offset;
          // 跳过空白字符
          while (currentIndex >= 0 && /\s/.test(source[currentIndex])) currentIndex--;
          
          const currentNode = parsedTree.rootNode.descendantForIndex(currentIndex);
          const ancestorWithSiblings = languageParserAndQuerier.getAncestorWithSiblingFunctions(languageId, currentNode);

          if (ancestorWithSiblings) {
            const precedingComment = languageParserAndQuerier.getFirstPrecedingComment(ancestorWithSiblings);
            const startIndex = precedingComment?.startIndex ?? ancestorWithSiblings.startIndex;

            // 计算缩进
            let indentationLength = 0;
            let currentChar: string;
            while ((currentChar = source[startIndex - indentationLength - 1]) === " " || currentChar === "\t") {
              indentationLength++;
            }
            const indentation = source.substring(startIndex - indentationLength, startIndex);

            // 收集兄弟函数
            for (let sibling = ancestorWithSiblings.nextSibling; sibling; sibling = sibling.nextSibling) {
              if (languageParserAndQuerier.isFunctionDefinition(languageId, sibling)) {
                const siblingComment = languageParserAndQuerier.getFirstPrecedingComment(sibling);
                const siblingStartIndex = siblingComment?.startIndex ?? sibling.startIndex;

                if (siblingStartIndex < offset) continue;

                const siblingContent = source.substring(siblingStartIndex, sibling.endIndex);
                const formattedSiblingContent = promptManager.newLineEnded(siblingContent) + "\n" + indentation;
                siblingFunctions.push(formattedSiblingContent);
              }
            }

            contentBeforeInsertion = source.substring(0, startIndex);
            contentAfterInsertion = source.substring(startIndex, offset);
          }
        } finally {
          parsedTree.delete();
        }
    }
  }

  return {
    siblings: siblingFunctions,
    beforeInsertion: contentBeforeInsertion,
    afterInsertion: contentAfterInsertion,
  };
}


/**
 * 获取下一个兄弟函数的起始位置
 * 
 * 此方法用于获取给定源代码中指定位置之后的下一个兄弟函数的起始位置。
 * 它分析源代码，找到当前位置的祖先节点，然后查找其下一个兄弟函数。
 * 
 * @param params - 输入参数
 * @returns 下一个兄弟函数的起始位置，如果没有找到则返回原始偏移量
 */
export async function getSiblingFunctionStart({
  source,
  offset,
  languageId,
}: GetSiblingFunctionStartParams): Promise<number> {
  if (languageParserAndQuerier.isSupportedLanguageId(languageId)) {
    const parsedTree = await languageParserAndQuerier.parseTree(languageId, source);
    if(parsedTree){
      try {
        let currentIndex = offset;
        // 跳过空白字符
        while (currentIndex >= 0 && /\s/.test(source[currentIndex])) currentIndex--;

        const currentNode = parsedTree.rootNode.descendantForIndex(currentIndex);
        const ancestorWithSiblings = languageParserAndQuerier.getAncestorWithSiblingFunctions(languageId, currentNode);

        if (ancestorWithSiblings) {
          // 查找下一个兄弟函数
          for (let sibling = ancestorWithSiblings.nextSibling; sibling; sibling = sibling.nextSibling) {
            if (languageParserAndQuerier.isFunctionDefinition(languageId, sibling)) {
              const siblingComment = languageParserAndQuerier.getFirstPrecedingComment(sibling);
              const siblingStartIndex = siblingComment?.startIndex ?? sibling.startIndex;

              if (siblingStartIndex < offset) continue;
              return siblingStartIndex;
            }
          }

          // 如果没有找到下一个兄弟函数，返回当前祖先节点的结束位置
          if (ancestorWithSiblings.endIndex >= offset) return ancestorWithSiblings.endIndex;
        }
      } finally {
        parsedTree.delete();
      }
    }
  }
  // 如果不支持的语言或没有找到兄弟函数，返回原始偏移量
  return offset;
}
