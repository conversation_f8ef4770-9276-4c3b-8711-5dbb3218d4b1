/**
 * 代码补全后处理模块（TypeScript 版本）
 * 
 * 该模块负责处理和过滤Copilot生成的代码补全结果。主要功能包括：
 * 1. 过滤重复的补全内容
 * 2. 检查补全内容是否已经存在于文档中
 * 3. 处理补全内容的格式和截断（如删除多余的闭合标记）
 * 4. 检查后缀匹配情况
 * 
 * 原文件名: 1124.ts
 */

import {Features} from './featureManager';
import * as blockCloseTokenUtils from './workerManager';
//import * as telemetryManager from './telemetryManager';
import * as runtimeModeManager from './runtimeModeManager';
import * as repetitiveFilter from './repetitionDetector';
import  {My_Context,Document,Position} from './contextType';
import {Logger} from './loggerManager';

export interface CompletionItem {
  tokens: string[];
  requestId: string;
  completionText: string;
  choiceIndex: number;
}

// 导出函数
export const postProcessChoice = async function (
  context: My_Context,
  t: any, // 未使用参数
  document: Document,
  cursorPosition: Position,
  completionItem: CompletionItem,
  trimNewlines: boolean,
  logger: Logger
): Promise<CompletionItem | undefined> {
  // 检查是否为重复的补全内容
  if (
    repetitiveFilter.isRepetitive(
      completionItem.tokens,
      await context.get(Features).repetitionFilterMode()
    )
  ) {
    // const telemetryData = telemetryManager.TelemetryData.createAndMarkAsIssued();
    // telemetryData.extendWithRequestId(completionItem.requestId);
    // telemetryManager.telemetry(context, "repetition.detected", telemetryData, true);
    logger.info(context, "Filtered out repetitive solution");
    return undefined;
  }

  // 复制补全项
  const processedCompletionItem: CompletionItem = {
    ...completionItem,
  };

  /**
   * 检查补全内容是否已经存在于文档的后续行中
   */
  if (
    (function (document: Document, position: Position, completionText: string): boolean {
      let lineText = "";
      let lineIndex = position.line + 1;
      // 遍历文档中当前位置之后的行
      for (; lineText === "" && lineIndex < document.lineCount;) {
        lineText = document.lineAt(lineIndex).text.trim();
        if (lineText === completionText.trim()) return true;
        lineIndex++;
      }
      return false;
    })(document, cursorPosition, processedCompletionItem.completionText)
  ) {
    // 如果补全内容已存在于文档中，记录遥测数据并过滤掉
    // const telemetryData = telemetryManager.TelemetryData.createAndMarkAsIssued();
    // telemetryData.extendWithRequestId(completionItem.requestId);
    // telemetryManager.telemetry(context, "completion.alreadyInDocument", telemetryData);
    // telemetryManager.telemetry(
    //   context,
    //   "completion.alreadyInDocument",
    //   telemetryData.extendedBy({
    //     completionTextJson: JSON.stringify(processedCompletionItem.completionText),
    //   }),
    //   true
    // );
    logger.info(context, "Filtered out solution matching next line");
    return undefined;
  }

  /**
   * 处理补全内容的格式，可能会截断多余的闭合标记
   */
  processedCompletionItem.completionText = await (async function (
    context: My_Context,
    document: Document,
    position: Position,
    completionText: string,
    trimNewlines: boolean
  ): Promise<string> {
    let blockCloseToken: string | null;
    if (completionText === "") return completionText;

    // 获取语言对应的块闭合标记（如"}"）
    let closeToken = "}";
    try {
      blockCloseToken = blockCloseTokenUtils.getBlockCloseToken(document.languageId);
      closeToken = blockCloseToken !== null && blockCloseToken !== undefined ? blockCloseToken : "}";
    } catch (error) {
      // 忽略异常，使用默认值
    }

    // 从后向前查找可能需要截断的闭合标记
    let endPosition = completionText.length;
    do {
      const newlinePosition = completionText.lastIndexOf("\n", endPosition - 2) + 1;
      const lastLine = completionText.substring(newlinePosition, endPosition);

      // 如果最后一行是闭合标记，检查是否需要截断
      if (lastLine.trim() === closeToken) {
        for (let lineIndex = position.line; lineIndex < document.lineCount; lineIndex++) {
          let documentLine = document.lineAt(lineIndex).text;
          if (lineIndex === position.line) {
            documentLine = documentLine.substr(position.character);
          }
          // 如果文档中已有相同的闭合标记行，则截断补全内容
          if (documentLine.startsWith(lastLine.trimRight()))
            return completionText.substring(0, Math.max(0, trimNewlines ? newlinePosition : newlinePosition - 1));
          if (documentLine.trim() !== "") break;
        }
        break;
      }

      // 防止无限循环
      if (endPosition === newlinePosition) {
        if (runtimeModeManager.shouldFailForDebugPurposes(context))
          throw Error(
            `Aborting: maybeSnipCompletion would have looped on completion: ${completionText}`
          );
        break;
      }
      endPosition = newlinePosition;
    } while (endPosition > 1);

    return completionText;
  })(context, document, cursorPosition, processedCompletionItem.completionText, trimNewlines);

  return processedCompletionItem.completionText ? processedCompletionItem : undefined;
};

/**
 * 检查补全内容是否与当前行的后缀匹配
 * 
 * 该函数检查文档中当前位置后面的文本（后缀）是否与补全内容匹配。
 * 匹配有两种情况：
 * 1. 后缀完全包含在补全内容中
 * 2. 后缀中的字符按顺序出现在补全内容中（子序列匹配）
 */
export const checkSuffix = function (
  document: Document,
  position: Position,
  completionItem: CompletionItem
): boolean {
  // 获取当前位置后面的文本（后缀）
  const suffixText: string = document.lineAt(position.line).text.substring(position.character);
  if (suffixText.length > 0) {
    // 检查后缀是否完全包含在补全内容中
    if (completionItem.completionText.indexOf(suffixText) !== -1) return true;
    {
      // 检查后缀是否是补全内容的子序列
      let lastFoundIndex = 0;
      for (const char of suffixText) {
        const foundIndex = completionItem.completionText.indexOf(char, lastFoundIndex + 1);
        if (!(foundIndex > lastFoundIndex)) {
          lastFoundIndex = -1;
          break;
        }
        lastFoundIndex = foundIndex;
      }
      return lastFoundIndex !== -1;
    }
  }
  return false;
};
