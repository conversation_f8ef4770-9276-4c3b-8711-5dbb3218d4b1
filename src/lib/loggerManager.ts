/**
 * 9899
 * 日志管理模块 (loggerManager.ts)
 * 
 * 该模块提供了一个灵活的日志系统，用于在GitHub Copilot项目中进行日志记录和管理。
 * 它支持不同的日志级别、多种日志目标，以及可配置的日志过滤和格式化。
 * 
 * 主要功能：
 * 1. 定义日志级别（DEBUG, INFO, WARN, ERROR）
 * 2. 提供不同的日志目标（控制台、输出通道、多目标）
 * 3. 支持verbose日志记录
 * 4. 可配置的日志过滤和级别覆盖
 * 5. 集成遥测错误报告
 */

import { Clock } from "./clock";
import { getConfig, ConfigKey, isProduction } from "./configurationManager";
import { My_Context } from "./contextType";

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export class LogVerbose {
  constructor(public logVerbose: boolean) {}
}

export function verboseLogging(context: My_Context): boolean {
  return context.get(LogVerbose).logVerbose;
}

export abstract class LogTarget {
  abstract logIt(context: My_Context, level: LogLevel, message: string, ...args: any[]): void;
  shouldLog(context: My_Context, level: LogLevel): boolean | undefined {
    return undefined;
  }
}

export class ConsoleLog extends LogTarget {
  constructor(private console: Console) {
    super();
  }

  logIt(context: My_Context, level: LogLevel, message: string, ...args: any[]): void {
    if (verboseLogging(context) || level == LogLevel.ERROR) {
      this.console.error(message, ...args);
    } else if (level == LogLevel.WARN) {
      this.console.warn(message, ...args);
    }
  }
}

export class OutputChannelLog extends LogTarget {
  constructor(private output: { appendLine: (message: string) => void }) {
    super();
  }

  logIt(context: My_Context, level: LogLevel, message: string, ...args: any[]): void {
    this.output.appendLine(`${message} ${args.map(toPlainText)}`);
  }
}

export class MultiLog extends LogTarget {
  constructor(private targets: LogTarget[]) {
    super();
  }

  logIt(context: My_Context, level: LogLevel, message: string, ...args: any[]): void {
    this.targets.forEach((target) => target.logIt(context, level, message, ...args));
  }
}

export class Logger {
  constructor(private minLoggedLevel: LogLevel, private context: string) {}

  setLevel(newLevel: LogLevel): void {
    this.minLoggedLevel = newLevel;
  }

  private stringToLevel(levelString: string): LogLevel {
    return LogLevel[levelString as keyof typeof LogLevel];
  }

  private log(context: My_Context, level: LogLevel, isSecure: boolean, ...args: any[]): void {
    const logLevelString = LogLevel[level];
    if (level == LogLevel.ERROR) {
      // 发送遥测数据
      // 这里需要实现遥测数据发送逻辑
    }

    const logTarget = context.get(LogTarget);
    // const shouldLogResult = logTarget.shxuldLog(context, level);
    // if (shouldLogResult === false) return;
    // if (shouldLogResult === undefined && !this.shouldLog(context, level, this.context)) return;
    // debugger
    const timestamp = context.get(Clock).now().toISOString();
    const logPrefix = `[${logLevelString}] [${this.context}] [${timestamp}]`;
    logTarget.logIt(context, level, logPrefix, ...args);
  }

  private shouldLog(context: My_Context, level: LogLevel, category: string): boolean {
    if (verboseLogging(context)) return true;

    const debugFilterCategories = getConfig(context, ConfigKey.DebugFilterLogCategories);
    if (debugFilterCategories.length > 0 && !debugFilterCategories.includes(category)) return false;

    if (isProduction(context)) return level >= this.minLoggedLevel;

    const debugOverrideLevels = getConfig(context, ConfigKey.DebugOverrideLogLevels);
    const globalOverride = this.stringToLevel(debugOverrideLevels["*"]);
    const categoryOverride = this.stringToLevel(debugOverrideLevels[this.context]);

    return level >= (categoryOverride ?? globalOverride ?? this.minLoggedLevel);
  }

  debug(context: My_Context, ...args: any[]): void {
    this.log(context, LogLevel.DEBUG, false, ...args);
  }

  info(context: My_Context, ...args: any[]): void {
    this.log(context, LogLevel.INFO, false, ...args);
  }

  warn(context: My_Context, ...args: any[]): void {
    this.log(context, LogLevel.WARN, false, ...args);
  }

  error(context: My_Context, ...args: any[]): void {
    this.log(context, LogLevel.ERROR, false, ...args);
  }

  secureError(context: My_Context, message: string, ...args: any[]): void {
    this.log(context, LogLevel.ERROR, false, message);
    this.log(context, LogLevel.ERROR, true, message, ...args);
  }
}

export function toPlainText(value: any): string {
  return typeof value === "object" ? JSON.stringify(value) : String(value);
}

export const logger = new Logger(LogLevel.INFO, "default");
