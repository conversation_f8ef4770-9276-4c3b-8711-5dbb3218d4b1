/**
 * @file 7727 indentationUtils.ts
 * @description 此模块提供了一系列用于处理代码缩进、块结构和上下文分析的实用函数。
 * 主要功能包括：
 * 1. 检测空块和块体的开始和结束
 * 2. 分析文本的缩进结构
 * 3. 处理代码补全时的缩进逻辑
 * 4. 提供上下文感知的缩进信息
 * 
 * 这些功能对于代码编辑器的智能缩进、自动完成和语法分析等功能非常重要。
 */

import { LocationFactory } from './locationFactory';
import * as blockUtils from './workerManager';
import  { Document ,Position,My_Context,IndentationInfo} from './contextType';




// 检查给定位置是否为空块的开始
export const isEmptyBlockStart = (document: Document, position: Position): Promise<boolean> => {
  return blockUtils.isEmptyBlockStart(document.languageId, document.getText(), document.offsetAt(position));
};

// 检查块体是否已完成
export const isBlockBodyFinished = (context: My_Context, document: Document, position: Position, prefix: string): Promise<number | undefined>  => {
  const locFactory: LocationFactory = context.get(LocationFactory);
  const text = document.getText(locFactory.range(locFactory.position(0, 0), position));
  const offset = document.offsetAt(position);
  return blockUtils.isBlockBodyFinished(document.languageId, text, prefix, offset);
};

// 检查带有附加文本的块体是否已完成
export const isBlockBodyFinishedWithPrefix = (context: My_Context, document: Document, position: Position, prefix: string, additionalText: string): Promise<number | undefined> => {
  const locFactory: LocationFactory = context.get(LocationFactory);
  const text = document.getText(locFactory.range(locFactory.position(0, 0), position));
  const offset = document.offsetAt(position);
  return blockUtils.isBlockBodyFinished(document.languageId, text + additionalText, prefix, offset + additionalText.length);
};

// 异步获取节点的起始位置
export const getNodeStart = async (context: My_Context, document: Document, position: Position, prefix: string): Promise<Position | undefined> => {
  const locFactory: LocationFactory = context.get(LocationFactory);
  const text = document.getText(locFactory.range(locFactory.position(0, 0), position)) + prefix;
  const nodeStart = await blockUtils.getNodeStart(document.languageId, text, document.offsetAt(position));
  if (nodeStart) return document.positionAt(nodeStart);
};

const blockStartTokens: string[] = ["\\{", "\\}", "\\[", "\\]", "\\(", "\\)"].concat(
  [
    "then",
    "else",
    "elseif",
    "elif",
    "catch",
    "finally",
    "fi",
    "done",
    "end",
    "loop",
    "until",
    "where",
    "when",
  ].map((e) => e + "\\b")
);

const blockStartRegex = new RegExp(`^(${blockStartTokens.join("|")})`);

// 判断给定行是否为块的开始
function isBlockStart(line: string): boolean {
  return blockStartRegex.test(line.trimLeft().toLowerCase());
}

// 获取给定行的缩进级别
function getIndentation(line: string): number | undefined {
  const match = /^(\s*)([^]*)$/.exec(line);
  return match && match[2] && match[2].length > 0 ? match[1].length : undefined;
}

// 从文本中获取上下文缩进信息
function contextIndentationFromText(text: string, offset: number, languageId: string): IndentationInfo {
  const beforeLines = text.slice(0, offset).split("\n");
  const afterLines = text.slice(offset).split("\n");

  function findIndentation(lines: string[], startIndex: number, direction: number): [number | undefined, number | undefined] {
    let indentation: number | undefined;
    let lineIndex: number | undefined;
    let currentIndex = startIndex;

    for (; undefined === indentation && currentIndex >= 0 && currentIndex < lines.length; ) {
      indentation = getIndentation(lines[currentIndex]);
      lineIndex = currentIndex;
      currentIndex += direction;
    }

    if ("python" === languageId && -1 === direction) {
      currentIndex++;
      const line = lines[currentIndex].trim();
      if (line.endsWith('"""')) {
        if (!line.startsWith('"""') || '"""' === line)
          for (currentIndex--; currentIndex >= 0 && !lines[currentIndex].trim().startsWith('"""'); ) currentIndex--;
        if (currentIndex >= 0)
          for (indentation = undefined, currentIndex--; undefined === indentation && currentIndex >= 0; ) {
            indentation = getIndentation(lines[currentIndex]);
            lineIndex = currentIndex;
            currentIndex--;
          }
      }
    }

    return [indentation, lineIndex];
  }

  const [currentIndentation, currentLineIndex] = findIndentation(beforeLines, beforeLines.length - 1, -1);
  const previousIndentation = (() => {
    if (undefined !== currentIndentation && undefined !== currentLineIndex)
      for (let i = currentLineIndex - 1; i >= 0; i--) {
        const indent = getIndentation(beforeLines[i]);
        if (undefined !== indent && indent < currentIndentation) return indent;
      }
  })();

  const [nextIndentation] = findIndentation(afterLines, 1, 1);

  return {
    prev: previousIndentation,
    current: currentIndentation ?? 0,
    next: nextIndentation,
  };
}

// 决定是否截断或继续补全
function completionCutOrContinue(completion: string, indentation: IndentationInfo, prefix?: string): number | "continue" {
  const lines = completion.split("\n");
  const hasPrefix = prefix !== undefined;
  const lastPrefixLine = prefix?.split("\n").pop();
  let startLine = 0;

  if (hasPrefix && lastPrefixLine?.trim() !== "" && lines[0].trim() !== "") {
    startLine++;
  }

  if (hasPrefix) {
    startLine++;
  }

  if (lines.length === startLine) return "continue";

  const targetIndentation = Math.max(
    indentation.current,
    indentation.next ?? 0
  );

  for (let i = startLine; i < lines.length; i++) {
    let line = lines[i];
    if (i === 0 && lastPrefixLine !== undefined) {
      line = lastPrefixLine + line;
    }
    const lineIndentation = getIndentation(line);
    if (lineIndentation !== undefined && (lineIndentation < targetIndentation || (lineIndentation === targetIndentation && !isBlockStart(line))))
      return lines.slice(0, i).join("\n").length;
  }

  return "continue";
}

// 获取文档中给定位置的上下文缩进
export const contextIndentation = (document: Document, position: Position): IndentationInfo => {
  return contextIndentationFromText(document.getText(), document.offsetAt(position), document.languageId);
};

export { contextIndentationFromText, completionCutOrContinue };

// 检查缩进块是否已完成
export const indentationBlockFinished = (indentation: IndentationInfo, prefix: string | undefined) => {
  return async (completion: string): Promise<number | undefined> => {
    const result = completionCutOrContinue(completion, indentation, prefix);
    return result === "continue" ? undefined : result;
  };
};
