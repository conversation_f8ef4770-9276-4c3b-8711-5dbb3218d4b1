
/**
 * 9748
 * 实验配置制造模块
 * 
 * 该模块定义了用于创建和获取实验配置的抽象类和具体实现。
 * 主要功能包括：
 * 1. 定义实验配置制造的抽象基类 (ExpConfigMaker)
 * 2. 提供一个返回空配置的具体实现 (ExpConfigNone)
 * 
 * 这个模块在 Copilot 扩展中用于管理不同类型的实验配置，
 * 支持灵活的实验设置和空配置场景。
 */

// 导入所需模块
import { ExpConfig } from './experimentConfig';

// 定义基础实验配置制造类
export abstract class ExpConfigMaker {
  abstract fetchExperiments(e: any, t: any): Promise<ExpConfig>;
}

// 空实验配置类
export class ExpConfigNone extends ExpConfigMaker {
  async fetchExperiments(e: any, t: any): Promise<ExpConfig> {
    // 返回空配置
    return ExpConfig.createEmptyConfig();
  }
}
