/**
 * 937 completionUtils.ts
 * 
 * 该模块提供了一系列与 OpenAI 完成功能相关的实用函数和常量。
 * 
 * 主要功能：
 * 1. 计算平均对数概率和平均替代对数概率
 * 2. 将完成结果转换为 API 选择格式
 * 3. 清理和缩进选择结果
 * 4. 根据样本数量获取温度值
 * 5. 导出 OpenAI 相关的常量和函数
 * 
 * 该模块是 Copilot 扩展的核心组件之一，用于处理和优化 AI 生成的代码补全结果。
 */

import * as configManager from './configurationManager';
import * as logger from './loggerManager';
//import * as telemetry from './telemetryManager';
import * as runtimeMode from './runtimeModeManager';
import * as openAIFetcher from './openAIFetcher';

import {My_Context} from './contextType';

// 导出 OpenAI 相关的常量和函数
export const {
  CopilotUiKind,
  getRequestId,
  LiveOpenAIFetcher,
  OpenAIFetcher
} = openAIFetcher;

export const MAX_PROMPT_LENGTH = 1500;
export const DEFAULT_CHARACTER_MULTIPLIER = 3;

// 定义接口
interface LogProbs {
  token_logprobs: number[];
  tokens: string[];
  top_logprobs: { [key: string]: number }[];
}

interface CompletionData {
  logprobs?: LogProbs;
}

interface ModelInfo {
  // 根据实际情况定义 ModelInfo 的属性
}

interface TelemetryData {
  // 根据实际情况定义 TelemetryData 的属性
}

interface APIChoice {
  completionText: string;
  meanLogProb?: number;
  meanAlternativeLogProb?: number;
  choiceIndex: number;
  requestId: string;
  modelInfo: ModelInfo;
  blockFinished: boolean;
  tokens: string[];
  numTokens: number;
  telemetryData: TelemetryData;
}

/**
 * 计算平均对数概率
 * @param context - 上下文信息
 * @param data - 包含logprobs的数据对象
 * @returns 平均对数概率，如果无法计算则返回undefined
 */
export function calculateMeanLogProb(context: My_Context, data: CompletionData): number | undefined {
  const tokenLogProbs = data.logprobs?.token_logprobs;
  if (tokenLogProbs) {
    try {
      let sum = 0;
      let count = 0;
      let limit = 50;
      for (let i = 0; i < tokenLogProbs.length - 1 && limit > 0; i++, limit--) {
        sum += tokenLogProbs[i];
        count += 1;
      }
      return count > 0 ? sum / count : undefined;
    } catch (error) {
      logger.logger.error(context, `Error calculating mean prob: ${error}`);
    }
  }
  return undefined;
}

/**
 * 计算平均替代对数概率
 * @param context - 上下文信息
 * @param data - 包含logprobs的数据对象
 * @returns 平均替代对数概率，如果无法计算则返回undefined
 */
export function calculateMeanAlternativeLogProb(context: My_Context, data: CompletionData): number | undefined {
  const topLogProbs = data.logprobs?.top_logprobs;
  const tokens = data.logprobs?.tokens;
  if (topLogProbs && tokens) {
    try {
      let sum = 0;
      let count = 0;
      let limit = 50;
      for (let i = 0; i < tokens.length - 1 && limit > 0; i++, limit--) {
        const currentTopLogProbs = { ...topLogProbs[i] };
        delete currentTopLogProbs[tokens[i]];
        sum += Math.max(...Object.values(currentTopLogProbs));
        count += 1;
      }
      return count > 0 ? sum / count : undefined;
    } catch (error) {
      logger.logger.error(context, `Error calculating mean prob: ${error}`);
    }
  }
  return undefined;
}

/**
 * 将完成结果转换为API选择格式
 * @param context - 上下文信息
 * @param completionText - 完成的文本
 * @param logprobs - 对数概率信息
 * @param choiceIndex - 选择索引
 * @param requestId - 请求ID
 * @param blockFinished - 块是否完成
 * @param telemetryData - 遥测数据
 * @param modelInfo - 模型信息
 * @returns 转换后的API选择对象
 */
export function convertToAPIChoice(
  context: My_Context,
  completionText: string,
  logprobs: LogProbs,
  choiceIndex: number,
  requestId: any,
  blockFinished: boolean,
  telemetryData: any,
  modelInfo?: any
): APIChoice {
  //telemetry.logEngineCompletion(context, completionText, logprobs, requestId, choiceIndex);
  return {
    completionText,
    meanLogProb: calculateMeanLogProb(context, { logprobs }),
    meanAlternativeLogProb: calculateMeanAlternativeLogProb(context, { logprobs }),
    choiceIndex,
    requestId,
    modelInfo,
    blockFinished,
    tokens: logprobs.tokens,
    numTokens: logprobs.tokens.length,
    telemetryData,
  };
}

/**
 * 清理并缩进选择
 * @param choices - 异步可迭代的选择对象
 * @param indent - 缩进字符串
 * @returns 清理并缩进后的选择对象
 */
export async function* cleanupIndentChoices(
  choices: AsyncIterable<APIChoice>,
  indent: string
): AsyncGenerator<APIChoice> {
  for await (const choice of choices) {
    const cleanedChoice = { ...choice };
    const lines = cleanedChoice.completionText.split("\n");
    for (let i = 0; i < lines.length; ++i) {
      const trimmedLine = lines[i].trimLeft();
      lines[i] = trimmedLine === "" ? trimmedLine : indent + trimmedLine;
    }
    cleanedChoice.completionText = lines.join("\n");
    yield cleanedChoice;
  }
}

/**
 * 根据样本数量获取温度值
 * @param context - 上下文信息
 * @param samples - 样本数量
 * @returns 计算得出的温度值
 */
export function getTemperatureForSamples(context: My_Context, samples: number): number {
  if (runtimeMode.isRunningInTest(context)) return 0;
  const temperature = parseFloat(configManager.getConfig(context, configManager.ConfigKey.Temperature));
  if (temperature >= 0 && temperature <= 1) return temperature;
  if (samples <= 1) return 0;
  if (samples < 10) return 0.2;
  if (samples < 20) return 0.4;
  return 0.8;
}
