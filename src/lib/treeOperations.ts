/**
 * 3055617
 * treeOperations.ts
 * 
 * 这个文件包含了一系列用于操作树结构的工具函数。
 * 主要功能包括：
 * - 树的遍历（深度优先，可选择自顶向下或自底向上）
 * - 节点标签的清除和条件清除
 * - 节点标签的映射
 * - 行号的重置
 * - 条件树遍历
 * - 树的折叠操作
 * - 树的重建
 * 
 * 这些函数主要用于处理抽象语法树（AST）或类似的树形结构，
 * 可以用于代码分析、转换或其他需要操作树形数据的场景。
 */

// 导入 treeUtils 模块
import { Node, isVirtual, isTop, isBlank, isLine, topNode } from './treeNodeTypes';
export { Node };
// 定义遍历顺序类型
type TraversalOrder = 'topDown' | 'bottomUp';

/**
 * 遍历树结构
 * @param rootNode 根节点
 * @param callback 回调函数
 * @param traversalOrder 遍历顺序
 */
function visitTree(rootNode: Node, callback: (node: Node) => void, traversalOrder: TraversalOrder): void {
  (function traverse(node: Node) {
    if (traversalOrder === "topDown") {
      callback(node);
    }
    node.subs.forEach((subNode) => {
      traverse(subNode);
    });
    if (traversalOrder === "bottomUp") {
      callback(node);
    }
  })(rootNode);
}

/**
 * 清除所有节点的标签
 * @param rootNode 根节点
 * @returns 处理后的根节点
 */
export function clearLabels(rootNode: Node): Node {
  visitTree(
    rootNode,
    (node) => {
      if (isVirtual(node) || isLine(node)) {
        node.label = undefined;
      }
    },
    "bottomUp"
  );
  return rootNode;
}

/**
 * 根据条件清除节点的标签
 * @param rootNode 根节点
 * @param condition 条件函数
 * @returns 处理后的根节点
 */
export function clearLabelsIf(rootNode: Node, condition: (label: any) => boolean): Node {
  visitTree(
    rootNode,
    (node) => {
      if (isVirtual(node) || isLine(node)) {
        node.label = node.label ? (condition(node.label) ? undefined : node.label) : undefined;
      }
    },
    "bottomUp"
  );
  return rootNode;
}

/**
 * 映射节点的标签
 * @param node 当前节点
 * @param labelMapper 标签映射函数
 * @returns 处理后的节点
 */
export function mapLabels(node: Node, labelMapper: (label: any) => any): Node {
  switch (node.type) {
    case "line":
    case "virtual":
      const mappedSubs = node.subs.map((subNode) => mapLabels(subNode, labelMapper));
      return {
        ...node,
        subs: mappedSubs,
        label: node.label ? labelMapper(node.label) : undefined,
      };
    case "blank":
      return {
        ...node
      };
    case "top":
      return {
        ...node,
        subs: node.subs.map((subNode) => mapLabels(subNode, labelMapper)),
      };
  }
}

/**
 * 重置行号
 * @param rootNode 根节点
 */
export function resetLineNumbers(rootNode: Node): void {
  let lineCount = 0;
  visitTree(
    rootNode,
    function (node) {
      if (isVirtual(node)) {
        (node as any).lineNumber = lineCount;
        lineCount++;
      } else if (isLine(node) || isBlank(node)) {
        node.lineNumber = lineCount;
        lineCount++;
      }
    },
    "topDown"
  );
}

export { visitTree };

/**
 * 有条件地遍历树
 * @param rootNode 根节点
 * @param condition 条件函数
 * @param traversalOrder 遍历顺序
 */
export function visitTreeConditionally(rootNode: Node, condition: (node: Node) => boolean, traversalOrder: TraversalOrder): void {
  (function traverse(node: Node): boolean {
    if (traversalOrder === "topDown" && !condition(node)) return false;
    let result = true;
    node.subs.forEach((subNode) => {
      result = result && traverse(subNode);
    });
    if (traversalOrder === "bottomUp") {
      result = result && condition(node);
    }
    return result;
  })(rootNode);
}

/**
 * 折叠树结构
 * @param rootNode 根节点
 * @param initialValue 初始值
 * @param foldFunction 折叠函数
 * @param traversalOrder 遍历顺序
 * @returns 折叠结果
 */
export function foldTree<T>(rootNode: Node, initialValue: T, foldFunction: (node: Node, acc: T) => T, traversalOrder: TraversalOrder): T {
  let accumulator = initialValue;
  visitTree(
    rootNode,
    function (node) {
      accumulator = foldFunction(node, accumulator);
    },
    traversalOrder
  );
  return accumulator;
}

/**
 * 重建树结构
 * @param rootNode 根节点
 * @param nodeTransformer 节点转换函数
 * @param skipCondition 跳过条件函数
 * @returns 重建后的树结构
 */
export function rebuildTree(
  rootNode: Node,
  nodeTransformer: (node: Node) => Node | undefined,
  skipCondition?: (node: Node) => boolean
): Node {
  const processNode = (node: Node): Node | undefined => {
    if (skipCondition !== undefined && skipCondition(node)) return node;
    {
      const processedSubs = node.subs.map(processNode).filter((subNode): subNode is Node => subNode !== undefined);
      node.subs = processedSubs;
      return nodeTransformer(node);
    }
  };
  const processedRoot = processNode(rootNode);
  return processedRoot !== undefined ? processedRoot : topNode();
}
