// 导入 './2337' 模块
import r from './2337';

// 定义 ParsedURL 接口，描述解析后的 URL 结构
interface ParsedURL {
  protocols: string[] | any;
  protocol: string | null;
  port: string | null;
  resource: string;
  host: string;
  user: string;
  password: string;
  pathname: string;
  hash: string;
  search: string;
  href: string;
  query: Record<string, string>;
  parse_failed: boolean;
}

// 导出一个函数，接受一个字符串参数并返回 ParsedURL 对象
export default function (e: string): ParsedURL {
  // 初始化 ParsedURL 对象
  const t: ParsedURL = {
    protocols: [],
    protocol: null,
    port: null,
    resource: "",
    host: "",
    user: "",
    password: "",
    pathname: "",
    hash: "",
    search: "",
    href: e,
    query: {},
    parse_failed: false,
  };

  try {
    // 尝试创建 URL 对象
    const n = new URL(e);

    // 解析 URL 各个部分
    t.protocols = r(n);
    t.protocol = t.protocols[0];
    t.port = n.port;
    t.resource = n.hostname;
    t.host = n.host;
    t.user = n.username || "";
    t.password = n.password || "";
    t.pathname = n.pathname;
    t.hash = n.hash.slice(1);
    t.search = n.search.slice(1);
    t.href = n.href;
    t.query = Object.fromEntries(n.searchParams as any);
  } catch (n) {
    // 如果 URL 解析失败，设置默认值
    t.protocols = ["file"];
    t.protocol = t.protocols[0];
    t.port = "";
    t.resource = "";
    t.user = "";
    t.pathname = "";
    t.hash = "";
    t.search = "";
    t.href = e;
    t.query = {};
    t.parse_failed = true;
  }

  // 返回解析后的 URL 对象
  return t;
}
