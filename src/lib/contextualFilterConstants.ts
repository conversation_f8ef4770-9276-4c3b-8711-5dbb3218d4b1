/**
 * contextualFilterConstants.ts
 * 
 * 该文件的主要功能：
 * 1. 定义上下文过滤器相关的常量，包括接受阈值和截距。
 * 2. 提供上下文过滤器权重数组。
 * 3. 定义编程语言到数字的映射。
 * 4. 定义字符到数字的映射。
 * 
 * 这些常量和映射可能用于实现上下文相关的代码补全或建议过滤功能。
 */

// 声明模块为 ES 模块
export {};

// 定义上下文过滤器接受阈值
export const contextualFilterAcceptThreshold: number = 15;

// 定义上下文过滤器截距
export const contextualFilterIntercept: number = -0.3043572714994554;

// 定义上下文过滤器权重数组
export const contextualFilterWeights: number[] = [
  0.9978708359643611, 0.7001905605239328, -0.1736749244124868,
  // ... (省略中间的权重值)
  0.584914157527457, 0, -0.4573883817015294,
];

// 定义上下文过滤器语言映射
export const contextualFilterLanguageMap: { [key: string]: number } = {
  javascript: 1,
  typescript: 2,
  typescriptreact: 3,
  python: 4,
  vue: 5,
  php: 6,
  dart: 7,
  javascriptreact: 8,
  go: 9,
  css: 10,
  cpp: 11,
  html: 12,
  scss: 13,
  markdown: 14,
  csharp: 15,
  java: 16,
  json: 17,
  rust: 18,
  ruby: 19,
  c: 20,
};

// 定义上下文过滤器字符映射
export const contextualFilterCharacterMap: { [key: string]: number } = {
  " ": 1,
  "!": 2,
  '"': 3,
  "#": 4,
  $: 5,
  "%": 6,
  "&": 7,
  "'": 8,
  "(": 9,
  ")": 10,
  "*": 11,
  "+": 12,
  ",": 13,
  "-": 14,
  ".": 15,
  "/": 16,
  0: 17,
  1: 18,
  2: 19,
  3: 20,
  4: 21,
  5: 22,
  6: 23,
  7: 24,
  8: 25,
  9: 26,
  ":": 27,
  ";": 28,
  "<": 29,
  "=": 30,
  ">": 31,
  "?": 32,
  "@": 33,
  A: 34,
  B: 35,
  C: 36,
  D: 37,
  E: 38,
  F: 39,
  G: 40,
  H: 41,
  I: 42,
  J: 43,
  K: 44,
  L: 45,
  M: 46,
  N: 47,
  O: 48,
  P: 49,
  Q: 50,
  R: 51,
  S: 52,
  T: 53,
  U: 54,
  V: 55,
  W: 56,
  X: 57,
  Y: 58,
  Z: 59,
  "[": 60,
  "\\": 61,
  "]": 62,
  "^": 63,
  _: 64,
  "`": 65,
  a: 66,
  b: 67,
  c: 68,
  d: 69,
  e: 70,
  f: 71,
  g: 72,
  h: 73,
  i: 74,
  j: 75,
  k: 76,
  l: 77,
  m: 78,
  n: 79,
  o: 80,
  p: 81,
  q: 82,
  r: 83,
  s: 84,
  t: 85,
  u: 86,
  v: 87,
  w: 88,
  x: 89,
  y: 90,
  z: 91,
  "{": 92,
  "|": 93,
  "}": 94,
  "~": 95,
};
