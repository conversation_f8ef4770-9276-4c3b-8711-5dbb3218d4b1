/**
 * 766 仓库信息管理器 (repoInfoManager.ts)
 *
 * 该模块负责处理Git仓库相关的信息提取和解析功能，主要包括：
 * 1. 从Git配置文件中提取仓库URL
 * 2. 解析仓库URL获取主机名、所有者、仓库名等信息
 * 3. 判断仓库是否属于GitHub或Microsoft
 * 4. 提供缓存机制以提高性能
 * 5. 支持在后台异步提取仓库信息
 */

import * as path from "path";

import {parseGitUrl} from './3458';
//const tokenModule = require('./362');
import * as cacheUtils from './cacheUtils';
import * as  moduleExports from './moduleExports';
import { My_Context } from "./contextType";
// 仓库信息接口定义
export interface RepoInfo {
  baseFolder: string;
  url: string;
  hostname: string;
  owner: string;
  repo: string;
  pathname: string;
}

// RepoUrl解析结果接口
export interface ParsedRepoUrl {
  hostname: string;
  owner: string;
  repo: string;
  pathname: string;
}

// 计算状态枚举，用于异步仓库信息提取状态
export enum ComputationStatus {
  PENDING = 0,
}

// Dogfood枚举定义，用于标识仓库所属的组织类型
export enum Dogfood {
  GITHUB = "github",
  MICROSOFT = "microsoft",
  UNKNOWN = "",
}

/**
 * 结果包装类
 * 用于在缓存中存储结果
 */
class ResultWrapper<T> {
  result: T;
  constructor(result: T) {
    this.result = result;
  }
}

/**
 * 尝试获取GitHub的名称与所有者(Name With Owner)
 * @param repoInfo 仓库信息对象
 * @returns 格式为"owner/repo"的GitHub标识符，如果不是GitHub仓库则返回undefined
 */
export function tryGetGitHubNWO(repoInfo: RepoInfo | ComputationStatus | undefined): string | undefined {
  if (repoInfo !== undefined && repoInfo !== ComputationStatus.PENDING) {
    return repoInfo.hostname === "github.com" ? `${repoInfo.owner}/${repoInfo.repo}` : undefined;
  }
}

/**
 * 判断对象是否为有效的仓库信息
 * @param repoInfo 仓库信息对象
 * @returns 如果是有效的仓库信息则返回true
 */
export function isRepoInfo(repoInfo: RepoInfo | ComputationStatus | undefined): boolean {
  return repoInfo !== undefined && repoInfo !== ComputationStatus.PENDING;
}

/**
 * 判断对象是否表示非仓库
 * @param repoInfo 仓库信息对象
 * @returns 如果不是仓库则返回true
 */
export function isNotRepo(repoInfo: RepoInfo | ComputationStatus | undefined): boolean {
  return repoInfo === undefined;
}

/**
 * 获取用户类型
 * @param context 上下文对象
 * @returns 用户类型标识符
 */
export async function getUserKind(context: any): Promise<string> {
  // // 获取CopilotTokenManager对象
  // const tokenManager = context.get(tokenModule.CopilotTokenManager);
  // // 获取token及组织列表
  // const token: any = await tokenManager.getCopilotToken(context, false);
  // const orgList: string[] = token?.organization_list ?? [];
  // // 检查是否属于指定组织
  // const result = [
  //   "a5db0bcaae94032fe715fb34a5e4bce2",
  //   "4535c7beffc844b46bb1ed4aa04d759a",
  // ].find((org) => orgList.includes(org));
  // return result ?? "";
  return "";
}

/**
 * 获取仓库所属的组织类型
 * @param repoInfo 仓库信息对象
 * @returns 组织类型：github、microsoft或空字符串(未知)
 */
export function getDogFood(repoInfo: RepoInfo | ComputationStatus | undefined): Dogfood {
  if (repoInfo === undefined || repoInfo === ComputationStatus.PENDING) {
    return Dogfood.UNKNOWN;
  }
  if (tryGetGitHubNWO(repoInfo) === "github/github") {
    return Dogfood.GITHUB;
  }
  if (
    repoInfo.hostname === "ssh.dev.azure.com" ||
    repoInfo.hostname === "vs-ssh.visualstudio.com" ||
    repoInfo.hostname === "dev.azure.com" ||
    repoInfo.hostname === "domoreexp.visualstudio.com" ||
    repoInfo.hostname === "office.visualstudio.com"
  ) {
    return Dogfood.MICROSOFT;
  }
  return Dogfood.UNKNOWN;
}

/**
 * 在后台提取仓库信息
 * @param context 上下文对象
 * @param filePath 文件路径
 * @returns 仓库信息或undefined
 */
export function extractRepoInfoInBackground(
  context: any,
  filePath: string
): RepoInfo | ComputationStatus | undefined {
  if (!filePath) return;
  const dirPath = path.dirname(filePath);
  return extractRepoInfoWithCache(context, dirPath);
}

/**
 * 带缓存的仓库信息提取函数
 * 使用LRU缓存提高性能，避免重复提取相同路径的仓库信息
 */
const extractRepoInfoWithCache = (function (
  originalFunction: (context: any, dirPath: string) => Promise<RepoInfo | undefined>
) {
  const cache = new cacheUtils.LRUCache<ResultWrapper<RepoInfo>>(1e4);
  const pendingRequests = new Set<string>();
  return (ctx: any, ...args: [string]): RepoInfo | ComputationStatus => {
    const cacheKey = JSON.stringify(args);
    const cachedResult = cache.get(cacheKey);
    if (cachedResult){
      return cachedResult.result;
    } 
    if (pendingRequests.has(cacheKey)){
      return ComputationStatus.PENDING;
    } 
    const resultPromise = originalFunction(ctx, ...args);
    pendingRequests.add(cacheKey);
    resultPromise.then((result) => {
      cache.put(cacheKey, new ResultWrapper<RepoInfo>(result as any));
      pendingRequests.delete(cacheKey);
    });
    return ComputationStatus.PENDING;
  };
})(extractRepoInfo);

/**
 * 提取仓库信息
 * @param context 上下文对象
 * @param dirPath 目录路径
 * @returns 仓库信息对象或undefined
 */
async function extractRepoInfo(
  context: My_Context,
  dirPath: string
): Promise<RepoInfo | undefined> {
  // 查找包含.git/config文件的仓库根目录
  const repoRoot: string | undefined = await (async function (context: any, startPath: string): Promise<string | undefined> {
    let previousPath = startPath + "_add_to_make_longer";
    const fileSystem = context.get(moduleExports.MyFileSystem);
    while (startPath.length > 1 && startPath.length < previousPath.length) {
      const gitConfigPath = path.join(startPath, ".git", "config");
      let exists = false;
      try {
        await fileSystem.stat(gitConfigPath);
        exists = true;
      } catch (error) {
        exists = false;
      }
      if (exists){
        return startPath;
      } 
      previousPath = startPath;
      startPath = path.dirname(startPath);
    }
    return undefined;
  })(context, dirPath);

  if (!repoRoot) return;

  const fileSystem = context.get(moduleExports.MyFileSystem);
  const gitConfigPath = path.join(repoRoot, ".git", "config");
  const configText: string = (await fileSystem.readFile(gitConfigPath)).toString();

  const repoUrl: string =
    getRepoUrlFromConfigText(configText) ?? "";

    // return {
    //   baseFolder: repoRoot,
    //   url: repoUrl,
    //   hostname: "",
    //   owner: "",
    //   repo: "",
    //   pathname: "",
    // };

  const parsedUrl = parseRepoUrl(repoUrl);

  if (parsedUrl === undefined) {
    return {
      baseFolder: repoRoot,
      url: repoUrl,
      hostname: "",
      owner: "",
      repo: "",
      pathname: "",
    };
  } else {
    return {
      baseFolder: repoRoot,
      url: repoUrl,
      ...parsedUrl,
    };
  }
}

/**
 * 解析仓库URL
 * @param url 仓库URL
 * @returns 解析后的URL对象或undefined
 */
export function parseRepoUrl(url: string): ParsedRepoUrl | undefined {
  let parsedUrl: any = {};
  try {
    parsedUrl = parseGitUrl(url);
    if (
      !parsedUrl.host ||
      !parsedUrl.owner ||
      !parsedUrl.name ||
      !parsedUrl.pathname
    )
      return;
  } catch (error) {
    return;
  }
  return {
    hostname: parsedUrl.host,
    owner: parsedUrl.owner,
    repo: parsedUrl.name,
    pathname: parsedUrl.pathname,
  };
}

/**
 * 从Git配置文本中提取仓库URL
 * @param configText Git配置文件内容
 * @returns 仓库URL或undefined
 */
export function getRepoUrlFromConfigText(configText: string): string | undefined {
  const remoteQuotedPattern = /^\s*\[\s*remote\s+"((\\\\|\\"|[^\\"])+)"/;
  const remoteUnquotedPattern = /^\s*\[remote.([^"\s]+)/;
  const urlPattern = /^\s*url\s*=\s*([^\s#;]+)/;
  const sectionStartPattern = /^\s*\[/;
  let repoUrl: string | undefined;
  let remoteName: string | undefined;
  let multiline = false;

  for (const line of configText.split("\n")) {
    if (multiline && repoUrl !== undefined) {
      repoUrl += line;
      if (line.endsWith("\\")) repoUrl = repoUrl.substring(0, repoUrl.length - 1);
      else if ((multiline = false), remoteName === "origin") return repoUrl;
    } else {
      const remoteMatch =
        line.match(remoteQuotedPattern) ?? line.match(remoteUnquotedPattern);
      if (remoteMatch) remoteName = remoteMatch[1];
      else if (line.match(sectionStartPattern)) remoteName = undefined;
      else {
        if (repoUrl && remoteName !== "origin") continue;
        const urlMatch = line.match(urlPattern);
        if (urlMatch) {
          repoUrl = urlMatch[1];
          if (repoUrl.endsWith("\\")) {
            repoUrl = repoUrl.substring(0, repoUrl.length - 1);
            multiline = true;
          } else if (remoteName === "origin") return repoUrl;
        }
      }
    }
  }
  return repoUrl;
}

/**
 * 用于测试的仓库信息提取函数
 * @param context 上下文对象
 * @param dirPath 目录路径
 * @returns 仓库信息对象或undefined
 */
export async function extractRepoInfoForTesting(
  context: any,
  dirPath: string
): Promise<RepoInfo | undefined> {
  return extractRepoInfo(context, dirPath);
}
