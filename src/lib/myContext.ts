// 定义 Context 类的接口
interface IContext {
  baseContext?: IContext;
  constructionStack: string[];
  instances: Map<any, any>;
  get(e: any): any;
  tryGet(e: any): any | undefined;
  set(e: any, t: any): void;
  forceSet(e: any, t: any): void;
  toString(): string;
  debug: Record<string, any>;
}

// 导出 Context 类
export class MyContext implements IContext {
  baseContext?: IContext;
  constructionStack: string[];
  instances: Map<any, any>;

  constructor(e?: IContext) {
    this.baseContext = e;
    this.constructionStack = [];
    this.instances = new Map();

    // 获取当前调用栈
    const stackTrace = new Error().stack?.split("\n");
    if (stackTrace) {
      this.constructionStack.push(...stackTrace.slice(1));
    }
  }

  // 获取实例，如果不存在则抛出错误
  get(e: any): typeof e {
    const t = this.tryGet(e);
    if (t) return t as typeof e;
    throw new Error(`No instance of ${e.name} has been registered.\n${this}`);
  }

  // 尝试获取实例，如果不存在则返回 undefined
  tryGet(e: any): any | undefined {
    return (
      this.instances.get(e) ||
      (this.baseContext ? this.baseContext.tryGet(e) : undefined)
    );
  }

  // 设置实例，如果已存在则抛出错误
  set(e: any, t: any): void {
    if (this.tryGet(e))
      throw new Error(
        `An instance of ${e.name} has already been registered. Use forceSet() if you're sure it's a good idea.`
      );
    this.instances.set(e, t);
  }

  // 强制设置实例，无论是否已存在
  forceSet(e: any, t: any): void {
    this.instances.set(e, t);
  }

  // 返回上下文的字符串表示
  toString(): string {
    let n = "    Context created at:\n";
    for (const e of this.constructionStack || []) n += `    ${e}\n`;
    n += this.baseContext?.toString() ?? "";
    return n;
  }

  // 返回调试信息
  get debug(): Record<string, any> {
    const e: Record<string, any> = {};
    for (const [t, n] of this.instances) e[t.name] = n;
    return e;
  }
}