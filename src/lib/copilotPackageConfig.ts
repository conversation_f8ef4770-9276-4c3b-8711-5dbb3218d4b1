const str=`{
  "name": "auto-complete",
  "displayName": "Auto Complete",
  "description": "A TypeScript-based auto-completion extension for VSCode",
  "version": "0.0.1",
  "engines": {
    "vscode": "^1.60.0"
  },
  "categories": [
    "Other"
  ],
  "publisher": "undefined_publisher",
  "enabledApiProposals": [
    "inlineCompletionsAdditions"
  ],
  "activationEvents": [
    "onLanguage:typescript",
    "onLanguage:javascript"
  ],
  "main": "./dist/extension.js",
  "contributes": {
    "commands": [
      {
        "command": "auto-complete.test",
        "title": "Test Auto Complete Extension"
      }
    ],
    "configuration": [
      {
        "title": "Copilot",
        "properties": {
          "github.copilot.advanced": {
            "type": "object",
            "title": "Advanced Settings",
            "properties": {
              "secret_key": {
                "type": "string",
                "default": "",
                "description": "Secret API key"
              },
              "length": {
                "type": "integer",
                "default": 500,
                "description": "Length of code to generate in tokens"
              },
              "temperature": {
                "type": "string",
                "default": "",
                "description": "Override sampling temperature (range 0.0 - 1.0)"
              },
              "top_p": {
                "type": "number",
                "default": 1,
                "description": "Top probability mass to consider"
              },

              "indentationMode": {
                "type": "object",
                "default": {
                  "python": false,
                  "javascript": false,
                  "javascriptreact": false,
                  "jsx": false,
                  "typescript": false,
                  "typescriptreact": false,
                  "go": false,
                  "ruby": false,
                  "": true
                },
                "markdownDescription": "Enable or disable indentation block termination for specified languages. Set to 'clientandserver' to run both parser-based and indent-based termination."
              },
              "inlineSuggestCount": {
                "type": "integer",
                "default": 3,
                "description": "Number of inline suggestions to fetch"
              },
              "listCount": {
                "type": "integer",
                "default": 10,
                "description": "Number of solutions to list in Open GitHub Copilot"
              },
              "debug.showScores": {
                "type": "boolean",
                "default": false,
                "description": "Show scores in sorted solutions"
              },
              "debug.overrideEngine": {
                "type": "string",
                "default": 10,
                "description": "Override engine name"
              },
              "debug.overrideProxyUrl": {
                "type": "string",
                "default": "",
                "description": "Override GitHub authentication proxy full URL"
              },
              "debug.testOverrideProxyUrl": {
                "type": "string",
                "default": "",
                "description": "Override GitHub authentication proxy URL when running tests"
              },
              "debug.filterLogCategories": {
                "type": "array",
                "default": [],
                "description": "Show only log categories listed in this setting. If an array is empty, show all loggers"
              },
              "debug.useSuffix": {
                "type": "boolean",
                "default": true,
                "description": "useSuffix "
              },
              "stops":{
                    "type": "object",
                    "default": {
                        "*": [
                            "\\n\\n\\n"
                        ],
                        "python": [
                            "\\ndef ",
                            "\\nclass ",
                            "\\nif ",
                            "\\n\\n#"
                        ]
                    },
                    "description": "Configure per-language stop sequences"
                }
            }
          },
          "github.copilot.enable": {
            "type": "object",
            "default": {
              "": true,
              "yaml": false,
              "plaintext": false,
              "markdown": false,
              "java": true
            },
            "markdownDescription": "Enable or disable Copilot for specified languages"
          },
          "github.copilot.inlineSuggest.enable": {
            "type": "boolean",
            "default": true,
            "description": "Show inline suggestions"
          }
        }
      }
    ]
  },
  "scripts": {
    "vscode:prepublish": "npm run compile",
    "compile": "tsc -p ./ && cp src/*.wasm src/*.bpe  src/tokenizer.json src/vocab.bpe dist/",
    "watch": "tsc -watch -p ./",
    "pretest": "npm run compile && npm run lint",
    "lint": "eslint src --ext ts",
    "test": "node ./dist/test/runTest.js"
  },
  "devDependencies": {
    "@types/emscripten": "^1.40.1",
    "@types/glob": "^7.1.3",
    "@types/mocha": "^8.2.2",
    "@types/node": "^13.11.0",
    "@types/tunnel": "^0.0.6",
    "tunnel": "^0.0.6",
    "@types/uuid": "^8.3.2",
    "uuid": "^8.3.2",
    "@types/vscode": "^1.60.0",
    "@adobe/helix-fetch": "github:bmuskalla/helix-fetch#4e33e47bf6e64b637d5d713558fde504bf71c947",
    "@typescript-eslint/eslint-plugin": "^4.26.0",
    "@typescript-eslint/parser": "^4.26.0",
    "eslint": "^7.27.0",
    "glob": "^7.1.7",
    "mocha": "^8.4.0",
    "typescript": "^4.3.2",
    "vscode-test": "^1.5.2",
    "@types/git-url-parse": "^9.0.1",
    "git-url-parse": "^13.1.0"
  },
  "dependencies": {
    "@dqbd/tiktoken": "^1.0.20",
    "@types/sha256": "^0.2.2",
    "sha256": "^0.2.0",
    "tree-sitter": "^0.22.4",
    "web-tree-sitter": "^0.25.3"
  }
}
`
 // '{"name":"copilot","displayName":"GitHub Copilot","description":"Your AI pair programmer","version":"1.57.7193","build":"7193","buildType":"prod","publisher":"GitHub","preview":false,"homepage":"https://github.com/features/copilot","bugs":{"url":"https://github.com/community/community/discussions/categories/copilot"},"qna":"https://github.com/github-community/community/discussions/categories/copilot","icon":"assets/Copilot-App-Icon.png","engines":{"vscode":"^1.68.0","npm":">=7.0.0"},"categories":["Other"],"badges":[{"url":"https://img.shields.io/badge/GitHub%20Copilot-Subscription%20Required-orange","href":"https://github.com/github-copilot/signup","description":"Sign up for GitHub Copilot"}],"activationEvents":["onStartupFinished"],"main":"./dist/extension","enabledApiProposals":["inlineCompletionsAdditions"],"contributes":{"commands":[{"command":"github.copilot.generate","title":"Open GitHub Copilot"},{"command":"github.copilot.toggleCopilot","title":"GitHub Copilot: Configure Enabled/Disabled"},{"command":"github.copilot.sendFeedback","title":"GitHub Copilot: Send Feedback"}],"keybindings":[{"command":"github.copilot.generate","key":"ctrl+enter","mac":"ctrl+enter","when":"editorTextFocus && github.copilot.activated"},{"command":"editor.action.inlineSuggest.trigger","key":"alt+\\\\","when":"editorTextFocus && !editorHasSelection && config.github.copilot.inlineSuggest.enable && !inlineSuggestionsVisible"}],"menus":{"editor/inlineCompletions/actions":[{"command":"github.copilot.generate","when":"github.copilot.activated"}],"commandPalette":[{"command":"github.copilot.generate","when":"github.copilot.activated"},{"command":"github.copilot.toggleCopilot","when":"github.copilot.activated"},{"command":"github.copilot.sendFeedback","when":"github.copilot.activated"}]},"configuration":[{"title":"Copilot","properties":{"github.copilot.advanced":{"type":"object","title":"Advanced Settings","properties":{"secret_key":{"type":"string","default":"","description":"Secret API key"},"length":{"type":"integer","default":500,"description":"Length of code to generate in tokens"},"temperature":{"type":"string","default":"","description":"Override sampling temperature (range 0.0 - 1.0)"},"top_p":{"type":"number","default":1,"description":"Top probability mass to consider"},"stops":{"type":"object","default":{"*":["\\n\\n\\n"],"python":["\\ndef ","\\nclass ","\\nif ","\\n\\n#"]},"description":"Configure per-language stop sequences"},"indentationMode":{"type":"object","default":{"python":false,"javascript":false,"javascriptreact":false,"jsx":false,"typescript":false,"typescriptreact":false,"go":false,"ruby":false,"*":true},"markdownDescription":"Enable or disable indentation block termination for specified [languages](https://code.visualstudio.com/docs/languages/identifiers). Set to \'clientandserver\' to run both parser-based and indent-based termination."},"inlineSuggestCount":{"type":"integer","default":3,"description":"Number of inline suggestions to fetch"},"listCount":{"type":"integer","default":10,"description":"Number of solutions to list in Open GitHub Copilot"},"debug.showScores":{"type":"boolean","default":false,"description":"Show scores in sorted solutions"},"debug.overrideEngine":{"type":"string","default":"","description":"Override engine name"},"debug.overrideProxyUrl":{"type":"string","default":"","description":"Override GitHub authentication proxy full URL"},"debug.testOverrideProxyUrl":{"type":"string","default":"","description":"Override GitHub authentication proxy URL when running tests"},"debug.filterLogCategories":{"type":"array","default":[],"description":"Show only log categories listed in this setting. If an array is empty, show all loggers"}}},"github.copilot.enable":{"type":"object","default":{"*":true,"yaml":false,"plaintext":false,"markdown":false},"markdownDescription":"Enable or disable Copilot for specified [languages](https://code.visualstudio.com/docs/languages/identifiers)"},"github.copilot.inlineSuggest.enable":{"type":"boolean","default":true,"description":"Show inline suggestions"}}}],"icons":{"copilot-logo":{"description":"GitHub Copilot icon","default":{"fontPath":"assets/copilot.woff","fontCharacter":"\\\\0041"}},"copilot-warning":{"description":"GitHub Copilot icon","default":{"fontPath":"assets/copilot.woff","fontCharacter":"\\\\0042"}},"copilot-notconnected":{"description":"GitHub Copilot icon","default":{"fontPath":"assets/copilot.woff","fontCharacter":"\\\\0043"}}},"iconFonts":[{"id":"copilot-font","src":[{"path":"assets/copilot.woff","format":"woff"}]}]},"scripts":{"postinstall":"npx vscode-dts dev && mv vscode.proposed.*.ts extension/src","vscode:prepublish":"npm-run-all build:extension","build":"npm-run-all build:*","build:agent":"cd agent && npm run build","build:agent-no-pkg":"cd agent && npm run build-no-pkg","build:extension":"webpack --mode production","build:lib":"cd lib && npm run build","compile":"npm-run-all compile:*","compile:agent":"cd agent && npm run compile","compile:extension":"webpack --mode development","compile:lib":"cd lib && npm run compile","watch":"npm-run-all -p watch:*","watch:agent":"cd agent && npm run watch","watch:extension":"webpack --mode development --watch","watch:lib":"cd lib && npm run watch","lint":"eslint lib extension agent --ext ts","check_deps":"depcruise -c .dependency-cruiser.js .","test":"npm-run-all test:*","test:agent":"cd agent && npm run test","test:extension":"cd extension && ts-node ./test/runTest.ts","test:prompt":"cd prompt && npm install && npm run test","test:lib":"cd lib && npm run test","test:lsp-client":"cd lsp-client && npm run smokeTest","build_test":"tsc extension/test/run.ts","get_token":"ts-node script/getToken.ts"},"devDependencies":{"@github/prettier-config":"0.0.4","@types/crypto-js":"^4.0.1","@types/mocha":"^5.2.6","@types/node":"^13.11.0","@types/tunnel":"^0.0.3","@types/uuid":"^8.3.1","@types/vscode":"^1.68.0","@typescript-eslint/eslint-plugin":"^4.27.0","@typescript-eslint/parser":"^4.27.0","dependency-cruiser":"^9.26.1","eslint":"^7.29.0","eslint-plugin-no-only-tests":"^2.6.0","fantasticon":"^1.2.2","get-port":"^5.1.1","mocha":"^6.1.4","npm-run-all":"^4.1.5","prettier":"2.5.1","prettier-plugin-organize-imports":"^2.3.4","ts-dedent":"^2.2.0","ts-loader":"^8.0.11","ts-node":"^9.0.0","typescript":"^4.4.3","vsce":"1.91.0","vscode-dts":"^0.3.2","vscode-test":"^1.3.0","webpack":"^5.54.0","webpack-cli":"^4.2.0","zlib":"^1.0.5"},"dependencies":{"@adobe/helix-fetch":"github:bmuskalla/helix-fetch#4e33e47bf6e64b637d5d713558fde504bf71c947","@github/copilot-promptlib":"file:prompt","@sinclair/typebox":"^0.23.4","@types/git-url-parse":"^9.0.1","ajv":"^8.6.3","ajv-errors":"^3.0.0","crypto-js":"^4.0.0","git-url-parse":"^13.1.0","open":"^8.4.0","source-map-support":"^0.5.19","tunnel":"^0.0.6","uuid":"^8.3.2","vscode-extension-telemetry":"^0.1.6","vscode-uri":"^3.0.2"}}'

// 导出配置对象
const config =JSON.parse(
  str
);
export default config;


/**
 * 4147
 * GitHub Copilot 扩展配置文件
 * 
 * 文件名建议：copilotExtensionConfig.ts 或 copilotPackageConfig.ts
 * 
 * 该模块导出 GitHub Copilot VSCode 扩展的完整配置信息（等同于 package.json 的内容），包括：
 * - 基本信息：名称、版本、描述、发布者等
 * - 构建信息：构建号、构建类型
 * - 引擎要求：VSCode 和 npm 版本要求
 * - 扩展功能：命令、快捷键、菜单项
 * - 配置选项：高级设置、语言支持、内联建议等
 * - 图标和资源定义
 * - 脚本命令：用于构建、测试等
 * 
 * 该配置信息以 JSON 格式存储，用于定义扩展的行为和功能。
 * 
 */
