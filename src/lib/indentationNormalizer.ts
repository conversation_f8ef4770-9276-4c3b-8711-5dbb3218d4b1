/**
 * 3286
 * 文件名: indentationNormalizer.ts
 * 
 * 主要功能：
 * 这个模块提供了一个函数来规范化文本的缩进字符。
 * 它可以根据编辑器配置，将空格转换为制表符，或将制表符转换为空格，
 * 并且可以选择性地将缩进对齐到制表位。
 * 
 * 这个模块在处理不同编辑器设置下的代码缩进时非常有用，
 * 特别是在处理多人协作或跨平台开发的项目中。
 */

// 定义编辑器配置接口
interface EditorConfig {
  tabSize?: number | string;
  insertSpaces?: boolean;
}

// 定义文本内容接口
interface TextContent {
  displayText: string;
  completionText: string;
  displayNeedsWsOffset?: boolean;
  completionIndex: number;
}
/**
 * 规范化缩进字符
 * @param editorConfig - 编辑器配置
 * @param textContent - 包含要处理的文本内容
 * @param alignToTabStops - 是否将缩进对齐到制表位
 * @returns 处理后的文本内容
 */
export function normalizeIndentCharacter(
  editorConfig: EditorConfig,
  textContent: TextContent,
  alignToTabStops: boolean
): TextContent {
  /**
   * 替换缩进
   * @param text - 要处理的文本
   * @param indentChar - 要替换的缩进字符
   * @param replaceFunc - 替换函数
   * @returns 处理后的文本
   */
  function replaceIndentation(
    text: string,
    indentChar: string,
    replaceFunc: (indentLength: number) => string
  ): string {
    const indentRegex = new RegExp(`^(${indentChar})+`, "g");
    return text
      .split("\n")
      .map((line) => {
        const trimmedLine = line.replace(indentRegex, "");
        const indentLength = line.length - trimmedLine.length;
        return replaceFunc(indentLength) + trimmedLine;
      })
      .join("\n");
  }

  // 确定制表符大小
  let tabSize: number = 
    editorConfig.tabSize === undefined || typeof editorConfig.tabSize === "string"
      ? 4
      : editorConfig.tabSize;

  if (editorConfig.insertSpaces === false) {
    /**
     * 将空格转换为制表符
     * @param text - 要转换的文本
     * @returns 转换后的文本
     */
    const convertToTabs = (text: string): string =>
      replaceIndentation(text, " ", (indentLength) =>
        "\t".repeat(Math.floor(indentLength / tabSize)) +
        " ".repeat(indentLength % tabSize)
      );

    textContent.displayText = convertToTabs(textContent.displayText);
    textContent.completionText = convertToTabs(textContent.completionText);
  } else if (editorConfig.insertSpaces === true) {
    /**
     * 将制表符转换为空格
     * @param text - 要转换的文本
     * @returns 转换后的文本
     */
    const convertToSpaces = (text: string): string =>
      replaceIndentation(text, "\t", (indentLength) =>
        " ".repeat(indentLength * tabSize)
      );

    textContent.displayText = convertToSpaces(textContent.displayText);
    textContent.completionText = convertToSpaces(textContent.completionText);

    if (alignToTabStops) {
      /**
       * 将缩进对齐到制表位
       * @param text - 要对齐的文本
       * @returns 对齐后的文本
       */
      const alignToTabStop = (text: string): string => {
        const leadingSpaces = text.length - text.trimLeft().length;
        const extraSpaces = leadingSpaces % tabSize;
        return extraSpaces !== 0 && leadingSpaces > 0
          ? replaceIndentation(
              text,
              " ".repeat(extraSpaces),
              (indentLength) =>
                " ".repeat((Math.floor(indentLength / tabSize) + 1) * tabSize)
            )
          : text;
      };

      textContent.displayText = alignToTabStop(textContent.displayText);
      textContent.completionText = alignToTabStop(textContent.completionText);
    }
  }

  return textContent;
}
