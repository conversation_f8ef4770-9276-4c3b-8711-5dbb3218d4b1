/**
 * 1839
 * 文档访问跟踪器 (DocumentAccessTracker)
 * 
 * 该模块的主要功能是跟踪文档的访问时间并提供基于访问时间的排序功能。
 * 它维护了一个文档URI到最后访问时间的映射，并提供了一个根据这些访问时间对文档进行排序的方法。
 * 
 * 主要组件：
 * 1. accessTimeMap: 存储文档URI到最后访问时间的映射
 * 2. sortByAccessTimes: 根据文档的访问时间对文档数组进行排序的函数
 * 3. registerDocumentTracker: 注册文档焦点变化事件的处理函数，用于更新访问时间
 */

import { TextDocumentManager } from './textDocumentUtils';
import { My_Context } from './contextType';

// 定义文档元素接口
interface DocumentElement {
  uri: {
    toString: () => string;
  };
}

// 定义上下文接口
interface Context1 {
  get: (manager: typeof TextDocumentManager) => {
    onDidFocusTextDocument: (callback: (event: { document: DocumentElement } | undefined) => void) => void;
  };
}

// 存储文档URI到最后访问时间的映射
const accessTimeMap = new Map<string, number>();

/**
 * 根据文档的访问时间对文档数组进行排序
 * @param elements - 要排序的文档数组
 * @returns 排序后的文档数组
 */
export function sortByAccessTimes(elements: DocumentElement[]): DocumentElement[] {
return [...elements].sort((element, compareElement) => {
    const currentAccessTime = accessTimeMap.get(element.uri.toString()) ?? 0;
    const compareAccessTime = accessTimeMap.get(compareElement.uri.toString()) ?? 0;
    return compareAccessTime - currentAccessTime;
  });
}

/**
 * 注册文档跟踪器，用于更新文档的访问时间
 * @param context - 包含TextDocumentManager的上下文对象
 */
export function registerDocumentTracker(context: My_Context): void {
  context.get(TextDocumentManager).onDidFocusTextDocument((event :any) => {
    if (event) {
      accessTimeMap.set(event.document.uri.toString(), Date.now());
    }
  });
}
