/**
 * 3055250
 * 文件名: windowDelineator.ts
 * 
 * 主要功能:
 * 这个文件定义了一个 getWindowsDelineations 函数，用于将文本分割成窗口或段落。
 * 该函数使用树结构解析和遍历来确定文本的窗口边界，考虑了行数、长度和结构等因素。
 * 
 * 主要组件:
 * - Label 和 TreeNode 接口：定义了用于树结构的标签和节点类型
 * - getWindowsDelineations 函数：核心功能，根据给定的参数计算文本窗口的边界
 * 
 * 该模块主要用于文本分析和处理，可能在代码片段管理或上下文相关的文本操作中使用。
 */

import { parseTree } from './parseTree';
import { visitTree,clearLabels ,Node} from './treeOperations';

/**
 * 获取窗口划分
 * @param e 输入字符串数组
 * @param t 解析树的参数
 * @param n 最小窗口大小
 * @param i 最大窗口大小
 * @returns 窗口划分数组
 */
export function getWindowsDelineations(e: string[], language: string, n: number, i: number): number[][] {
  if (e.length < n || i === 0) return [];

  const s: number[][] = [];
  const tree =parseTree(e.join("\n"), language)
  const a: Node = clearLabels(tree);

  visitTree(
    a,
    (e: Node) => {
      if (e.type === "blank") {
        e.label = {
          totalLength: 1,
          firstLineAfter: (e.lineNumber || 0) + 1,
        };
        return;
      }

      let t = e.type === "line" ? 1 : 0;
      let r = e.type === "line" ? (e.lineNumber || 0) + 1 : NaN;

      function o(n: number): number {
        return n === -1
          ? r - t
          : (e.subs?.[n].label?.firstLineAfter || 0) - (e.subs?.[n].label?.totalLength || 0);
      }

      function a(t: number, n: number): number {
        return t === 0 ? n + 1 : e.subs?.[t - 1].label?.firstLineAfter || 0;
      }

      let c = e.type === "line" ? -1 : 0;
      let l = e.type === "line" ? 1 : 0;
      let u = 0;

      for (let d = 0; d < (e.subs?.length || 0); d++) {
        while (c >= 0 && c < (e.subs?.length || 0) && e.subs?.[c].type === "blank") {
          l -= e.subs[c].label?.totalLength || 0;
          c++;
        }

        if (e.subs?.[d].type !== "blank") {
          u = d;
        }

        r = e.subs?.[d].label?.firstLineAfter || 0;
        t += e.subs?.[d].label?.totalLength || 0;
        l += e.subs?.[d].label?.totalLength || 0;

        if (l > i) {
          const t = o(c);
          const r = a(d, t);
          const p = u === d ? r : a(u, t);

          if (n <= r - t) s.push([t, p]);

          while (l > i) {
            l -= c === -1
              ? e.type === "line"
                ? 1
                : 0
              : e.subs?.[c].label?.totalLength || 0;
            c++;
          }
        }
      }

      if (c < (e.subs?.length || 0)) {
        const t = o(c);
        const i = r;
        const a = c === -1 ? i : e.subs?.[u].label?.firstLineAfter || 0;

        if (n <= i - t) {
          s.push([t, a]);
        }
      }

      e.label = {
        totalLength: t,
        firstLineAfter: r,
      };
    },
    "bottomUp"
  );

  // 对结果进行排序和过滤
  return s
    .sort((e, t) => e[0] - t[0] || e[1] - t[1])
    .filter((e, t, n) => t === 0 || e[0] !== n[t - 1][0] || e[1] !== n[t - 1][1]);
}
