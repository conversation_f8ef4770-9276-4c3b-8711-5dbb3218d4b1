/**
 * 3055125
 * @file neighborSnippetManager.ts
 * @description 这个文件主要负责管理和获取邻近代码片段。它提供了格式化代码片段、
 * 配置不同邻近选项的匹配策略，以及根据给定条件查找和返回相关代码片段的功能。
 * 这些功能在代码补全和上下文相关的编程辅助中起着关键作用。
 */

import * as languageUtils from './languageUtils';
import * as matcherUtils from './jaccardMatcher';
import {Context} from './currentContext'
import {SnippetSelectionOption} from './promptManager'

// 定义接口
interface SnippetInfo {
  relativePath?: string;
  snippet: string;
}

interface MatcherOptions {
  matcherFactory: any;
  threshold: number;
  numberOfSnippets: number;
}

interface File {
  source: string;
  relativePath: string;
}

interface CursorPosition {
  line: number;
  character: number;
}

interface Match {
  relativePath?: string;
  score: number;
  snippet: string;
  startLine: number;
  endLine: number;
}

/**
 * 格式化代码片段行
 * @param snippetInfo - 包含代码片段信息的对象
 * @returns 格式化后的代码片段行数组
 */
function formatSnippetLines(snippetInfo: SnippetInfo): string[] {
  return [
    snippetInfo.relativePath
      ? "Compare this snippet from " + snippetInfo.relativePath + ":"
      : "Compare this snippet:",
  ].concat(snippetInfo.snippet.split("\n"));
}

/**
 * 邻近选项到选择配置的映射
 * 定义了不同邻近选项的匹配器工厂、阈值和代码片段数量
 */
export const neighborOptionToSelection: { [key: string]: MatcherOptions } = {
  none: {
    matcherFactory: matcherUtils.FixedWindowSizeJaccardMatcher.FACTORY(1),
    threshold: -1,
    numberOfSnippets: 0,
  },
  conservative: {
    matcherFactory: matcherUtils.FixedWindowSizeJaccardMatcher.FACTORY(10),
    threshold: 0.3,
    numberOfSnippets: 1,
  },
  medium: {
    matcherFactory: matcherUtils.FixedWindowSizeJaccardMatcher.FACTORY(20),
    threshold: 0.1,
    numberOfSnippets: 2,
  },
  eager: {
    matcherFactory: matcherUtils.FixedWindowSizeJaccardMatcher.FACTORY(60),
    threshold: 0,
    numberOfSnippets: 4,
  },
  eagerButLittle: {
    matcherFactory: matcherUtils.FixedWindowSizeJaccardMatcher.FACTORY(10),
    threshold: 0,
    numberOfSnippets: 1,
  },
};

/**
 * 获取邻近代码片段
 * @param Context - 当前文档对象
 * @param neighborFiles - 邻近文件数组
 * @param neighborOption - 邻近选项
 * @param indentationMinLength - 最小缩进长度
 * @param indentationMaxLength - 最大缩进长度
 * @param cursorPosition - 光标位置
 * @param desiredCompletionPos - 期望的补全位置
 * @returns 邻近代码片段数组
 */
export async function getNeighborSnippets(
  context: Context,
  neighborFiles: File[],
  neighborOption: string,
  indentationMinLength?: number,
  indentationMaxLength?: number,
  cursorPosition?: SnippetSelectionOption,
  desiredCompletionPos?: number
): Promise<Match[]> {
  const selectedNeighborOption = neighborOptionToSelection[neighborOption];
  
  /**
   * 创建匹配器
   * @param context - 当前文档对象
   * @param neighborOption - 邻近选项
   * @param minLength - 最小长度
   * @param maxLength - 最大长度
   * @returns 匹配器对象
   */
  const matcher = (function (
    context: Context,
    neighborOption: string,
    minLength?: number,
    maxLength?: number
  ) {
    const options = {
      ...neighborOptionToSelection[neighborOption],
    };
    if (minLength !== undefined && maxLength !== undefined) {
      options.matcherFactory = matcherUtils.IndentationBasedJaccardMatcher.FACTORY(minLength, maxLength);
    }
    return options.matcherFactory.to(context);
  })(context, neighborOption, indentationMinLength, indentationMaxLength);

  return neighborFiles
    // 过滤文件大小
    .filter((file) => file.source.length < 1e6 && file.source.length > 0)
    // 限制处理文件数量
    .slice(0, 20)
    // 查找匹配的代码片段
    .reduce<Match[]>(
      (acc, currentFile) =>
        acc.concat(
          matcher.findMatches(currentFile, cursorPosition, desiredCompletionPos).map((match: Match) => ({
            relativePath: currentFile.relativePath,
            ...match,
          }))
        ),
      []
    )
    // 过滤有效的匹配结果
    .filter((match) => match.score && match.snippet && match.score > selectedNeighborOption.threshold)
    // 按分数排序
    .sort((a, b) => a.score - b.score)
    // 选择指定数量的代码片段
    .slice(-selectedNeighborOption.numberOfSnippets)
    // 格式化最终结果
    .map((match) => ({
      score: match.score,
      snippet: formatSnippetLines(match)
        .map((line) => languageUtils.comment(line, context.languageId) + "\n")
        .join(""),
      startLine: match.startLine,
      endLine: match.endLine,
    }));
}
