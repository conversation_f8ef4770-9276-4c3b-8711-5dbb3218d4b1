/**
 * 3055395
 * 编辑距离计算模块 editDistanceCalculator
 * 
 * 该模块提供了一个函数用于计算两个字符串或token数组之间的编辑距离（Levenshtein距离）。
 * 编辑距离是指将一个字符串转换为另一个字符串所需的最少单字符编辑（插入、删除或替换）次数。
 */

import tokenizer from './tokenizer2';

// 定义结果对象的接口
interface EditDistanceResult {
  score: number;
}

// 定义输入类型
type InputType = string | number[];

// 定义一个辅助函数来确保输入是 number[]
function ensureTokenArray(input: InputType): number[] {
  if (typeof input === "string") {
    return tokenizer.tokenize(input);
  }
  return input;
}

/**
 * 计算两个字符串或token数组之间的编辑距离
 * @param source - 源字符串或token数组
 * @param target - 目标字符串或token数组
 * @returns 包含编辑距离分数的对象
 */
export function findEditDistanceScore(source: InputType, target: InputType): EditDistanceResult {
  // 确保输入是 number[]
  let sourceTokens: number[] = ensureTokenArray(source);
  let targetTokens: number[] = ensureTokenArray(target);

  // 处理空输入的情况
  if (sourceTokens.length === 0 || targetTokens.length === 0) {
    return {
      score: sourceTokens.length + targetTokens.length,
    };
  }

  // 初始化动态规划矩阵
  const dpMatrix: number[][] = Array.from({ length: sourceTokens.length }, () =>
    Array.from({ length: targetTokens.length }, () => 0)
  );

  // 初始化第一行和第一列
  for (let i = 0; i < sourceTokens.length; i++) dpMatrix[i][0] = i;
  for (let j = 0; j < targetTokens.length; j++) dpMatrix[0][j] = j;

  // 填充动态规划矩阵
  for (let j = 0; j < targetTokens.length; j++) {
    for (let i = 0; i < sourceTokens.length; i++) {
      dpMatrix[i][j] = Math.min(
        (i === 0 ? j : dpMatrix[i - 1][j]) + 1,
        (j === 0 ? i : dpMatrix[i][j - 1]) + 1,
        (i === 0 || j === 0 ? Math.max(i, j) : dpMatrix[i - 1][j - 1]) +
          (sourceTokens[i] === targetTokens[j] ? 0 : 1)
      );
    }
  }

  // 返回最终的编辑距离分数
  return {
    score: dpMatrix[sourceTokens.length - 1][targetTokens.length - 1],
  };
}
