// 导入模块
import r from 'git-url-parse';
import o from './9073';

// 定义接口来描述函数参数和返回值的结构
// interface ParsedUrl {
//   token: string;
//   user: string;
//   password: string;
//   protocols: string[];
//   protocol: string;
//   href: string;
//   [key: string]: any; // 允许其他属性
// }

// 导出一个函数，该函数接受一个字符串参数并返回 ParsedUrl 类型的对象
export default function (e: string): any {
  // 使用 r 函数解析输入的 URL
  let t: any = r(e);
  
  // 初始化 token 属性
  t.token = "";

  // 根据用户名和密码的组合确定 token
  if (t.password === "x-oauth-basic") {
    t.token = t.user;
  } else if (t.user === "x-token-auth") {
    t.token = t.password;
  }

  // 确定协议
  if (o(t.protocols) || (t.protocols.length === 0 && o(e))) {
    // 如果 protocols 为空或输入的 URL 符合某种条件，设置协议为 ssh
    t.protocol = "ssh";
  } else if (t.protocols.length) {
    // 如果 protocols 数组不为空，使用第一个协议
    t.protocol = t.protocols[0];
  } else {
    // 默认情况下，设置协议为 file
    t.protocol = "file";
    t.protocols = ["file"];
  }

  // 移除 URL 末尾的斜杠
  t.href = t.href.replace(/\/$/, "");

  // 返回处理后的对象
  return t;
}
