/**
 * id：1133
 * @fileoverview 配置管理模块
 * 
 * 该模块提供了一套完整的配置管理系统，包括：
 * 1. 配置键定义：定义了各种配置项的键，包括编辑器设置、调试选项等。
 * 2. 块模式管理：处理不同的代码块处理模式（解析、服务器、混合模式）。
 * 3. 配置提供者：包括默认配置提供者和内存配置提供者，用于管理和覆盖配置。
 * 4. 构建信息管理：处理版本、构建类型等信息。
 * 5. VSCode 相关信息管理：处理 VSCode 会话、机器 ID 等信息。
 * 6. 编辑器和插件版本信息：提供编辑器和插件版本的格式化输出。
 * 
 * 该模块是 Copilot 插件的核心配置管理系统，为其他模块提供配置支持。
 */

import * as configPrefix from './copilotConstants';
import * as features from './featureManager';
import * as languageParserAndQuerier from './languageParserAndQuerier';
import * as packageJson from './copilotPackageConfig';
import {MyContext} from './myContext';
export {MyContext};


// 定义配置键类型
export type ConfigKeyType = string | string[];

// 定义配置键枚举
export const ConfigKey: Record<string, ConfigKeyType> = {
  Enable: "enable",
  InlineSuggestEnable: "inlineSuggest.enable",
  ShowEditorCompletions: ["editor", "showEditorCompletions"],
  EnableAutoCompletions: ["editor", "enableAutoCompletions"],
  DelayCompletions: ["editor", "delayCompletions"],
  FilterCompletions: ["editor", "filterCompletions"],
  DisplayStyle: ["advanced", "displayStyle"],
  SecretKey: ["advanced", "secret_key"],
  SolutionLength: ["advanced", "length"],
  Stops: ["advanced", "stops"],
  Temperature: ["advanced", "temperature"],
  TopP: ["advanced", "top_p"],
  IndentationMode: ["advanced", "indentationMode"],
  InlineSuggestCount: ["advanced", "inlineSuggestCount"],
  ListCount: ["advanced", "listCount"],
  DebugOverrideProxyUrl: ["advanced", "debug.overrideProxyUrl"],
  DebugTestOverrideProxyUrl: ["advanced", "debug.testOverrideProxyUrl"],
  DebugOverrideEngine: ["advanced", "debug.overrideEngine"],
  DebugShowScores: ["advanced", "debug.showScores"],
  DebugOverrideLogLevels: ["advanced", "debug.overrideLogLevels"],
  DebugFilterLogCategories: ["advanced", "debug.filterLogCategories"],
  DebugUseSuffix: ["advanced", "debug.useSuffix"],
};

// 定义块模式枚举
export enum BlockMode {
  Parsing = "parsing",
  Server = "server",
  ParsingAndServer = "parsingandserver"
}

/**
 * 判断是否应该进行解析修剪
 * @param {BlockMode} mode - 当前的块模式
 * @returns {boolean} 如果模式是 Parsing 或 ParsingAndServer，则返回 true
 */
export const shouldDoParsingTrimming = (mode: BlockMode): boolean => {
  return [BlockMode.Parsing, BlockMode.ParsingAndServer].includes(mode);
};

/**
 * 判断是否应该进行服务器修剪
 * @param {BlockMode} mode - 当前的块模式
 * @returns {boolean} 如果模式是 Server 或 ParsingAndServer，则返回 true
 */
export const shouldDoServerTrimming = (mode: BlockMode): boolean => {
  return [BlockMode.Server, BlockMode.ParsingAndServer].includes(mode);
};

// 定义构建类型枚举
export enum BuildType {
  DEV = "dev",
  PROD = "prod",
  NIGHTLY = "nightly"
}

/**
 * 获取块模式
 * @param {BlockMode} mode - 当前的块模式
 * @param {string} languageId - 语言 ID
 * @returns {BlockMode} 返回适当的块模式
 */
function getBlockMode(mode: BlockMode, languageId: string): BlockMode {
  return mode !== BlockMode.ParsingAndServer || languageParserAndQuerier.isSupportedLanguageId(languageId) 
    ? mode 
    : BlockMode.Server;
}

// 块模式配置基类
export abstract class BlockModeConfig {
  abstract forLanguage(context: any, languageId: string): Promise<BlockMode>;
}

// 配置块模式配置类
export class ConfigBlockModeConfig extends BlockModeConfig {
  /**
   * 根据语言获取块模式配置
   * @param {any} context - 上下文对象
   * @param {string} languageId - 语言 ID
   * @returns {Promise<BlockMode>} 返回适当的块模式
   */
  async forLanguage(context: MyContext, languageId: string): Promise<BlockMode> {
    if (
      context
        .get(ConfigProvider)
        .isDefaultSettingOverwritten(ConfigKey.IndentationMode)
    ) {
      const indentationMode = context
        .get(ConfigProvider)
        .getLanguageConfig(ConfigKey.IndentationMode, languageId);
      
      switch (indentationMode) {
        case "client":
        case true:
        case "server":
          return BlockMode.Server;
        case "clientandserver":
          return getBlockMode(BlockMode.ParsingAndServer, languageId);
        default:
          return BlockMode.Parsing;
      }
    }
    
    const overrideMode = await context.get(features.Features).overrideBlockMode();
    return overrideMode 
      ? getBlockMode(overrideMode, languageId) 
      : languageParserAndQuerier.isSupportedLanguageId(languageId) 
        ? BlockMode.Parsing 
        : BlockMode.Server;
  }
}

// 配置提供者抽象类
export abstract class ConfigProvider {
  abstract getConfig(key: ConfigKeyType): any;
  abstract isDefaultSettingOverwritten(key: ConfigKeyType): boolean;
  abstract dumpConfig(): Record<string, any>;
  abstract getLanguageConfig(key: ConfigKeyType, languageId?: string): any;
}

/**
 * 获取配置键的默认值
 * @param {string} key - 配置键
 * @returns {any} 返回配置键的默认值
 * @throws {Error} 如果找不到默认值或发生错误
 */
export function getConfigDefaultForKey(key: string): any {
  try {
    const fullKey = `${configPrefix.CopilotConfigPrefix}.${key}`;
    const config = packageJson.default;
    
    if (!config || !config.contributes || !config.contributes.configuration) {
      throw new Error(`Configuration not found in package.json`);
    }

    const properties = config.contributes.configuration[0].properties;
    
    if (!properties || !properties[fullKey]) {
      throw new Error(`Configuration key not found: ${fullKey}`);
    }

    const defaultValue = properties[fullKey].default;
    
    if (undefined === defaultValue) {
      throw new Error(`Missing config default value: ${fullKey}`);
    }
    
    return defaultValue;
  } catch (error) {
    console.error(`Error in getConfigDefaultForKey for key ${key}:`, error);
    throw new Error(
      `Error inspecting config default value ${configPrefix.CopilotConfigPrefix}.${key}: ${error}`
    );
  }
}

/**
 * 获取对象配置键的默认值
 * @param {string} objectKey - 对象配置键
 * @param {string} propertyKey - 属性键
 * @returns {any} 返回对象配置键的默认值
 * @throws {Error} 如果找不到默认值或发生错误
 */
export function getConfigDefaultForObjectKey(objectKey: string, propertyKey: string): any {
  try {
    const config =packageJson.default;
    
    if (!config || !config.contributes || !config.contributes.configuration) {
      throw new Error(`Configuration not found in package.json`);
    }

    const defaultValue =
      config.contributes.configuration[0].properties[`${configPrefix.CopilotConfigPrefix}.${objectKey}`]
        .properties[propertyKey].default;
    if (undefined === defaultValue)
      throw new Error(
        `Missing config default value: ${configPrefix.CopilotConfigPrefix}.${objectKey}`
      );
    return defaultValue;
  } catch (error) {
    throw new Error(
      `Error inspecting config default value ${configPrefix.CopilotConfigPrefix}.${objectKey}.${propertyKey}: ${error}`
    );
  }
}

/**
 * 获取配置值
 * @param {any} context - 上下文对象
 * @param {ConfigKeyType} key - 配置键
 * @returns {any} 返回配置值
 */
export function getConfig(context: MyContext, key: ConfigKeyType): any {
  return context.get(ConfigProvider).getConfig(key);
}

/**
 * 检查默认设置是否被覆盖
 * @param {MyContext} context - 上下文对象
 * @param {ConfigKeyType} key - 配置键
 * @returns {boolean} 如果默认设置被覆盖则返回 true
 */
export function isDefaultSettingOverwritten(context: MyContext, key: ConfigKeyType): boolean {
  return context.get(ConfigProvider).isDefaultSettingOverwritten(key);
}

/**
 * 获取隐藏配置
 * @param {MyContext} context - 上下文对象
 * @param {ConfigKeyType} key - 配置键
 * @param {Record<string, any>} defaultValue - 默认值对象
 * @returns {any} 返回配置值或默认值
 */
export function getHiddenConfig(context: MyContext, key: ConfigKeyType, defaultValue: { default: any }): any {
  return isDefaultSettingOverwritten(context, key) ? getConfig(context, key) : defaultValue.default;
}

/**
 * 获取语言特定的配置
 * @param {any} context - 上下文对象
 * @param {ConfigKeyType} key - 配置键
 * @param {string} languageId - 语言 ID
 * @returns {any} 返回语言特定的配置值
 */
export function getLanguageConfig(context: MyContext, key: ConfigKeyType, languageId?: string): any {
  return context.get(ConfigProvider).getLanguageConfig(key, languageId);
}

// 仅默认值配置提供者类
export class DefaultsOnlyConfigProvider extends ConfigProvider {
  /**
   * 获取配置值
   * @param {ConfigKeyType} key - 配置键或键数组
   * @returns {any} 返回配置值
   */
  getConfig(key: ConfigKeyType): any {
    return Array.isArray(key)
      ? getConfigDefaultForObjectKey(key[0], key[1])
      : getConfigDefaultForKey(key);
  }

  /**
   * 检查默认设置是否被覆盖（始终返回 false）
   * @param {ConfigKeyType} key - 配置键
   * @returns {boolean} 始终返回 false
   */
  isDefaultSettingOverwritten(key: ConfigKeyType): boolean {
    return false;
  }

  /**
   * 导出配置（返回空对象）
   * @returns {Record<string, any>} 返回空对象
   */
  dumpConfig(): Record<string, any> {
    return {};
  }

  /**
   * 获取语言特定的配置
   * @param {ConfigKeyType} key - 配置键
   * @param {string} languageId - 语言 ID
   * @returns {any} 返回语言特定的配置值或通用配置值
   */
  getLanguageConfig(key: ConfigKeyType, languageId?: string): any {
    const config = this.getConfig(key);
    return languageId && languageId in config ? config[languageId] : config["*"];
  }
}

// 内存配置提供者类
export class InMemoryConfigProvider extends ConfigProvider {
  private baseConfigProvider: ConfigProvider;
  private overrides: Map<ConfigKeyType, any>;

  /**
   * 构造函数
   * @param {ConfigProvider} baseConfigProvider - 基础配置提供者
   * @param {Map<ConfigKeyType, any>} overrides - 覆盖配置的 Map
   */
  constructor(baseConfigProvider: ConfigProvider, overrides: Map<ConfigKeyType, any>) {
    super();
    this.baseConfigProvider = baseConfigProvider;
    this.overrides = overrides;
  }

  /**
   * 获取配置值
   * @param {ConfigKeyType} key - 配置键
   * @returns {any} 返回配置值
   */
  getConfig(key: ConfigKeyType): any {
    const overriddenValue = this.overrides.get(key);
    return undefined !== overriddenValue ? overriddenValue : this.baseConfigProvider.getConfig(key);
  }

  /**
   * 设置配置值
   * @param {ConfigKeyType} key - 配置键
   * @param {any} value - 配置值
   */
  setConfig(key: ConfigKeyType, value: any): void {
    if (undefined !== value) {
      this.overrides.set(key, value);
    } else {
      this.overrides.delete(key);
    }
  }

  /**
   * 设置语言启用状态
   * @param {string} language - 语言
   * @param {boolean} isEnabled - 是否启用
   */
  setLanguageEnablement(language: string, isEnabled: boolean): void {
    this.overrides.set(ConfigKey.Enable, {
      [language]: isEnabled,
    });
  }

  /**
   * 检查默认设置是否被覆盖
   * @param {ConfigKeyType} key - 配置键
   * @returns {boolean} 如果默认设置被覆盖则返回 true
   */
  isDefaultSettingOverwritten(key: ConfigKeyType): boolean {
    return (
      !!this.overrides.has(key) ||
      this.baseConfigProvider.isDefaultSettingOverwritten(key)
    );
  }

  /**
   * 将配置键转换为字符串
   * @param {ConfigKeyType} key - 配置键
   * @returns {string} 返回字符串形式的配置键
   */
  keyAsString(key: ConfigKeyType): string {
    return Array.isArray(key) ? key.join(".") : key;
  }

  /**
   * 导出配置
   * @returns {Record<string, any>} 返回包含所有配置的对象
   */
  dumpConfig(): Record<string, any> {
    const config = this.baseConfigProvider.dumpConfig();
    this.overrides.forEach((value, key) => {
      config[this.keyAsString(key)] = JSON.stringify(value);
    });
    return config;
  }

  /**
   * 获取语言特定的配置
   * @param {ConfigKeyType} key - 配置键
   * @param {string} language - 语言
   * @returns {any} 返回语言特定的配置值或通用配置值
   */
  getLanguageConfig(key: ConfigKeyType, language?: string): any {
    const overriddenValue = this.overrides.get(key);
    return undefined !== overriddenValue
      ? undefined !== language
        ? overriddenValue[language]
        : overriddenValue["*"]
      : this.baseConfigProvider.getLanguageConfig(key, language);
  }
}

/**
 * 导出配置
 * @param {any} context - 上下文对象
 * @returns {Record<string, any>} 返回包含所有配置的对象
 */
export function dumpConfig(context: MyContext): Record<string, any> {
  return context.get(ConfigProvider).dumpConfig();
}

/**
 * 获取启用配置
 * @param {any} context - 上下文对象
 * @param {string} languageId - 语言 ID
 * @returns {any} 返回语言特定的启用配置
 */
export function getEnabledConfig(context: MyContext, languageId?: string): any {
  return getLanguageConfig(context, ConfigKey.Enable, languageId);
}

/**
 * 获取后缀百分比
 * @param {any} context - 上下文对象
 * @param {any} param1 - 参数1
 * @param {any} param2 - 参数2
 * @returns {Promise<number>} 返回后缀百分比
 */
export async function suffixPercent(): Promise<number> {
  return 15;
  // 配置
  // getHiddenConfig(context, ConfigKey.DebugUseSuffix, {
  //   default: true,
  // })
  //   ? 15
  //   : context.get(features.Features).suffixPercent(param1, param2);
}

/**
 * 获取后缀匹配阈值
 * @param {any} context - 上下文对象
 * @param {any} param1 - 参数1
 * @param {any} param2 - 参数2
 * @returns {Promise<number>} 返回后缀匹配阈值
 */
export async function suffixMatchThreshold(context: MyContext, param1: any, param2: any): Promise<number> {
  return getHiddenConfig(context, ConfigKey.DebugUseSuffix, {
    default: false,
  })
    ? 0
    : context.get(features.Features).suffixMatchThreshold(param1, param2);
}

/**
 * 获取FIM后缀长度阈值
 * @param {any} context - 上下文对象
 * @param {any} param1 - 参数1
 * @param {any} param2 - 参数2
 * @returns {Promise<number>} 返回FIM后缀长度阈值
 */
export async function fimSuffixLengthThreshold(context: MyContext, param1: any, param2: any): Promise<number> {
  return getHiddenConfig(context, ConfigKey.DebugUseSuffix, {
    default: false,
  })
    ? -1
    : context.get(features.Features).fimSuffixLengthThreshold(param1, param2);
}

/**
 * 构建信息类
 */
export class BuildInfo {
  private packageJson: any;

  constructor() {
    this.packageJson = packageJson;
  }

  /**
   * 检查是否为生产环境
   * @returns {boolean} 如果不是开发环境则返回 true
   */
  isProduction(): boolean {
    return "dev" != this.getBuildType();
  }

  /**
   * 获取构建类型
   * @returns {string} 返回构建类型
   */
  getBuildType(): string {
    return this.packageJson.buildType;
  }

  /**
   * 获取版本号
   * @returns {string} 返回版本号
   */
  getVersion(): string {
    return this.packageJson.version;
  }

  /**
   * 获取构建号
   * @returns {string} 返回构建号
   */
  getBuild(): string {
    return this.packageJson.build;
  }

  /**
   * 获取包名
   * @returns {string} 返回包名
   */
  getName(): string {
    return this.packageJson.name;
  }
}



/**
 * 检查是否为生产环境
 * @param {any} context - 上下文对象
 * @returns {boolean} 如果是生产环境则返回 true
 */
export function isProduction(context: MyContext): boolean {
  return context.get(BuildInfo).isProduction();
}

/**
 * 获取构建类型
 * @param {any} context - 上下文对象
 * @returns {string} 返回构建类型
 */
export function getBuildType(context: MyContext): string {
  return context.get(BuildInfo).getBuildType();
}

export function defaultBuildInfo () : BuildInfo {
  return new BuildInfo();
}


/**
 * 获取构建号
 * @param {any} context - 上下文对象
 * @returns {string} 返回构建号
 */
export function getBuild(context: MyContext): string {
  return context.get(BuildInfo).getBuild();
}

export class VscInfo {
  /**
   * 构造函数
   * @param {string} sessionId - 会话 ID
   * @param {string} machineId - 机器 ID
   * @param {string} vsCodeVersion - VSCode 版本
   */
  constructor(
    public readonly sessionId: string, 
    public readonly machineId: string, 
    public readonly vsCodeVersion: string
  ) {}
}
/**
 * 获取版本号
 * @param {Object} context - 上下文对象
 * @returns {string} 返回版本号
 */

export function getVersion(context: MyContext): string {
  return context.get(BuildInfo).getVersion();
}

/**
 * 获取测试用 VSCode 信息
 * @returns {VscInfo} 返回测试用 VSCode 信息对象
 */
export function getTestVscInfo(): VscInfo {
  return new VscInfo(
    "test-session-id",
    "test-machine-id",
    "test-vscode-version"
  );
}

// 定义名称和版本接口
interface NameAndVersion {
  name: string;
  version: string;
}

/**
 * 格式化名称和版本
 * @param {NameAndVersion} param0 - 包含名称和版本的对象
 * @returns {string} 返回格式化后的字符串
 */
export function formatNameAndVersion({ name, version }: NameAndVersion): string {
  return `${name}/${version}`;
}

/**
 * 编辑器和插件信息抽象类
 */
export abstract class EditorAndPluginInfo {
  abstract getEditorInfo(context: any): NameAndVersion;
  abstract getEditorPluginInfo(context: any): NameAndVersion;
}

/**
 * 获取编辑器版本头信息
 * @param {any} context - 上下文对象
 * @returns {Record<string, string>} 返回包含编辑器版本和插件版本的对象
 */
export function editorVersionHeaders(context: MyContext): Record<string, string> {
  const editorAndPluginInfo = context.get(EditorAndPluginInfo);
  return {
    "Editor-Version": formatNameAndVersion(editorAndPluginInfo.getEditorInfo(context)),
    "Editor-Plugin-Version": formatNameAndVersion(editorAndPluginInfo.getEditorPluginInfo(context)),
  };
}
