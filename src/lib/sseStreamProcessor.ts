// 2901
// 文件名: sseStreamProcessor.ts
// 描述: 处理SSE流和相关数据处理的模块

// 导入所需的模块
import { Logger, LogLevel } from './loggerManager';
//import * as telemetryManager from './telemetryManager';
import * as completionUtils from './completionUtils';
import { My_Context } from './contextType';


// 创建日志记录器
const logger = new Logger(LogLevel.INFO, "streamChoices");

// 定义接口和类型
interface RequestId {
  headerRequestId: string;
  completionId: string;
  created: number;
}

interface Solution {
  logprobs: number[][];
  top_logprobs: any[][];
  text: string[];
  text_offset: number[][];
  tokens: string[][];
}

interface YieldResult {
  solution: Solution;
  finishOffset: number | undefined;
  reason: string;
  requestId: RequestId;
  index: number;
}

// 分割数据块
export function splitChunk(chunk: string): [string[], string] {
  const lines = chunk.split("\n");
  const lastLine = lines.pop() || "";
  return [lines.filter((line) => line !== ""), lastLine];
}

// 处理SSE流
export async function* processSSE(
  context: My_Context,
  response: any,
  finishedCb: Function,
  telemetryProperties: any,
  cancellationToken?: { isCancellationRequested: boolean }
): AsyncGenerator<YieldResult, void, unknown> {
  const body = await response.body();
  body.setEncoding("utf8");

  let requestId = completionUtils.getRequestId(response);
  logger.debug(context, `requestId: ${requestId.headerRequestId}`);

  const solutions: { [key: number]: Solution | null } = {};
  let extraData = "";

  for await (const chunk of body) {
    if (cancellationToken?.isCancellationRequested) {
      logger.info(context, "Cancelled after awaiting body chunk");
      return void body.destroy();
    }

    logger.debug(context, "chunk.toString", chunk.toString());
    const [lines, remaining] = splitChunk(extraData + chunk.toString());
    extraData = remaining;

    for (const line of lines) {
      const data = line.slice("data:".length).trim();

      if (data === "[DONE]") {
        // 处理完成情况
        for (const [index, solution] of Object.entries(solutions)) {
          const numIndex = Number(index);
          if (solution) {
            yield {
              solution,
              finishOffset: undefined,
              reason: "DONE",
              requestId,
              index: numIndex,
            };
            if (cancellationToken?.isCancellationRequested) {
              logger.debug(context, "Cancelled after yielding on DONE");
              return void body.destroy();
            }
          }
        }
        return;
      }

      // 解析JSON数据
      let jsonData: any;
      try {
        jsonData = JSON.parse(data);
      } catch (error) {
        logger.error(context, "Error parsing JSON stream data", line);
        continue;
      }

      // 处理选择和错误
      if (jsonData.choices !== undefined || jsonData.error === undefined) {
        // 更新requestId
        if (requestId.created === 0) {
          requestId = completionUtils.getRequestId(response, jsonData);
          if (requestId.created === 0) {
            logger.error(
              context,
              `Request id invalid, should have "completionId" and "created": ${JSON.stringify(requestId)}`,
              requestId
            );
          }
        }

        // 处理每个选择
        for (let i = 0; i < jsonData.choices.length; i++) {
          const choice = jsonData.choices[i];
          logger.debug(context, "choice", choice);

          if (!(choice.index in solutions)) {
            solutions[choice.index] = {
              logprobs: [],
              top_logprobs: [],
              text: [],
              text_offset: [],
              tokens: [],
            };
          }

          const solution = solutions[choice.index];
          if (!solution) continue;

          // 更新解决方案数据
          solution.text.push(choice.text);
          solution.tokens.push(choice.logprobs?.tokens ?? []);
          solution.text_offset.push(choice.logprobs?.text_offset ?? []);
          solution.logprobs.push(choice.logprobs?.token_logprobs ?? []);
          solution.top_logprobs.push(choice.logprobs?.top_logprobs ?? []);

          // 检查是否完成
          let finishOffset: number | undefined;
          if (choice.finish_reason || choice.text.indexOf("\n") > -1) {
            finishOffset = await finishedCb(solution.text.join(""));
            if (cancellationToken?.isCancellationRequested) {
              logger.debug(context, "Cancelled after awaiting finishedCb");
              return void body.destroy();
            }
          }

          if (choice.finish_reason || finishOffset !== undefined) {
            const finishReason = choice.finish_reason ?? "client-trimmed";
            // telemetryManager.telemetry(
            //   context,
            //   "completion.finishReason",
            //   telemetryProperties.extendedBy({
            //     completionChoiceFinishReason: finishReason,
            //   })
            // );

            yield {
              solution,
              finishOffset,
              reason: JSON.stringify(choice.finish_reason),
              requestId,
              index: choice.index,
            };

            if (cancellationToken?.isCancellationRequested) {
              logger.debug(context, "Cancelled after yielding finished choice");
              return void body.destroy();
            }

            solutions[choice.index] = null;
          }
        }
      } else {
        logger.error(context, "Error in response:", jsonData.error.message);
      }
    }
  }

  // 处理剩余的解决方案
  for (const [index, solution] of Object.entries(solutions)) {
    const numIndex = Number(index);
    if (solution) {
      yield {
        solution,
        finishOffset: undefined,
        reason: "Iteration Done",
        requestId,
        index: numIndex,
      };
      if (cancellationToken?.isCancellationRequested) {
        logger.debug(context, "Cancelled after yielding after iteration done");
        return void body.destroy();
      }
    }
  }

  // 处理额外数据
  if (extraData.length > 0) {
    try {
      const jsonData = JSON.parse(extraData);
      if (jsonData.error !== undefined) {
        logger.error(context, `Error in response: ${jsonData.error.message}`, jsonData.error);
      }
    } catch (error) {
      logger.error(context, `Error parsing extraData: ${extraData}`);
    }
  }
}

// 准备返回解决方案
export function prepareSolutionForReturn(
  context: My_Context,
  result: YieldResult,
  telemetryData: any
): any {
  let text = result.solution.text.join("");
  let truncated = false;

  if (result.finishOffset !== undefined) {
    logger.debug(context, `solution ${result.index}: early finish at offset ${result.finishOffset}`);
    text = text.substring(0, result.finishOffset);
    truncated = true;
  }

  logger.info(
    context,
    `solution ${result.index} returned. finish reason: [${result.reason}] finishOffset: [${result.finishOffset}] completionId: [${result.requestId.completionId}] created: [${result.requestId.created}]`
  );

  const processedSolution = processSolution(result.solution);
  return completionUtils.convertToAPIChoice(context, text, processedSolution, result.index, result.requestId, truncated, telemetryData);
}

// 处理解决方案
function processSolution(solution: Solution): any {
  const result: any = {
    text: solution.text.join(""),
    tokens: solution.text,
  };

  if (solution.logprobs.length === 0) return result;

  const token_logprobs = solution.logprobs.flat();
  const top_logprobs = solution.top_logprobs.flat();
  const text_offset = solution.text_offset.flat();
  const tokens = solution.tokens.flat();

  return {
    ...result,
    logprobs: {
      token_logprobs,
      top_logprobs,
      text_offset,
      tokens,
    },
  };
}
