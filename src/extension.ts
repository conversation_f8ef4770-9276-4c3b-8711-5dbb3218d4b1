import * as vscode from 'vscode';
import * as completionProvider from './lib/inlineCompletionProvider'
import {MyContext} from './lib/myContext';

import {MultiLog,ConsoleLog,OutputChannelLog,LogTarget,LogVerbose} from './lib/loggerManager';
import {Clock} from './lib/clock';
import {ExtensionTextDocumentManager} from './lib/extensionTextDocumentManager';
import {TextDocumentManager} from './lib/textDocumentUtils';
import {ConfigProvider,BlockModeConfig,ConfigBlockModeConfig,BuildInfo,VscInfo,EditorAndPluginInfo,} from './lib/configurationManager';
import {VSCodeConfigProvider,makeVscInfo,VSCodeEditorInfo} from './lib/vscodeConfigManager';
import {Features} from './lib/featureManager';
import {extensionFileSystem} from './lib/extensionFileSystem';
import {MyFileSystem} from './lib/fileSystemOperations';
import { ExpConfigMaker ,ExpConfigNone} from './lib/experimentConfigFactory';
import {LocationFactory} from './lib/locationFactory';
import {ExtensionLocationFactory} from './lib/extensionLocationFactory'
import {CopilotStatusBar} from './lib/7254'
import {CMDShowActivationErrors,CMDToggleCopilot} from './lib/3060'
import { My_Context } from './lib/contextType';
import { StatusReporter } from './lib/statusReporter';
import { GhostTextDebounceManager } from './lib/ghostTextDebounceManager';
import {RuntimeMode} from './lib/runtimeModeManager';
import {OpenAIFetcher,LiveOpenAIFetcher} from './lib/openAIFetcher';
import * as networkingModule from './lib/networkingModule';

import * as documentTrackingModule from './lib/documentAccessTracker';     // 可能包含文档跟踪相关功能

const U = vscode.window.createOutputChannel("GitHub Copilot");

function init() {
  networkingModule.init(new BuildInfo().getVersion());
}
init();

function registerAsyncCommand(ctx: My_Context, command: string, callback: (...args: any[]) => Promise<any>): vscode.Disposable {
    return vscode.commands.registerCommand(command, async (...args: any[]) => {
      try {
        return await callback(...args);
      } catch (err) {
        //E.telemetryException(ctx, err, command);
      }
    });
  }

  
export function activate(context: vscode.ExtensionContext) {
    console.log('Auto Complete extension is now active!');
    const myContext = new MyContext();
    myContext.set(BuildInfo, new BuildInfo());

    const multiLog = new MultiLog([
        new ConsoleLog(console),
        new OutputChannelLog(U),
    ]);
    myContext.set(LogTarget, multiLog);
    myContext.set(LogVerbose, new LogVerbose(true));
  
    myContext.set(Clock, new Clock());
    
    myContext.set(TextDocumentManager, new ExtensionTextDocumentManager());

    myContext.set(ConfigProvider, new VSCodeConfigProvider());


    myContext.set(Features, new Features(myContext));


    myContext.set(MyFileSystem, extensionFileSystem);

    myContext.set(ExpConfigMaker, new ExpConfigNone());

    myContext.set(LocationFactory, new ExtensionLocationFactory());

    myContext.set(RuntimeMode, RuntimeMode.fromEnvironment(false));

    myContext.set(OpenAIFetcher, new LiveOpenAIFetcher());
    myContext.set(VscInfo, makeVscInfo());
    myContext.set(EditorAndPluginInfo, new VSCodeEditorInfo());
  
    const helixFetcher = new networkingModule.HelixFetcher(myContext);
    myContext.set(networkingModule.Fetcher, helixFetcher);


    const K = (function (myContext, context) {
        const n = new CopilotStatusBar(myContext);
        context.subscriptions.push(
            registerAsyncCommand(myContext, CMDToggleCopilot, async () => {
            await n.toggleStatusBar();
          })
        );
        context.subscriptions.push(
            registerAsyncCommand(myContext, CMDShowActivationErrors, async () => {
            await n.showActivationErrors(U);
          })
        );
        context.subscriptions.push(n.getStatusBarItem());
        return n;
      })(myContext, context);

      myContext.set(StatusReporter, K);

      myContext.set(BlockModeConfig, new ConfigBlockModeConfig());

      myContext.set(GhostTextDebounceManager, new GhostTextDebounceManager(100000));
      console.log('Attempting to registerDocumentTracker');
      documentTrackingModule.registerDocumentTracker(myContext);

    try {
        console.log('Attempting to register ghost text...');
        const provider = completionProvider.registerGhostText(myContext);
        console.log('Ghost text registered successfully');
        context.subscriptions.push(...provider);
        console.log('Ghost text subscriptions added successfully');

        // 注册一个简单的命令来测试扩展
        let disposable = vscode.commands.registerCommand('auto-complete.test', () => {
            vscode.window.showInformationMessage('Auto Complete extension is working!');
        });

        context.subscriptions.push(disposable);
    } catch (error) {
        console.error('Error in extension activation:', error);
        vscode.window.showErrorMessage('Failed to activate Auto Complete extension. Check the console for more details.');
    }

    console.log('Auto Complete extension activation completed');
}

export function deactivate() {}
