import * as fs from "fs";
import * as languageParserAndQuerier from "./languageParserAndQuerier";
import * as priorities from "./promptElementManager";

import {
  getImportPath,
  getDirPathForAccessType,
  getAbsolutePathForImport,
} from "./ts_import";
import { cosineSimilarity } from "./TF-IDF";
import { SyntaxNode } from "tree-sitter";
import { TreeCursor ,Query } from "web-tree-sitter";

interface NodeSimilarityInfo {
  node: SyntaxNode; // 节点
  similarity: number; // 相似度
  nodeIndex: number; // 节点索引
}

interface FormateBodyParam {
  sourceCode: string;
  modifiedCode: string;
  curOffset: number;
  modifiedOffset: number;
  functionNodeInfo: FunctionNodeInfo;
  functionNodes: SyntaxNode[];
}

interface FunctionNodeInfo {
  curFunNode: NodeSimilarityInfo; // 节点
  lastFunNode: NodeSimilarityInfo; // 节点
  nextFunNode: NodeSimilarityInfo; // 节点
  curDocSimilarityFunNode: NodeSimilarityInfo[]; // 节点
}
var functionNodeInfo: FunctionNodeInfo | null = null;

const language_type = languageParserAndQuerier.WASMLanguage.Java;

// 加载文件内容
const sourceCode1 = fs.readFileSync(
  "/Users/<USER>/Documents/workspace/auto-complete/src/input.ts",
  "utf8"
);

// 解析 sourceCode 为语法树的方法
async function parseSourceCodeToAST(
  sourceCode: string,
  offset: number
): Promise<string[]> {
  //@ts-ignore
  var curTree = undefined;
  curTree = (await languageParserAndQuerier.parseTree(
    language_type,
    sourceCode
  )) as any;
  let rootNode = curTree.rootNode;
  //获取导入路径
  let importUri = getImportPath(rootNode);
  let absoluteImportPath = getAbsolutePathForImport(importUri, []);
  //包含当前文件的package路径 TODO
  //获取当前所有function节点
  const functionNodes = languageParserAndQuerier.queryFunctions(
    language_type,
    rootNode
  );
  // 查找给定偏移量所在的函数节点
  let currentIndex = -1;
  for (let i = 0; i < functionNodes.length; i++) {
    const node = functionNodes[i];
    if (node.startIndex <= offset && offset <= node.endIndex) {
      currentIndex = i;
      break;
    }
  }
  //查找光标偏移量所能访问的变量
  const accessFieldTypes = traverseAndFindFields(
    rootNode.walk(),
    functionNodes[currentIndex]?.id ?? -1
  );
  // 获取访问字段类型的URI
  const accessFieldTypeUri = getDirPathForAccessType(
    absoluteImportPath,
    accessFieldTypes
  );

  // 创建 FunctionNodeInfo 实例
  if (currentIndex !== -1) {
    functionNodeInfo = {
      curFunNode: {
        node: functionNodes[currentIndex],
        similarity: 100,
        nodeIndex: currentIndex,
      },
      lastFunNode:
        currentIndex > 0
          ? {
              node: functionNodes[currentIndex - 1],
              similarity: 0,
              nodeIndex: currentIndex - 1,
            }
          : { node: null, similarity: 0, nodeIndex: -1 },
      nextFunNode:
        currentIndex < functionNodes.length - 1
          ? {
              node: functionNodes[currentIndex + 1],
              similarity: 0,
              nodeIndex: currentIndex + 1,
            }
          : { node: null, similarity: 0, nodeIndex: -1 },
      curDocSimilarityFunNode: [], // 这里需要根据实际情况计算相似度
    };
  }
  // findTopKSimilarForNode,传入遍历functionNodes,当前函数节点 ，计算topk相似度，返回并加入到curDocSimilarityFunNode 中
  if (functionNodeInfo) {
    const topKSimilar = findTopKSimilarForNode(
      functionNodes,
      functionNodeInfo.curFunNode.node,
      5,
      0.5
    );
    functionNodeInfo.curDocSimilarityFunNode = topKSimilar.map((item) => ({
      node: item.node,
      similarity: item.similarity,
      nodeIndex: item.index,
    }));
  }
  // 调用 traverseTreeForCurDoc
  const formateBodyParam: FormateBodyParam = {
    sourceCode: sourceCode,
    modifiedCode: "",
    curOffset: offset,
    modifiedOffset: offset,
    functionNodeInfo: functionNodeInfo,
    functionNodes: functionNodes,
  };
  const priorityManager = new priorities.Priorities();

  // 创建一个top的优先级，如果
  const topPriority = priorityManager.justBelow(priorities.Priorities.TOP); // 0.5

  //导入上下文
  const importContextPriority = priorityManager.justBelow(topPriority); //0.4375

  // 相邻标签
  const recentEditPriority = priorityManager.justBelow(importContextPriority);

  const promptWishlist = new priorities.PromptWishlist(
    priorities.LineEndingOptionsType.ConvertToUnix
  );
  promptWishlist.append(
    priorities.PromptElementKind.ImportedFile,
    importContextPriority,
    "/usr/local/lib/node_modules/@types/node/index.d.ts",
    "",
    0
  );

  modifyFunctions(formateBodyParam);

  console.log("Modified code:", formateBodyParam.modifiedCode);
  console.log("Modified code:", formateBodyParam.modifiedOffset);

  // 根据accessFieldTypeUri 获取关联文件
  if (accessFieldTypeUri.length > 0) {
    console.log("Access Field Type URIs:", accessFieldTypeUri);
  } else {
    console.log("No access field type URIs found.");
  }

  return importUri;
}

// 使用方法解析 sourceCode
(async () => {
  console.log(1111);
  console.log(1111);
  const ast = await parseSourceCodeToAST(sourceCode1, 450);
})();

/**
 * 比较字符串数组中的每个元素与给定字符串的相似度，返回 top k 个最相似的结果
 * @param b 字符串数组
 * @param a 要比较的字符串
 * @param k 返回的 top k 个结果数量
 * @returns 返回 top k 个最相似的字符串及其相似度
 */
export function findTopKSimilarForNode(
  targetNodes: SyntaxNode[],
  sourceNode: SyntaxNode,
  k: number,
  j: number
): Array<{ index: number; similarity: number; node: SyntaxNode }> {
  // 为空直接返回
  if (!targetNodes || targetNodes.length === 0) {
    return [];
  }

  // 使用for循环遍历targetNodes
  const similarities: Array<{
    index: number;
    similarity: number;
    node: SyntaxNode;
  }> = [];
  for (let i = 0; i < targetNodes.length; i++) {
    const node = targetNodes[i];
    if (node.id !== sourceNode.id) {
      // 如果节点id相同则跳过
      continue;
    }
    const similarity = cosineSimilarity(node.text, sourceNode.text);
    // 相似度低于j不添加
    if (similarity >= j) {
      similarities.push({ index: i, similarity, node });
    }
  }
  // 按相似度降序排序
  similarities.sort((a, b) => b.similarity - a.similarity);
  // 返回 top k 个结果
  return similarities.slice(0, k);
}
function modifyFunctions(formateBodyParam: FormateBodyParam) {
  const {
    sourceCode,
    functionNodes: functions,
    functionNodeInfo,
    curOffset: offset,
  } = formateBodyParam;
  let curFunNodeId;
  const keepIndices = new Set();
  // 如果当前函数节点存在，保留它
  // 填充 fullFunNodeIds
  if (functionNodeInfo) {
    // 添加 curFunNode 的 id
    if (functionNodeInfo.curFunNode.node) {
      keepIndices.add(functionNodeInfo.curFunNode.node.id);
      curFunNodeId = functionNodeInfo.curFunNode.node.id;
    }
    // 添加 lastFunNode 的 id
    if (functionNodeInfo.lastFunNode.node) {
      keepIndices.add(functionNodeInfo.lastFunNode.node.id);
    }
    // 添加 nextFunNode 的 id
    if (functionNodeInfo.nextFunNode.node) {
      keepIndices.add(functionNodeInfo.nextFunNode.node.id);
    }
    // 添加 curDocSimilarityFunNode 的 id
    functionNodeInfo.curDocSimilarityFunNode.forEach((nodeSimilarity) => {
      if (nodeSimilarity.node) {
        keepIndices.add(nodeSimilarity.node.id);
      }
    });
  }
  keepIndices.add(functionNodeInfo.curFunNode.node.id);
  keepIndices.add(functionNodeInfo.curFunNode.node.id);

  // 一次性计算所有需要替换的区间
  const edits = [];
  for (let i = 0; i < functions.length; i++) {
    if (!keepIndices.has(i)) {
      const func = functions[i];
      const bodyNode = func.childForFieldName("body");
      if (bodyNode && bodyNode.endIndex - bodyNode.startIndex > 2) {
        edits.push({ start: bodyNode.startIndex, end: bodyNode.endIndex });
      }
    }
  }

  // 按从后往前顺序修改，避免索引偏移
  edits.sort((a, b) => b.start - a.start);

  let modifiedCode = sourceCode;
  let modifiedOffset = offset;
  for (const edit of edits) {
    // 替换函数体为空
    const oldLength = edit.end - edit.start;
    const newLength = 2; // "{}" 的长度
    modifiedCode =
      modifiedCode.slice(0, edit.start) + "{}" + modifiedCode.slice(edit.end);

    // 计算curOffset变化后的偏移量
    if (edit.start < offset) {
      modifiedOffset -= oldLength - newLength;
    }
  }

  formateBodyParam.modifiedCode = modifiedCode;
  formateBodyParam.modifiedOffset = modifiedOffset;

  return modifiedCode;
}

function traverseAndFindFields(cursor: TreeCursor, curFunId: number): string[] {
  let fields: string[] = [];
  do {
    const node = cursor.currentNode;

    if (isFieldDeclaration(node) || node.id == curFunId) {
      const type_identifiers = languageParserAndQuerier.queryTypeIdentifiers(
        language_type,
        node
      );
      //遍历type_identifiers
      type_identifiers.filter((item: any) =>
        fields.push(item[0].captures[0].node.text)
      );
    }
  } while (cursor.gotoNextSibling());
  return fields;
}

function isFieldDeclaration(node: any): boolean {
  return node.type === "local_variable_declaration";
}
