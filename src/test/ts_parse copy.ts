import * as fs from "fs";
import * as languageParserAndQuerier from "./languageParserAndQuerier";
//@ts-ignore
import Parser from "tree-sitter";
import { TreeCursor } from "web-tree-sitter";

const JAVA_LANGUAGE = languageParserAndQuerier.WASMLanguage.Java;

// 加载文件内容
const sourceCode = fs.readFileSync(
  "/Users/<USER>/Documents/workspace/auto-complete/src/input.ts",
  "utf8"
);
//@ts-ignore
var tree = undefined;
(async () => {
  tree = (await languageParserAndQuerier.parseTree(
    JAVA_LANGUAGE,
    sourceCode
  )) as any;
  var ab = languageParserAndQuerier.queryFunctions(
    JAVA_LANGUAGE,
    tree.rootNode
  );
  // let currentIndex = 450;
  // const ancestorWithSiblings = getAncestorWithSiblings(
  //   currentIndex,
  //   tree,
  //   sourceCode
  // );
  // console.log(ancestorWithSiblings.text);

  // 存储修改后的代码片段
  let modifiedCode = "";
  let lastIndex = 0;
  function traverseTree(cursor: TreeCursor) {
    do {
      const node = cursor.currentNode;
      const isfun = languageParserAndQuerier.isFunction(JAVA_LANGUAGE, node);
      console.log(node.type, node.startIndex, node.endIndex);
      if (isfun) {
        const body = node.childForFieldName("body");
        const de = languageParserAndQuerier.queryTypeIdentifiers(
          JAVA_LANGUAGE,
          node
        );

        if (body) {
          traverseAndFindFields(node.walk(), true, body);
          // 提取方法签名
          const bodyStart = body.startIndex;
          const bodyEnd = body.endIndex;

          // 保留方法签名和参数，移除方法体
          modifiedCode += sourceCode.slice(lastIndex, bodyStart) + ";";
          lastIndex = bodyEnd;
        }
      } else {
        if (cursor.gotoFirstChild()) {
          // 如果有子节点，递归进入子节点
          traverseTree(cursor);
          cursor.gotoParent(); // 回到父节点以继续遍历兄弟节点
        }
      }
    } while (cursor.gotoNextSibling());
  }
  // 开始遍历
  traverseTree(tree.rootNode.walk());

  // 添加剩余的代码
  modifiedCode += sourceCode.slice(lastIndex);

  // 移除多余的空行
  modifiedCode = removeExtraEmptyLines(modifiedCode);

  // 输出到文件
  fs.writeFileSync("./output.ts", modifiedCode);
})();

function removeExtraEmptyLines(code: string): string {
  // 将连续的多个空行替换为单个空行
  return code.replace(/\n\s*\n\s*\n/g, "\n\n");
}

function getAncestorWithSiblings(index: number, tree: any, sourceCode: string) {
  // 跳过空白字符
  while (index >= 0 && /\s/.test(sourceCode[index])) index--;
  const currentNode = tree.rootNode.descendantForIndex(index);
  return languageParserAndQuerier.getAncestorWithSiblingFunctions(
    JAVA_LANGUAGE,
    currentNode
  );
}

function isFieldDeclaration(node: any): boolean {
  return node.type === "local_variable_declaration";
}
let fields: any[] = [];
let offset = 450;
function traverseAndFindFields(cursor: TreeCursor, isin: boolean, bodyI: any) {
  do {
    const node = cursor.currentNode;
    if (node.startIndex >= offset) {
      return;
    }
    if (isFieldDeclaration(node)) {
      fields.push(node);
    } else {
      if (isin) {
        if (cursor.gotoFirstChild()) {
          traverseAndFindFields(cursor, false, bodyI);
        }
        cursor.gotoParent();
      } else if ((node.id = bodyI.id)) {
        traverseAndFindFields(cursor.currentNode.walk(), true, bodyI);
      } else {
        const body = cursor.currentNode.childForFieldName("body");
        if (body) {
          traverseAndFindFields(cursor.currentNode.walk(), true, body);
        }
      }
    }
  } while (cursor.gotoNextSibling());
  return;
}

// //TODO
// // 获取当前光标位置的函数节点
// function traverseTreeForCurDoc(
//   cursor: TreeCursor,
//   formateBodyParam: FormateBodyParam
// ) {
//   do {
//     const node = cursor.currentNode;
//     const isfun = languageParserAndQuerier.isFunction(language_type, node);
//     console.log(node.type, node.startIndex, node.endIndex);
//     if (isfun) {
//       if (formateBodyParam.fullFunNodeIds.includes(node.id)) {
//         continue;
//       }
//       const body = node.childForFieldName("body");
//       if (body) {
//         // 提取方法签名
//         const bodyStart = body.startIndex;
//         const bodyEnd = body.endIndex;
//         // 保留方法签名和参数，移除方法体
//         formateBodyParam.modifiedCode +=
//           formateBodyParam.sourceCode.slice(
//             formateBodyParam.lastIndex,
//             bodyStart
//           ) + ";";
//         formateBodyParam.lastIndex = bodyEnd;
//       }
//     } else {
//       if (cursor.gotoFirstChild()) {
//         // 如果有子节点，递归进入子节点
//         traverseTreeForCurDoc(cursor, formateBodyParam);
//         cursor.gotoParent(); // 回到父节点以继续遍历兄弟节点
//       }
//     }
//   } while (cursor.gotoNextSibling());
//   return formateBodyParam.modifiedCode;
// }
