// 导入自然语言处理库 natural 和文件系统模块
import * as natural from 'natural';
import * as fs from 'fs';

function preprocessText(text: string): string {
  // 去除特殊字符或不必要的空白
  return text.trim();
}

/**
 * 从文件中读取文本内容
 * @param filePath 文件路径
 * @returns 返回文件内容
 */
function readTextFromFile(filePath: string): string {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error);
    return '';
  }
}

/**
 * 计算两个文本之间的余弦相似度
 * @param text1 第一个文本
 * @param text2 第二个文本
 * @returns 返回两个文本之间的余弦相似度（0到1之间的数值）
 */

export function cosineSimilarity(text1: string, text2: string): number {
  // 创建 TF-IDF（词频-逆文档频率）实例
  const tfidf = new natural.TfIdf();
  // 预处理文本
  const processedText1 = preprocessText(text1);
  const processedText2 = preprocessText(text2);
  // 将两个文本添加到 TF-IDF 实例中
  tfidf.addDocument(processedText1);
  tfidf.addDocument(processedText2);

  // 初始化两个文本的特征向量
  const vec1: number[] = [];
  const vec2: number[] = [];

  // 获取第一个文本的 TF-IDF 值并存储在 vec1 中
  tfidf.listTerms(0).forEach((item) => {
    vec1.push(item.tfidf);
  });

  // 获取第二个文本的 TF-IDF 值并存储在 vec2 中
  tfidf.listTerms(1).forEach((item) => {
    vec2.push(item.tfidf);
  });

  // 计算余弦相似度
  // 1. 计算点积
  var dotProduct;
  if (vec2.length > vec1.length) {
    dotProduct = vec1.reduce((sum, val, i) => sum + val * vec2[i], 0);
  } else {
    dotProduct = vec2.reduce((sum, val, i) => sum + val * vec1[i], 0);
  }
  // 2. 计算向量1的模
  const magnitude1 = Math.sqrt(vec1.reduce((sum, val) => sum + val * val, 0));
  // 3. 计算向量2的模
  const magnitude2 = Math.sqrt(vec2.reduce((sum, val) => sum + val * val, 0));

  // 返回余弦相似度（点积除以两个向量模的乘积）
  return dotProduct / (magnitude1 * magnitude2);
}

// // 示例使用
// const text1 = readTextFromFile(
//   "/Users/<USER>/Documents/workspace/auto-complete/src//test/a.txt"
// );
// const text2 = readTextFromFile(
//   "/Users/<USER>/Documents/workspace/auto-complete/src//test/b.txt"
// );

// if (text1 && text2) {
//   // 计算两个示例文本的余弦相似度
//   const similarity = cosineSimilarity(text1, text2);
//   // 输出结果
//   console.log(`Cosine Similarity: ${similarity}`);
// } else {
//   console.error("Failed to read one or both files.");
// }

/**
 * 比较字符串数组中的每个元素与给定字符串的相似度，返回 top k 个最相似的结果
 * @param b 字符串数组
 * @param a 要比较的字符串
 * @param k 返回的 top k 个结果数量
 * @returns 返回 top k 个最相似的字符串及其相似度
 */
export function findTopKSimilar(
  b: string[],
  a: string,
  k: number,
  j: number
): Array<{ index: number; similarity: number }> {
  // 计算每个字符串与 a 的相似度，并保存原始索引
  const similarities = b.map((str, index) => ({
    index,
    similarity: cosineSimilarity(str, a),
  }));

  // 按相似度降序排序
  similarities.sort((a, b) => b.similarity - a.similarity);

  // 返回 top k 个结果
  return similarities.slice(0, k).filter((item) => item.similarity > j);
}

// 示例使用
const stringArray = [
  'The quick brown fox jumps over the lazy dog',
  'A journey of a thousand miles begins with a single step',
  'To be or not to be, that is the question',
  'All that glitters is not gold',
  "Where there's a will, there's a way",
];

const targetString = 'The fast brown fox leaps over the sleepy canine';
const k = 3;

const topKResults = findTopKSimilar(stringArray, targetString, k, 0.1);
console.log(`Top ${k} similar strings:`);
topKResults.forEach((result) => {
  console.log(`Index: ${result.index}, "" (Similarity: ${result.similarity.toFixed(4)})`);
});
