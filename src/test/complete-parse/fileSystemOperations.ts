/**
 * 3055271
 * 文件系统操作模块
 *
 * 该模块提供了一个简化的文件系统操作接口，包括：
 * 1. 读取文件内容
 * 2. 获取文件的最后修改时间
 * 3. 获取文件的详细状态信息
 *
 * 这个模块主要用于在 Copilot 插件中处理文件相关的操作，
 * 提供了一个统一的接口来访问文件系统，简化了文件操作的复杂性。
 */
import * as fs from 'fs';

/**
 * 文件系统接口定义
 */
export abstract class MyFileSystem {
  /**
   * 读取文件内容
   * @param filePath 文件路径
   * @returns Promise<Buffer> 文件内容的Buffer
   */
  abstract readFile(filePath: string): Promise<Buffer>;

  /**
   * 获取文件的最后修改时间
   * @param filePath 文件路径
   * @returns Promise<number> 文件的修改时间戳（毫秒）
   */
  abstract mtime(filePath: string): Promise<number>;

  /**
   * 获取文件的详细状态信息
   * @param filePath 文件路径
   * @returns Promise<FileStats> 文件状态对象
   */
  abstract stat(filePath: string): Promise<FileStats>;
}

/**
 * 文件状态信息接口
 */
export abstract class FileStats {
  /**
   * 文件创建时间（毫秒时间戳）
   */
  abstract ctime: number;

  /**
   * 文件修改时间（毫秒时间戳）
   */
  abstract mtime: number;

  /**
   * 文件大小（字节）
   */
  abstract size: number;
}

/**
 * 默认文件系统实现
 */
export class DefaultFileSystem extends MyFileSystem {
  // 读取文件内容
  async readFile(filePath: string): Promise<Buffer> {
    return fs.promises.readFile(filePath);
  }

  // 获取文件的最后修改时间
  async mtime(filePath: string): Promise<number> {
    const stats = await fs.promises.stat(filePath);
    return stats.mtimeMs;
  }

  // 获取文件的详细状态信息
  async stat(filePath: string): Promise<FileStats> {
    const fileStats = await fs.promises.stat(filePath);
    return {
      ctime: fileStats.ctimeMs,
      mtime: fileStats.mtimeMs,
      size: fileStats.size,
    };
  }
}

// 创建默认文件系统实例
export const defaultFileSystem: MyFileSystem = new DefaultFileSystem();
