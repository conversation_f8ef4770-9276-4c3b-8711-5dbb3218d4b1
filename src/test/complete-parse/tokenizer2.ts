/**
 * 305594
 * tokenizer.ts
 * 
 * 这个文件实现了一个分词器（tokenizer）和相关的文本处理功能。
 * 主要功能包括：
 * 1. 初始化和配置分词器
 * 2. 将文本分割成token
 * 3. 将token转换回文本
 * 4. 处理文本的截取和转换操作
 * 
 * 该模块主要用于自然语言处理任务，特别是在机器学习和文本生成模型中使用。
 */

import * as fs from "fs";
import * as path from "path";
import { TextDecoder, TextEncoder } from 'util';

// 定义类型
type TokenId = number;
type TokenMap = Record<string | number, TokenId>;
type IdTokenMap = Record<TokenId, string>;

// 创建一个指定范围的数组
const createRange = (start: number, end: number): number[] => Array.from(Array(end).keys()).slice(start);

// 获取字符的Unicode编码
const getCharCode = (char: string): number => char.charCodeAt(0);

// 创建UTF-8解码器
const utf8Decoder = new TextDecoder("utf-8");

// 解码Uint8Array为字符串
const decodeUint8Array = (array: number[]): string => utf8Decoder.decode(new Uint8Array(array));

// 创建数组中相邻元素的对
function createPairs(array: number[]): Set<number[]> {
  const pairs = new Set<number[]>();
  let prev = array[0];
  for (let i = 1; i < array.length; i++) {
    const current = array[i];
    pairs.add([prev, current]);
    prev = current;
  }
  return pairs;
}

// 创建UTF-8编码器
const utf8Encoder = new TextEncoder();

// 定义用于分割文本的正则表达式
const tokenRegex =
  /'s|'t|'re|'ve|'m|'ll|'d| ?\p{L}+| ?\p{N}+| ?[^\s\p{L}\p{N}]+|\s+(?!\S)|\s+/gu;

let isTokenizerInitialized = false;
const tokenToString: IdTokenMap = {};
let bytePairEncoding: Record<string, number> = {};
let tokenToId: TokenMap = {};
let byteToUnicode = new Map<number, string>();
let unicodeToByte = new Map<string, number>();
const tokenCache = new Map<string, TokenId[]>();

// 辅助函数：安全地获取 Unicode 字符
function safeGetUnicode(byte: number): string {
  return byteToUnicode.get(byte) || String.fromCharCode(byte);
}

// 辅助函数：安全地获取字节
function safeGetByte(char: string): number {
  return unicodeToByte.get(char) || char.charCodeAt(0);
}

// 初始化分词器
function initializeTokenizer(): void {
  if (isTokenizerInitialized) return;
  
  // 加载token到ID的映射
  tokenToId = JSON.parse(
    fs.readFileSync(path.resolve(__dirname, "..", "tokenizer.json"), 'utf-8')
  );
  
  // 创建ID到token的映射
  Object.keys(tokenToId).forEach((key) => {
    tokenToString[tokenToId[key]] = key;
  });
  
  // 加载BPE词汇表
  const lines = fs
    .readFileSync(path.resolve(__dirname, "..", "vocab.bpe"), "utf-8")
    .split("\n");
  
  // 解析BPE合并规则
  const bpeMerges = lines.slice(1, lines.length - 1).map((line) =>
    line.split(/(\s+)/).filter(function (part) {
      return part.trim().length > 0;
    })
  );
  
  // 初始化BPE编码
  bytePairEncoding = ((merges: string[][], vocabSize: number[]) => {
    const bpe: Record<string, number> = {};
    merges.forEach((merge, i) => {
      bpe[merge.join('')] = vocabSize[i];
    });
    return bpe;
  })(bpeMerges, createRange(0, bpeMerges.length));
  
  // 初始化字节到Unicode的映射
  (function (byteToUnicode: Map<number, string>) {
    const range = createRange(getCharCode("!"), getCharCode("~") + 1).concat(
      createRange(getCharCode("¡"), getCharCode("¬") + 1),
      createRange(getCharCode("®"), getCharCode("ÿ") + 1)
    );
    let extendedBytes: number[] = range.slice();
    let n = 0;
    for (let b = 0; b < 256; b++) {
      if (range.includes(b)) {
        range.push(b);
        extendedBytes.push(256 + n);
        n += 1;
      }
    }
    const extendedChars: string[] = extendedBytes.map((n) => String.fromCharCode(n));
    for (let i = 0; i < range.length; i++){
      byteToUnicode.set(range[i], extendedChars[i]);
    } 
  })(byteToUnicode);
  
  // 创建Unicode到字节的反向映射
  byteToUnicode.forEach((value, key) => {
    unicodeToByte.set(value, key);
  });
  
  isTokenizerInitialized = true;
}

// 对文本进行分词
function tokenize(text: string): TokenId[] {
  // 如果已缓存，直接返回结果
  if (tokenCache.has(text)) return tokenCache.get(text)!;
  
  // 将文本转换为字节序列，然后映射到Unicode
  let bytes = Array.from(utf8Encoder.encode(text)).map((byte) => safeGetByte(safeGetUnicode(byte)));
  
  // 创建字节对
  let pairs = createPairs(bytes);
  if (!pairs.size) return bytes.map((byte) => tokenToId[safeGetUnicode(byte)] || 0);
  
  // BPE算法的主循环
  while (true) {
    const minPairs: Record<number, number[]> = {};
    Array.from(pairs).forEach((pair) => {
      const pairStr = pair.map(b => safeGetUnicode(b)).join('');
      const freq = bytePairEncoding[pairStr];
      minPairs[isNaN(freq) ? 1e11 : freq] = pair;
    });
    
    // 找到最小频率的对
    const minKey = Math.min(...Object.keys(minPairs).map(Number));
    const bigram = minPairs[minKey];
    
    // 如果没有在BPE编码中找到这个对，退出循环
    if (!(bigram.map(b => safeGetUnicode(b)).join('') in bytePairEncoding)) break;
    
    const first = bigram[0];
    const second = bigram[1];
    let newBytes: number[] = [];
    let i = 0;
    
    // 合并字节对
    while (i < bytes.length) {
      const j = bytes.indexOf(first, i);
      if (j === -1) {
        newBytes.push(...bytes.slice(i));
        break;
      }
      newBytes.push(...bytes.slice(i, j));
      i = j;
      if (bytes[i] === first && i < bytes.length - 1 && bytes[i + 1] === second) {
        newBytes.push(safeGetByte(safeGetUnicode(first) + safeGetUnicode(second)));
        i += 2;
      } else {
        newBytes.push(bytes[i]);
        i += 1;
      }
    }
    
    bytes = newBytes;
    if (bytes.length === 1) break;
    pairs = createPairs(bytes);
  }
  
  // 将字节序列转换为token ID
  const tokens = bytes.map((byte) => tokenToId[safeGetUnicode(byte)] || 0);
  tokenCache.set(text, tokens);
  return tokens;
}

// 对文本进行分词，处理匹配的子字符串
function tokenizeText(text: string): TokenId[] {
  initializeTokenizer();
  let tokens: TokenId[] = [];
  const matches = text.match(tokenRegex) || [];
  for (let match of matches) {
    const matchTokens = tokenize(match);
    tokens.push(...matchTokens);
  }
  return tokens;
}

// 从文本末尾取指定数量的token
function takeLastTokens(text: string, maxTokens: number): string {
  if (maxTokens <= 0) return "";
  
  // 初始估计需要取的字节数
  let bytesToTake = Math.min(text.length, 4 * maxTokens);
  let truncatedText = text.slice(-bytesToTake);
  return truncatedText
  // let tokens = tokenizeText(truncatedText);
  
  // // 如果token数量不足，逐步增加取的字节数
  // while (tokens.length < maxTokens + 2 && bytesToTake < text.length) {
  //   bytesToTake = Math.min(text.length, bytesToTake + 1 * maxTokens);
  //   truncatedText = text.slice(-bytesToTake);
  //   tokens = tokenizeText(truncatedText);
  // }
  
  // return tokens.length < maxTokens ? text : detokenize(tokens.slice(-maxTokens));
}

// 将token转换回文本
function detokenize(tokens: TokenId[]): string {
  initializeTokenizer();
  let text = tokens.map((token) => tokenToString[token]).join("");
  text = decodeUint8Array(text.split("").map((char) => unicodeToByte.get(char)!));
  return text;
}

// 导出模块
export = {
  // 准备分词器
  prepareTokenizer: initializeTokenizer,
  
  // 对文本进行分词
  tokenize: tokenizeText,
  
  // 将分词结果转换为字符串数组
  tokenize_strings: function (text: string): string[] {
    return tokenizeText(text).map((token) => 
      decodeUint8Array(tokenToString[token].split("").map((char) => unicodeToByte.get(char)!))
    );
  },
  
  // 获取文本的token长度
  tokenLength: function (text: string): number {
    return tokenizeText(text).length;
  },
  
  // 从文本末尾取指定数量的token
  takeLastTokens: takeLastTokens,
  
  // 从文本末尾取指定数量的token，并从最后一个换行符开始
  takeLastLinesTokens: function (text: string, maxTokens: number): string {
    const lastTokens = takeLastTokens(text, maxTokens);
    if (lastTokens.length === text.length || "\n" === text[text.length - lastTokens.length - 1]) 
      return lastTokens;
    
    let newlineIndex = lastTokens.indexOf("\n");
    return lastTokens.substring(newlineIndex + 1);
  },
  
  // 从文本开头取指定数量的token
  takeFirstTokens: function (text: string, maxTokens: number): { text: string; tokens: TokenId[] } {
    if (maxTokens <= 0)
      return {
        text: "",
        tokens: [],
      };
    
    // 初始估计需要取的字节数
    let bytesToTake = Math.min(text.length, 4 * maxTokens);
    let truncatedText = text.slice(0, bytesToTake);
    
    let tokens = tokenizeText(truncatedText);
    

    return {
      text: text,
      tokens: tokens,
    }

    // // 如果token数量不足，逐步增加取的字节数
    // while (tokens.length < maxTokens + 2 && bytesToTake < text.length) {
    //   bytesToTake = Math.min(text.length, bytesToTake + 1 * maxTokens);
    //   truncatedText = text.slice(0, bytesToTake);
    //   tokens = tokenizeText(truncatedText);
    // }
    
    // return tokens.length < maxTokens
    //   ? {
    //       text: text,
    //       tokens: tokens,
    //     }
    //   : {
    //       text: detokenize(tokens.slice(0, maxTokens)),
    //       tokens: tokens.slice(0, maxTokens),
    //     };
  },
  
  // 将token转换回文本
  detokenize: detokenize,
};
