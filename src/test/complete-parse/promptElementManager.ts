/**
 * 3055456
 * promptElementManager.js
 *
 * 该文件主要用于管理和处理提示元素，包括提示背景、选择、范围和优先级。
 *
 * 主要功能：
 * 1. 定义和管理提示元素的类型（PromptElementKind）
 * 2. 处理提示背景信息（PromptBackground）
 * 3. 管理提示选择（PromptChoices）
 * 4. 计算和存储提示元素范围（PromptElementRanges）
 * 5. 实现提示愿望列表功能（PromptWishlist），包括内容管理和提示填充
 * 6. 处理优先级系统（Priorities）
 *
 * 该模块是Copilot提示系统的核心组件，负责组织和优化提示内容，
 * 以提供最相关和有效的代码建议。
 */

// 导入所需模块
// import * as promptManager from './promptManager';
// import * as tokenizer from './tokenizer';
//@ts-ignore
import tokenUtil from './tokenizer2';
export enum LineEndingOptionsType {
  ConvertToUnix = 'unix',
  KeepOriginal = 'keep',
}
/**
 * 提示元素的类型枚举
 */
export enum PromptElementKind {
  BeforeCursor = 'BeforeCursor', // 光标之前的内容
  AfterCursor = 'AfterCursor', // 光标之后的内容
  SimilarFile = 'SimilarFile', // 相似文件的内容
  ImportedFile = 'ImportedFile', // 导入文件的内容
  LanguageMarker = 'LanguageMarker', // 语言标记
  PathMarker = 'PathMarker', // 路径标记
}

/**
 * 表示一个提示元素的接口
 */
interface PromptElement {
  id: number; // 元素的唯一标识符
  text: string; // 元素的文本内容
  kind: PromptElementKind; // 元素的类型
  priority: number; // 元素的优先级
  tokens: number; // 元素包含的令牌数
  uri: string; // 元素的URI（如果适用）
  score: number; // 元素的分数
}

/**
 * 表示转换后的相邻标签信息的接口
 */
interface ConvertedNeighboringTabInfo {
  score: string; // 格式化后的分数
  length: number; // 文本内容的长度
}

interface PromptElementRange {
  kind: PromptElementKind;
  start: number;
  end: number;
}

export class PromptWishlist {
  private content: PromptElement[] = [];
  private lineEndingOption: LineEndingOptionsType;

  /**
   * 创建一个新的PromptWishlist实例
   * @param lineEndingOption 行尾处理选项
   */
  constructor(lineEndingOption: LineEndingOptionsType) {
    this.lineEndingOption = lineEndingOption;
  }

  /**
   * 获取当前内容的副本
   * @returns PromptElement数组的副本
   */
  getContent(): PromptElement[] {
    return [...this.content];
  }

  /**
   * 根据设置的选项转换行尾
   * @param text 要转换的文本
   * @returns 转换后的文本
   */
  private convertLineEndings(text: string): string {
    if (this.lineEndingOption === LineEndingOptionsType.ConvertToUnix) {
      text = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    }
    return text;
  }

  /**
   * 添加一个新的提示元素
   * @param text 元素文本
   * @param kind 元素类型
   * @param priority 优先级
   * @param tokens 令牌数（默认使用tokenUtil计算）
   * @param score 分数（默认为NaN）
   * @returns 新添加元素的ID
   */
  append(kind: PromptElementKind, priority: number, uri: string, text: string, score: number): number {
    const id = this.content.length;
    this.content.push({
      id,
      text: this.convertLineEndings(text),
      kind,
      priority,
      tokens: -1,
      uri,
      score: score,
    });
    return id;
  }
}

/**
 * 管理优先级系统，提供优先级注册和计算功能
 */
export class Priorities {
  static readonly TOP = 1;
  static readonly BOTTOM = 0;

  private registeredPriorities: number[] = [0, 1];

  /**
   * 注册一个新的优先级
   * @param priority 要注册的优先级值
   * @returns 注册的优先级值
   * @throws 如果优先级值不在0到1之间
   */
  register(priority: number): number {
    if (priority > Priorities.TOP || priority < Priorities.BOTTOM) throw new Error('Priority must be between 0 and 1');
    this.registeredPriorities.push(priority);
    return priority;
  }

  /**
   * 计算给定优先级列表中最高优先级的正上方优先级
   * @param priorities 优先级列表
   * @returns 计算得到的新优先级
   */
  justAbove(...priorities: number[]): number {
    const maxPriority = Math.max(...priorities);
    const nextHigherPriority = Math.min(...this.registeredPriorities.filter((p) => p > maxPriority));
    return this.register((nextHigherPriority + maxPriority) / 2);
  }

  /**
   * 计算给定优先级列表中最低优先级的正下方优先级
   * @param priorities 优先级列表
   * @returns 计算得到的新优先级
   */
  justBelow(...priorities: number[]): number {
    const minPriority = Math.min(...priorities);
    const nextLowerPriority = Math.max(...this.registeredPriorities.filter((p) => p < minPriority));
    return this.register((nextLowerPriority + minPriority) / 2);
  }

  /**
   * 计算两个优先级之间的中间优先级
   * @param lowerPriority 较低的优先级
   * @param higherPriority 较高的优先级
   * @returns 计算得到的中间优先级
   * @throws 如果给定的优先级不相邻或不在已注册的优先级列表中
   */
  between(lowerPriority: number, higherPriority: number): number {
    if (
      this.registeredPriorities.some((p) => p > lowerPriority && p < higherPriority) ||
      !this.registeredPriorities.includes(lowerPriority) ||
      !this.registeredPriorities.includes(higherPriority)
    )
      throw new Error('Priorities must be adjacent in the list of priorities');
    return this.register((lowerPriority + higherPriority) / 2);
  }
}
