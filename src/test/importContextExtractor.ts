/**
 * 3055179
 * @file importContextExtractor.ts
 * @description 此文件主要用于处理TypeScript/JavaScript项目中的导入上下文提取和相关操作。
 * 主要功能包括：
 * 1. 解析和处理导入语句
 * 2. 提取本地导入上下文
 * 3. 处理导出声明
 * 4. 解析类、函数和其他声明的结构
 * 5. 缓存和管理导出信息
 *
 * 该模块是Copilot项目的一个重要组成部分，用于分析和理解代码结构，
 * 为代码补全和智能提示提供必要的上下文信息。
 */

import * as path from "path";
import * as languageParserAndQuerier from "./languageParserAndQuerier";
import { MyFileSystem } from "./fileSystemOperations";
// 定义接口
interface ImportItem {
  name: string;
  alias?: string;
}

interface DeclarationInfo {
  name: string;
  decl: string;
}

interface ExportCacheItem {
  mtime: number;
  exports: Map<string, string[]>;
}

interface Document {
  source: string;
  uri: string;
  languageId: string;
}

/**
 * 获取导入路径
 * @param filePath - 文件路径
 * @param importNode - 导入节点
 * @returns 返回处理后的导入路径或null
 */
import * as fs from "fs";
import { SyntaxNode } from "tree-sitter";
import { Tree } from "web-tree-sitter";

function getImportPath(filePath: string, importNode: any): string | null {
  const importPathNode = importNode.namedChild(1);
  let importPath = importPathNode?.text.slice(1, -1);
  if (!importPath || !importPath.startsWith(".")) return null;
  if (path.extname(importPath) === "") importPath += ".ts";
  else if (path.extname(importPath) !== ".ts") return null;
  return path.join(path.dirname(filePath), importPath);
}

export function getRootPath(startPath: string): string {
  let currentPath = startPath;
  while (currentPath.length > 1) {
    const gitPath = path.join(currentPath, ".git");
    const vscodePath = path.join(currentPath, ".vscode");

    if (fs.existsSync(gitPath) || fs.existsSync(vscodePath)) {
      return currentPath;
    }

    const parentPath = path.dirname(currentPath);
    if (parentPath === currentPath) {
      break; // 已经到达文件系统的根目录
    }
    currentPath = parentPath;
  }
  //startPath从后往前截取/src/main/java出现的位置
  const result = startPath.split("/src/main/java")[0];

  const srcPath = path.join(currentPath, "src");
  if (fs.existsSync(srcPath)) {
    return path.dirname(currentPath);
  }

  return ""; // 如果没有找到 .git、.vscode 目录或 src 目录，返回空字符串
}

function findAllJavaSrcDirs(rootPath: string): string[] {
  const result: string[] = [];

  function searchDir(dir: string) {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      if (entry.isDirectory()) {
        if (
          entry.name === "src" &&
          fs.existsSync(path.join(fullPath, "main", "java"))
        ) {
          result.push(path.join(fullPath, "main", "java"));
        } else {
          searchDir(fullPath);
        }
      }
    }
  }

  searchDir(rootPath);
  return result;
}

function findJavaFile(
  currentFilePath: string,
  importPath: string
): string | null {
  const curren = path.dirname(currentFilePath);
  let currentDir = curren.replace("file:", "");
  const rootPath = getRootPath(currentDir);
  const javaSrcDirs = findAllJavaSrcDirs(rootPath);

  for (const srcDir of javaSrcDirs) {
    const fullPath = path.join(srcDir, importPath);
    if (fs.existsSync(fullPath)) {
      return fullPath;
    }
  }

  return null;
}

/**
 * 获取命名导入
 * @param importNode - 导入节点
 * @returns 返回包含导入名称和别名的对象数组
 */
function getNamedImports(importNode: any): ImportItem[] {
  let imports: ImportItem[] = [];
  const importClauseNode = importNode.namedChild(0);
  if (importClauseNode?.type === "import_clause") {
    const importClause = importNode.namedChild(0);
    const namedImportsNode = importClause?.namedChild(0);
    if (namedImportsNode?.type === "named_imports") {
      const namedImports = importClause.namedChild(0);
      for (let importSpecifier of namedImports?.namedChildren ?? []) {
        if (importSpecifier.type === "import_specifier") {
          const name = importSpecifier.childForFieldName("name")?.text;
          if (name) {
            const alias = importSpecifier.childForFieldName("alias")?.text;
            imports.push({ name, alias });
          }
        }
      }
    }
  }
  return imports;
}

const exportCache = new Map<string, ExportCacheItem>();

/**
 * 获取声明信息
 * @param sourceCode - 源代码
 * @param node - AST节点
 * @returns 返回包含声明名称和内容的对象
 */
function getDeclarationInfo(sourceCode: string, node: any): DeclarationInfo {
  const declarationName = node?.childForFieldName("name")?.text ?? "";
  switch (node?.type) {
    case "ambient_declaration":
      return getDeclarationInfo(sourceCode, node.namedChild(0));
    case "interface_declaration":
    case "enum_declaration":
    case "type_alias_declaration":
      return {
        name: declarationName,
        decl: node.text,
      };
    case "function_declaration":
    case "function_signature":
      return {
        name: declarationName,
        decl: getFunctionDeclaration(sourceCode, node),
      };
    case "class_declaration": {
      const classMembers = getClassMembers(sourceCode, node);
      let classDeclaration = "";
      if (classMembers) {
        const bodyNode = node.childForFieldName("body");
        classDeclaration = `declare ${sourceCode.substring(
          node.startIndex,
          bodyNode.startIndex + 1
        )}`;
        classDeclaration += classMembers
          .map((member) => "\n" + member)
          .join("");
        classDeclaration += "\n}";
      }
      return {
        name: declarationName,
        decl: classDeclaration,
      };
    }
  }
  return {
    name: declarationName,
    decl: "",
  };
}

/**
 * 获取类成员
 * @param sourceCode - 源代码
 * @param classNode - 类节点
 * @returns 返回类成员声明数组
 */
function getClassMembers(
  sourceCode: string,
  classNode: any
): string[] | undefined {
  const bodyNode = classNode.childForFieldName("body");
  if (bodyNode) {
    return bodyNode.namedChildren
      .map((memberNode: any) =>
        getClassMemberDeclaration(sourceCode, memberNode)
      )
      .filter((member: string | null): member is string => member !== null);
  }
}

/**
 * 获取函数声明
 * @param sourceCode - 源代码
 * @param node - AST节点
 * @returns 返回函数声明字符串
 */
function getFunctionDeclaration(sourceCode: string, node: any): string {
  const returnTypeNode = node.childForFieldName("return_type");
  const parametersNode = node.childForFieldName("parameters");
  const declarationEndIndex =
    returnTypeNode?.endIndex ?? parametersNode?.endIndex;
  if (declarationEndIndex !== undefined) {
    let declaration =
      sourceCode.substring(node.startIndex, declarationEndIndex) + ";";
    return node.type === "function_declaration" ||
      node.type === "function_signature"
      ? "declare " + declaration
      : declaration;
  }
  return "";
}

/**
 * 获取文档注释
 * @param sourceCode - 源代码
 * @param node - AST节点
 * @returns 返回文档注释字符串
 */
export function getDocComment(sourceCode: string, node: any): string {
  const precedingComment =
    languageParserAndQuerier.getFirstPrecedingComment(node);
  return precedingComment
    ? sourceCode.substring(precedingComment.startIndex, node.startIndex)
    : "";
}

/**
 * 获取类成员声明
 * @param sourceCode - 源代码
 * @param memberNode - 类成员节点
 * @returns 返回类成员声明字符串
 */
function getClassMemberDeclaration(
  sourceCode: string,
  memberNode: any
): string | null {
  if (
    memberNode?.firstChild?.type === "accessibility_modifier" &&
    memberNode.firstChild.text === "private"
  ) {
    return null;
  }
  const precedingComment =
    languageParserAndQuerier.getFirstPrecedingComment(memberNode);
  const memberIndentation =
    getIndentation(sourceCode, precedingComment ?? memberNode) ?? "  ";
  const docComment = getDocComment(sourceCode, memberNode);
  switch (memberNode.type) {
    case "ambient_declaration":
      const ambientDeclaration = memberNode.namedChild(0);
      return ambientDeclaration
        ? memberIndentation +
            docComment +
            getClassMemberDeclaration(sourceCode, ambientDeclaration)
        : null;
    case "method_definition":
    case "method_signature":
      return (
        memberIndentation +
        docComment +
        getFunctionDeclaration(sourceCode, memberNode)
      );
    case "public_field_definition": {
      const typeNode = memberNode.childForFieldName("type");
      const nameNode = memberNode.childForFieldName("name");
      const fieldEndIndex = typeNode?.endIndex ?? nameNode?.endIndex;
      if (fieldEndIndex !== undefined) {
        return (
          memberIndentation +
          docComment +
          sourceCode.substring(memberNode.startIndex, fieldEndIndex) +
          ";"
        );
      }
    }
  }
  return null;
}

/**
 * 获取缩进
 * @param sourceCode - 源代码
 * @param node - AST节点
 * @returns 返回缩进字符串
 */
function getIndentation(sourceCode: string, node: any): string | undefined {
  let index = node.startIndex - 1;
  while (
    index >= 0 &&
    (sourceCode[index] === " " || sourceCode[index] === "\t")
  ) {
    index--;
  }
  if (index < 0 || sourceCode[index] === "\n") {
    return sourceCode.substring(index + 1, node.startIndex);
  }
}

//const importRegex = /^\s*import\s*(type|)\s*\{[^}]*\}\s*from\s*['"]\./gm;
const importRegex =
  /^\s*import\s+(?:(?:type\s+)?(?:{[^}]*}|\*\s+as\s+[^;]+)|[^;]+)\s+from\s+['"][^'"]+['"]/gm;
const importRegexJava =
  /^\s*import\s+(?:static\s+)?[\w.]+(?:\s*\.\s*\*)?\s*;/gm;

/**
 * 提取本地导入上下文
 * @param document - 文档对象
 * @param fileSystem - 文件系统对象
 * @returns 返回本地导入的数组
 */
export async function extractLocalImportContext(
  document: Document,
  fileSystem: MyFileSystem
): Promise<string[]> {
  let { source: sourceCode, uri: documentUri, languageId } = document;
  if (!fileSystem || languageId !== "typescript") {
    return [];
    // return extractLocalImportsForOtherLanguageId(sourceCode,documentUri,fileSystem,languageId);
  }
  return extractLocalImportsForTypeScript(sourceCode, documentUri, fileSystem);
}

/**
 * 为TypeScript提取本地导入
 * @param sourceCode - 源代码
 * @param documentUri - 文档URI
 * @param fileSystem - 文件系统对象
 * @returns 返回本地导入的数组
 */
async function extractLocalImportsForTypeScript(
  sourceCode: string,
  documentUri: string,
  fileSystem: MyFileSystem
): Promise<string[]> {
  let localImports: string[] = [];
  const importEndIndex = findImportEndIndex(sourceCode);
  if (importEndIndex === -1) return localImports;
  sourceCode = sourceCode.substring(0, importEndIndex);
  let parseTree = await languageParserAndQuerier.parseTree(
    "typescript",
    sourceCode
  );
  try {
    if (parseTree) {
      for (let importNode of getImportStatements(parseTree.rootNode)) {
        let importPath = getImportPath(documentUri, importNode);
        if (!importPath) continue;
        let namedImports = getNamedImports(importNode);
        if (namedImports.length === 0) continue;
        let filePath2 = importPath.replace("file:", "");
        // chuli
        // let exports = await getExports(filePath2, "typescript", fileSystem);
        for (let importItem of namedImports) {
          if (exports.has(importItem.name)) {
            localImports.push(...exports.get(importItem.name)!);
          }
        }
      }
    }
  } finally {
    if (parseTree) {
      parseTree.delete();
    }
  }
  return localImports;
}

/**
 * 为TypeScript提取本地导入
 * @param sourceCode - 源代码
 * @param documentUri - 文档URI
 * @param fileSystem - 文件系统对象
 * @returns 返回本地导入的数组
 */
async function extractLocalImports(
  parseTree: Tree,
  sourceCode: string,
  documentUri: string,
  language: string,
  fileSystem: MyFileSystem
): Promise<string[]> {
  let localImports: string[] = [];
  try {
    if (parseTree) {
      let importNode = languageParserAndQuerier.queryImports(
        language,
        parseTree.rootNode
      );
    }
  } finally {
  }
  return localImports;
}
/**
 * 查找导入语句结束的索引
 * @param sourceCode - 源代码
 * @returns 返回导入语句结束的索引
 */
export function findImportEndIndex(
  sourceCode: string,
  languageId?: string
): number {
  let match;
  let lastIndex = -1;

  let importR = importRegex;
  if (languageId === "java") {
    importR = importRegexJava;
  }

  importR.lastIndex = -1;
  do {
    match = importR.exec(sourceCode);
    if (match) {
      lastIndex = importR.lastIndex + match[0].length;
    }
  } while (match);
  if (lastIndex === -1) return -1;
  const newlineIndex = sourceCode.indexOf("\n", lastIndex);
  return newlineIndex !== -1 ? newlineIndex : sourceCode.length;
}

/**
 * 获取导入语句
 * @param rootNode - 根节点
 * @returns 返回导入语句节点数组
 */
function getImportStatements(rootNode: any, languageId?: string): any[] {
  let importStatements: any[] = [];
  let importKey = "import_statement";
  if (languageId === "java") {
    importKey = "import_declaration";
  }
  for (let child of rootNode.namedChildren) {
    if (child.type === importKey) {
      importStatements.push(child);
    }
  }
  return importStatements;
}

/**
 * 获取Java类声明
 * @param sourceCode - 源代码
 * @param classNode - 类节点
 * @returns 返回Java类声明字符串
 */
function getJavaClassDeclaration(sourceCode: string, classNode: any): string {
  const classNameNode = classNode.childForFieldName("name");
  const bodyNode = classNode.childForFieldName("body");
  if (!classNameNode || !bodyNode) return "";

  const classDeclaration = sourceCode.substring(
    classNode.startIndex,
    bodyNode.startIndex + 1
  );
  const members = getJavaClassMembers(sourceCode, bodyNode);

  return `${getDocComment(
    sourceCode,
    classNode
  )}${classDeclaration}${members.join("")}\n}`;
}

/**
 * 获取Java类成员
 * @param sourceCode - 源代码
 * @param bodyNode - 类体节点
 * @returns 返回Java类成员声明数组
 */
function getJavaClassMembers(sourceCode: string, bodyNode: any): string[] {
  const members: string[] = [];
  for (const memberNode of bodyNode.namedChildren) {
    if (
      memberNode.type === "method_declaration" ||
      memberNode.type === "field_declaration"
    ) {
      const memberDeclaration = getJavaMemberDeclaration(
        sourceCode,
        memberNode
      );
      if (memberDeclaration) {
        members.push(memberDeclaration);
      }
    }
  }
  return members;
}

/**
 * 获取Java成员声明
 * @param sourceCode - 源代码
 * @param memberNode - 成员节点
 * @returns 返回Java成员声明字符串
 */
function getJavaMemberDeclaration(
  sourceCode: string,
  memberNode: any
): string | null {
  const modifiers = memberNode.childForFieldName("modifiers");
  if (modifiers && modifiers.text.includes("private")) {
    return null;
  }

  const docComment = getDocComment(sourceCode, memberNode);
  const memberDeclaration = sourceCode.substring(
    memberNode.startIndex,
    memberNode.endIndex
  );
  return `\n  ${docComment}${memberDeclaration}`;
}
