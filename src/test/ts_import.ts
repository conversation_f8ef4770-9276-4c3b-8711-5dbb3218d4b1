import * as fs from "fs";
import * as path from "path";
import * as languageParserAndQuerier from "./languageParserAndQuerier";
import * as importContextExtractor from "./importContextExtractor";

//@ts-ignore
import Parser, { SyntaxNode } from "tree-sitter";
import { TreeCursor } from "web-tree-sitter";

const language_type = languageParserAndQuerier.WASMLanguage.Java;

export function getImportPath(node: SyntaxNode): string[] {
  const result: string[] = [];
  let importNodeArray = languageParserAndQuerier.queryImports(
    language_type,
    node
  );
  //遍历importNodeArray ,获取导入的路径
  for (const importNode of importNodeArray) {
    // 获取导入路径
    importNode.captures[0].node.toString();
    const pathNode = importNode.captures[0].node;

    if (pathNode.childCount > 4) {
      result.push(
        pathNode.child(1).text + pathNode.child(2).text + pathNode.child(3).text
      );
    } else {
      result.push(pathNode.child(1).text);
    }
  }
  // 对result排序，后缀为.*的放在前面
  result.sort((a, b) => {
    if (a.endsWith(".*") && !b.endsWith(".*")) {
      return -1; // a 在 b 前面
    } else if (!a.endsWith(".*") && b.endsWith(".*")) {
      return 1; // b 在 a 前面
    } else {
      return a.localeCompare(b); // 字符串比较
    }
  });
  return result;
}

export function getAbsolutePathForImport(
  importPath: string[],
  srcPath: string[]
): string[] {
  const result: string[] = [];

  for (const importItem of importPath) {
    for (const srcItem of srcPath) {
      const fullPath = path.join(srcItem, importItem);
      if (fs.existsSync(fullPath)) {
        result.push(fullPath);
        break; // 找到一个存在的路径就继续下一个 importPath
      }
    }
  }
  return result;
}

export function getDirPathForAccessType(
  absoluteImportPath: string[],
  accessFieldTypes: string[]
): string[] {
  const result: string[] = [];

  for (const typeItem of accessFieldTypes) {
    for (const dirPath of absoluteImportPath) {
      const fullPath = path.join(dirPath, typeItem);
      if (fs.existsSync(fullPath)) {
        result.push(fullPath);
        break; // 找到一个存在的类型定义文件就继续下一个 type
      }
    }
  }
  return result;
}
