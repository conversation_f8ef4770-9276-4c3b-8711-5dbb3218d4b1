/**
 * id:3055306
 * languageParserAndQuerier.ts。这个名字反映了文件的两个主要功能：
 *
 * 语言解析（Language Parsing）：文件包含了对多种编程语言进行语法分析和抽象语法树（AST）生成的功能。
 *
 * 代码查询（Code Querying）：文件提供了多种查询功能，如查询函数、导入语句、导出语句和全局变量等。
 * @fileoverview 语言解析和查询模块
 *
 * 该模块提供了对多种编程语言的解析和查询功能，包括：
 * 1. 语言识别和转换
 * 2. 抽象语法树（AST）解析
 * 3. 代码结构查询（如函数、导入、导出等）
 * 4. 语言特定的辅助函数
 *
 * 主要支持的语言包括 Python, JavaScript, TypeScript, Go 和 Ruby。
 */

import * as path from "path";
import { Parser, Language, Tree, Node, Query } from "web-tree-sitter";

/**
 * 支持的WebAssembly语言枚举
 */
export enum WASMLanguage {
  Python = "python",
  JavaScript = "javascript",
  TypeScript = "typescript",
  Go = "go",
  Ruby = "ruby",
  Java = "java",
}

/**
 * 语言ID到WASM语言的映射
 */
const languageIdToWasmLanguageMap: Record<string, WASMLanguage> = {
  python: WASMLanguage.Python,
  javascript: WASMLanguage.JavaScript,
  javascriptreact: WASMLanguage.JavaScript,
  jsx: WASMLanguage.JavaScript,
  typescript: WASMLanguage.TypeScript,
  typescriptreact: WASMLanguage.TypeScript,
  go: WASMLanguage.Go,
  ruby: WASMLanguage.Ruby,
  java: WASMLanguage.Java,
};

/**
 * 将语言ID转换为WASM语言
 * @param languageId 语言ID
 * @returns WASM语言
 */
export function languageIdToWasmLanguage(languageId: string): WASMLanguage {
  if (!(languageId in languageIdToWasmLanguageMap)) {
    throw new Error(`Unrecognized language: ${languageId}`);
  }
  return languageIdToWasmLanguageMap[languageId];
}

/**
 * 检查语言ID是否被支持
 * @param languageId 语言ID
 * @returns 是否支持
 */
export function isSupportedLanguageId(languageId: string): boolean {
  return languageId in languageIdToWasmLanguageMap;
}

/**
 * 函数查询模式
 */
const functionQueryPatterns: Record<WASMLanguage, [string, any?][]> = {
  [WASMLanguage.Python]: [
    [
      "(function_definition body: (block\n             (expression_statement (string))? @docstring) @body) @function",
    ],
    ['(ERROR ("def" (identifier) (parameters))) @function'],
  ],
  [WASMLanguage.JavaScript]: [
    [
      "[\n            (function body: (statement_block) @body)\n            (function_declaration body: (statement_block) @body)\n            (generator_function body: (statement_block) @body)\n            (generator_function_declaration body: (statement_block) @body)\n            (method_definition body: (statement_block) @body)\n          ] @function",
    ],
  ],
  [WASMLanguage.TypeScript]: [
    [
      "[\n            (function body: (statement_block) @body)\n            (function_declaration body: (statement_block) @body)\n            (generator_function body: (statement_block) @body)\n            (generator_function_declaration body: (statement_block) @body)\n            (method_definition body: (statement_block) @body)\n          ] @function",
    ],
  ],
  [WASMLanguage.Go]: [
    [
      "[\n            (function_declaration body: (block) @body)\n            (method_declaration body: (block) @body)\n          ] @function",
    ],
  ],
  [WASMLanguage.Ruby]: [
    [
      '[\n            (method name: (_) parameters: (method_parameters)? @params [(_)+ "end"] @body)\n            (singleton_method name: (_) parameters: (method_parameters)? @params [(_)+ "end"] @body)\n          ] @function',
    ],
  ],
  [WASMLanguage.Java]: [
    [
      "[\n            (method_declaration body: (block) @body)\n            (constructor_declaration body: (constructor_body) @body)\n          ] @function",
    ],
  ],
};

/**
 * JavaScript/TypeScript require模式
 */
const requirePattern =
  '(variable_declarator value: (call_expression function: ((identifier) @req (#eq? @req "require"))))';
const declarationPattern = `\n    (lexical_declaration ${requirePattern}+)\n    (variable_declaration ${requirePattern}+)\n`;

/**
 * 导入查询模式
 */
const importQueryPatterns: Record<WASMLanguage, [string, any?][]> = {
  [WASMLanguage.Python]: [
    ["(module (future_import_statement) @import)"],
    ["(module (import_statement) @import)"],
    ["(module (import_from_statement) @import)"],
  ],
  [WASMLanguage.JavaScript]: [
    [`(program [ ${declarationPattern} ] @import)`],
    ["(program [ (import_statement) ] @import)"],
  ],
  [WASMLanguage.TypeScript]: [
    [`(program [ ${declarationPattern} ] @import)`],
    ["(program [ (import_statement) (import_alias) ] @import)"],
  ],
  [WASMLanguage.Go]: [],
  [WASMLanguage.Ruby]: [],
  [WASMLanguage.Java]: [["(program (import_declaration) @import)"]],
};

/**
 * 导出查询模式
 */
const exportQueryPatterns: Record<WASMLanguage, [string, any?][]> = {
  [WASMLanguage.Python]: [],
  [WASMLanguage.JavaScript]: [["(program (export_statement) @export)"]],
  [WASMLanguage.TypeScript]: [["(program (export_statement) @export)"]],
  [WASMLanguage.Go]: [],
  [WASMLanguage.Ruby]: [],
  [WASMLanguage.Java]: [],
};

/**
 * 全局变量查询模式
 */
const globalVarQueryPatterns: Record<WASMLanguage, [string, any?][]> = {
  [WASMLanguage.Python]: [
    ["(module (global_statement) @globalVar)"],
    ["(module (expression_statement) @globalVar)"],
  ],
  [WASMLanguage.JavaScript]: [],
  [WASMLanguage.TypeScript]: [],
  [WASMLanguage.Go]: [],
  [WASMLanguage.Ruby]: [],
  [WASMLanguage.Java]: [["(program (field_declaration) @globalVar)"]],
};

/**
 * 类型标识符查询模式
 */
const typeIdentifierQueryPatterns: Record<WASMLanguage, [string, any?][]> = {
  [WASMLanguage.Python]: [["(type_identifier) @typeIdentifier"]],
  [WASMLanguage.JavaScript]: [["(type_identifier) @typeIdentifier"]],
  [WASMLanguage.TypeScript]: [["(type_identifier) @typeIdentifier"]],
  [WASMLanguage.Go]: [["(type_identifier) @typeIdentifier"]],
  [WASMLanguage.Ruby]: [], // Ruby doesn't have explicit type identifiers
  [WASMLanguage.Java]: [["(type_identifier) @typeIdentifier"]],
};

/**
 * 函数类型集合
 */
const functionTypes: Record<WASMLanguage, Set<string>> = {
  [WASMLanguage.Python]: new Set(["function_definition"]),
  [WASMLanguage.JavaScript]: new Set([
    "function",
    "function_declaration",
    "generator_function",
    "generator_function_declaration",
    "method_definition",
    "arrow_function",
  ]),
  [WASMLanguage.TypeScript]: new Set([
    "function",
    "function_declaration",
    "generator_function",
    "generator_function_declaration",
    "method_definition",
    "arrow_function",
  ]),
  [WASMLanguage.Go]: new Set(["function_declaration", "method_declaration"]),
  [WASMLanguage.Ruby]: new Set(["method", "singleton_method"]),
  [WASMLanguage.Java]: new Set([
    "method_declaration",
    "constructor_declaration",
  ]),
};

/**
 * 判断节点是否可以包含兄弟函数的函数
 */
type NodePredicate = (node: any) => boolean;

const ancestorWithSiblingFunctionsPredicates: Record<
  WASMLanguage,
  NodePredicate
> = {
  [WASMLanguage.Python]: (node) => {
    return (
      "module" === node.type ||
      ("block" === node.type && "class_definition" === node.parent?.type)
    );
  },
  [WASMLanguage.JavaScript]: (node) =>
    "program" === node.type || "class_body" === node.type,
  [WASMLanguage.TypeScript]: (node) =>
    "program" === node.type || "class_body" === node.type,
  [WASMLanguage.Go]: (node) => "source_file" === node.type,
  [WASMLanguage.Ruby]: (node) =>
    "program" === node.type || "class" === node.type,
  [WASMLanguage.Java]: (node) =>
    "program" === node.type || "class_body" === node.type,
};

/**
 * 语言缓存
 */
const languageCache = new Map<WASMLanguage, Language>();

/**
 * 获取语言
 * @param languageId 语言ID
 * @returns 语言对象
 */
export async function getLanguage(languageId: string): Promise<Language> {
  const wasmLanguage = languageIdToWasmLanguage(languageId);
  if (!languageCache.has(wasmLanguage)) {
    const language = await loadLanguage(wasmLanguage);
    languageCache.set(wasmLanguage, language);
  }
  return languageCache.get(wasmLanguage)!;
}

/**
 * 加载语言
 * @param wasmLanguage WASM语言
 * @returns 语言对象
 */
async function loadLanguage(wasmLanguage: WASMLanguage): Promise<Language> {
  await Parser.init();
  const wasmPath = path.resolve(
    __dirname,
    "..",
    `tree-sitter-${wasmLanguage}.wasm`
  );
  return await Language.load(wasmPath);
}

/**
 * 解析树
 * @param languageId 语言ID
 * @param text 文本
 * @returns 解析树
 */
export async function parseTree(
  languageId: string,
  text: string
): Promise<Tree | null | any> {
  const language = await getLanguage(languageId);
  const parser = new Parser();
  parser.setLanguage(language);
  const tree = parser.parse(text);
  parser.delete();
  return tree;
}

/**
 * 检查是否可以无错误解析
 * @param languageId 语言ID
 * @param text 文本
 * @returns 是否可以无错误解析
 */
export async function parsesWithoutError(
  languageId: string,
  text: string
): Promise<boolean> {
  const tree = await parseTree(languageId, text);
  if (tree === null) {
    return false;
  }
  const result = !tree.rootNode.hasError;
  tree.delete();
  return result;
}

/**
 * 获取块关闭标记
 * @param languageId 语言ID
 * @returns 块关闭标记
 */
export function getBlockCloseToken(languageId: string): string | null {
  switch (languageIdToWasmLanguage(languageId)) {
    case WASMLanguage.Python:
      return null;
    case WASMLanguage.JavaScript:
    case WASMLanguage.TypeScript:
    case WASMLanguage.Go:
    case WASMLanguage.Java:
      return "}";
    case WASMLanguage.Ruby:
      return "end";
    default:
      return null;
  }
}

/**
 * 执行查询
 * @param patterns 查询模式
 * @param node 节点
 * @returns 查询结果
 */
function executeQuery(patterns: [string, any?][], node: Node): any[] {
  const matches: any[] = [];
  for (const pattern of patterns) {
    if (!pattern[1]) {
      const language = node.tree.language;
      pattern[1] = language.query(pattern[0]);
    }
    matches.push(...pattern[1].matches(node));
  }
  return matches;
}

/**
 * 查询函数
 * @param languageId 语言ID
 * @param node 节点
 * @returns 查询结果
 */
export function queryFunctions(languageId: string, node: Node): any[] {
  return executeQuery(
    functionQueryPatterns[languageIdToWasmLanguage(languageId)],
    node
  );
}

/**
 * 查询导入
 * @param languageId 语言ID
 * @param node 节点
 * @returns 查询结果
 */
export function queryImports(languageId: string, node: any): any[] {
  return executeQuery(
    importQueryPatterns[languageIdToWasmLanguage(languageId)],
    node
  );
}

/**
 * 查询导出
 * @param languageId 语言ID
 * @param node 节点
 * @returns 查询结果
 */
export function queryExports(languageId: string, node: any): any[] {
  return executeQuery(
    exportQueryPatterns[languageIdToWasmLanguage(languageId)],
    node
  );
}

/**
 * 查询全局变量
 * @param languageId 语言ID
 * @param node 节点
 * @returns 查询结果
 */
export function queryGlobalVars(languageId: string, node: any): any[] {
  return executeQuery(
    globalVarQueryPatterns[languageIdToWasmLanguage(languageId)],
    node
  );
}

/**
 * 查询类型标识符
 * @param languageId 语言ID
 * @param node 节点
 * @returns 查询结果
 */
export function queryTypeIdentifiers(languageId: string, node: any): any[] {
  return executeQuery(
    typeIdentifierQueryPatterns[languageIdToWasmLanguage(languageId)],
    node
  );
}

/**
 * Python docstring查询模式
 */
const pythonDocstringQueryPattern: [string, any?] = [
  "[\n    (class_definition (block (expression_statement (string))))\n    (function_definition (block (expression_statement (string))))\n]",
];

/**
 * 查询Python docstring
 * @param node 节点
 * @returns 是否是docstring
 */
export function queryPythonIsDocstring(node: any): boolean {
  return executeQuery([pythonDocstringQueryPattern], node).length === 1;
}

/**
 * 获取具有兄弟函数的祖先节点
 * @param languageId 语言ID
 * @param node 节点
 * @returns 祖先节点
 */
export function getAncestorWithSiblingFunctions(
  languageId: string,
  node: any
): any {
  const predicate =
    ancestorWithSiblingFunctionsPredicates[
      languageIdToWasmLanguage(languageId)
    ];
  while (node.parent) {
    if (predicate(node.parent)) {
      return node;
    }
    node = node.parent;
  }
  return node.parent ? node : null;
}

/**
 * 判断节点是否是函数
 * @param languageId 语言ID
 * @param node 节点
 * @returns 是否是函数
 */
export function isFunction(languageId: string, node: any): boolean {
  return functionTypes[languageIdToWasmLanguage(languageId)].has(node.type);
}

/**
 * 判断节点是否是函数定义
 * @param languageId 语言ID
 * @param node 节点
 * @returns 是否是函数定义
 */
export function isFunctionDefinition(languageId: string, node: any): boolean {
  switch (languageIdToWasmLanguage(languageId)) {
    case WASMLanguage.Python:
    case WASMLanguage.Go:
    case WASMLanguage.Ruby:
      return isFunction(languageId, node);
    case WASMLanguage.JavaScript:
    case WASMLanguage.TypeScript:
      if (
        "function_declaration" === node.type ||
        "generator_function_declaration" === node.type ||
        "method_definition" === node.type
      )
        return true;
      if (
        "lexical_declaration" === node.type ||
        "variable_declaration" === node.type
      ) {
        if (node.namedChildCount > 1) return false;
        let n = node.namedChild(0);
        if (null == n) return false;
        let r = n.namedChild(1);
        return null !== r && isFunction(languageId, r);
      }
      if ("expression_statement" === node.type) {
        let n = node.namedChild(0);
        if ("assignment_expression" === (null == n ? undefined : n.type)) {
          let t = n.namedChild(1);
          return null !== t && isFunction(languageId, t);
        }
      }
      return false;
  }
  return false;
}

/**
 * 获取前面的注释
 * @param node 节点
 * @returns 注释节点
 */
export function getFirstPrecedingComment(node: any): any {
  let n = node;
  while (n.previousSibling && "comment" === n.previousSibling.type) {
    let e = n.previousSibling;
    if (e.endPosition.row < n.startPosition.row - 1) break;
    n = e;
  }
  return "comment" === (null == n ? undefined : n.type) ? n : null;
}

/**
 * 获取函数位置
 * @param languageId 语言ID
 * @param text 文本
 * @returns 函数位置数组
 */
export async function getFunctionPositions(
  languageId: string,
  text: string
): Promise<{ startIndex: number; endIndex: number }[]> {
  const tree = await parseTree(languageId, text);
  if (tree === null) {
    return [];
  }
  return queryFunctions(languageId, tree.rootNode).map((e: any) => {
    const t = e.captures.find((e: any) => "function" === e.name).node;
    return {
      startIndex: t.startIndex,
      endIndex: t.endIndex,
    };
  });
}
