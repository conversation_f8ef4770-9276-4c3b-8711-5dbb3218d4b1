async *createMessage(systemPrompt: string, messages: Anthropic.Messages.MessageParam[]): ApiStream {
		const openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [
			{ role: "system", content: systemPrompt },

			...convertToOpenAiMessages(messages),


		]
		console.log("openAiMessages: " + JSON.stringify(openAiMessages, null, 2))  // 使用 JSON.stringify 并格式化输出
		const stream = await this.client.chat.completions.create({
			model: this.options.openAiModelId ?? "",


			messages: openAiMessages,
			temperature: 0,
			stream: true,
			stream_options: { include_usage: true },	


		})
		for await (const chunk of stream) {
			const delta = chunk.choices[0]?.delta
			if (delta?.content) {
				yield {
					type: "text",
					text: delta.content,
				}
			}
			if (chunk.usage) {
				yield {
					type: "usage",
					inputTokens: chunk.usage.prompt_tokens || 0,
					outputTokens: chunk.usage.completion_tokens || 0,
				}
			}
		}
	}
