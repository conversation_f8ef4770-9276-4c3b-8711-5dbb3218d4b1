import * as fs from "fs";
import * as languageParserAndQuerier from "./languageParserAndQuerier";
//@ts-ignore
import Parser from "tree-sitter";
import { TreeCursor } from "web-tree-sitter";

// 加载文件内容
const sourceCode = fs.readFileSync(
  "/Users/<USER>/Documents/workspace/auto-complete/src/input.ts",
  "utf8"
);
//@ts-ignore
var tree = undefined;
(async () => {
  tree = (await languageParserAndQuerier.parseTree(
    languageParserAndQuerier.WASMLanguage.Java,
    sourceCode
  )) as any;

  // 存储修改后的代码片段
  let modifiedCode = "";
  let lastIndex = 0;
  function traverseTree(cursor: TreeCursor) {
    do {
      const node = cursor.currentNode;
      console.log(node.type, node.startPosition, node.endPosition);
      if (
        node.type === "function_declaration" ||
        node.type === "method_definition" ||
        node.type === "method_declaration" ||
        node.type === "constructor"
      ) {
        const body = node.childForFieldName("body");
        if (body) {
          // 提取方法签名
          const bodyStart = body.startIndex;
          const bodyEnd = body.endIndex;

          // 保留方法签名和参数，移除方法体
          modifiedCode += sourceCode.slice(lastIndex, bodyStart) + ";";
          lastIndex = bodyEnd;
        }
      } else {
        if (cursor.gotoFirstChild()) {
          // 如果有子节点，递归进入子节点
          traverseTree(cursor);
          cursor.gotoParent(); // 回到父节点以继续遍历兄弟节点
        }
      }
    } while (cursor.gotoNextSibling());
  }
  // 开始遍历
  traverseTree(tree.rootNode.walk());
  // 遍历语法树，移除方法实现
  console.log(12);
  console.log(12);

  // 添加剩余的代码
  modifiedCode += sourceCode.slice(lastIndex);

  // 输出到文件
  fs.writeFileSync("./output.ts", modifiedCode);

  // 测试 rootNodeWithOffset 和 walk 方法
  console.log("\n测试 rootNodeWithOffset 和 walk 方法：");
  const offsetBytes = 10; // 偏移10个字节
  const offsetExtent = { row: 1, column: 0 }; // 偏移1行
  const offsetRoot = tree.rootNodeWithOffset(offsetBytes, offsetExtent);

  function traverseOffsetTree(cursor: TreeCursor) {
    do {
      const node = cursor.currentNode;
      console.log(
        `类型: ${node.type}, 开始位置: ${JSON.stringify(
          node.startPosition
        )}, 结束位置: ${JSON.stringify(node.endPosition)}`
      );
      if (cursor.gotoFirstChild()) {
        traverseOffsetTree(cursor);
        cursor.gotoParent();
      }
    } while (cursor.gotoNextSibling());
  }

  console.log("偏移后的语法树遍历结果：");
  traverseOffsetTree(offsetRoot.walk());
})();
