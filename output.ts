
public interface MyInterface {
    // 常量字段（接口中的字段默认是 public static final）
    String DEFAULT_NAME = "Default";
    int MAX_SIZE = 100;

    // 抽象方法（不需要显式使用 abstract 关键字）
    void processData(String data);
    int calculateValue(int input);

    // 默认实现方法（使用 default 关键字）
    default String getDefaultName(String) ;

    default void printInfo() {
        System.out.println("Processing with max size: " + MAX_SIZE);

                System.out.println("Processing with max size: " + MAX_SIZE);

    }

    // 静态方法
    static boolean isValidInput(String input) ;
}