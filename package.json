{"name": "auto-complete", "displayName": "Auto Complete", "description": "A TypeScript-based auto-completion extension for VSCode", "version": "0.0.1", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "publisher": "undefined_publisher", "enabledApiProposals": ["inlineCompletionsAdditions"], "activationEvents": ["onLanguage:typescript", "onLanguage:javascript"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "auto-complete.test", "title": "Test Auto Complete Extension"}], "configuration": [{"title": "Copilot", "properties": {"github.copilot.advanced": {"type": "object", "title": "Advanced Settings", "properties": {"secret_key": {"type": "string", "default": "", "description": "Secret API key"}, "length": {"type": "integer", "default": 500, "description": "Length of code to generate in tokens"}, "temperature": {"type": "string", "default": "", "description": "Override sampling temperature (range 0.0 - 1.0)"}, "top_p": {"type": "number", "default": 1, "description": "Top probability mass to consider"}, "stops": {"type": "object", "default": {"": ["\n\n\n"], "python": ["\ndef ", "\nclass ", "\nif ", "\n\n#"]}, "description": "Configure per-language stop sequences"}, "indentationMode": {"type": "object", "default": {"python": false, "javascript": false, "javascriptreact": false, "jsx": false, "typescript": false, "typescriptreact": false, "go": false, "ruby": false, "": true}, "markdownDescription": "Enable or disable indentation block termination for specified languages. Set to 'clientandserver' to run both parser-based and indent-based termination."}, "inlineSuggestCount": {"type": "integer", "default": 3, "description": "Number of inline suggestions to fetch"}, "listCount": {"type": "integer", "default": 10, "description": "Number of solutions to list in Open GitHub Copilot"}, "debug.showScores": {"type": "boolean", "default": false, "description": "Show scores in sorted solutions"}, "debug.overrideEngine": {"type": "string", "default": 10, "description": "Override engine name"}, "debug.overrideProxyUrl": {"type": "string", "default": "", "description": "Override GitHub authentication proxy full URL"}, "debug.testOverrideProxyUrl": {"type": "string", "default": "", "description": "Override GitHub authentication proxy URL when running tests"}, "debug.filterLogCategories": {"type": "array", "default": [], "description": "Show only log categories listed in this setting. If an array is empty, show all loggers"}, "debug.useSuffix": {"type": "boolean", "default": true, "description": "useSuffix "}}}, "github.copilot.enable": {"type": "object", "default": {"": true, "yaml": false, "plaintext": false, "markdown": false, "java": true}, "markdownDescription": "Enable or disable Copilot for specified languages"}, "github.copilot.inlineSuggest.enable": {"type": "boolean", "default": true, "description": "Show inline suggestions"}}}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./ && cp src/*.wasm src/*.bpe  src/tokenizer.json src/vocab.bpe dist/", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./dist/test/runTest.js"}, "devDependencies": {"@adobe/helix-fetch": "github:bmuskalla/helix-fetch#4e33e47bf6e64b637d5d713558fde504bf71c947", "@types/emscripten": "^1.40.1", "@types/git-url-parse": "^9.0.1", "@types/glob": "^7.1.3", "@types/mocha": "^8.2.2", "@types/node": "^13.11.0", "@types/tunnel": "^0.0.6", "@types/uuid": "^8.3.2", "@types/vscode": "^1.60.0", "@typescript-eslint/eslint-plugin": "^4.26.0", "@typescript-eslint/parser": "^4.26.0", "eslint": "^7.27.0", "git-url-parse": "^13.1.0", "glob": "^7.1.7", "mocha": "^8.4.0", "tunnel": "^0.0.6", "typescript": "^4.3.2", "uuid": "^8.3.2", "vscode-test": "^1.5.2"}, "dependencies": {"@dqbd/tiktoken": "^1.0.20", "@types/sha256": "^0.2.2", "natural": "^8.0.1", "sha256": "^0.2.0", "tiktoken": "^1.0.20", "tree-sitter": "^0.22.4", "web-tree-sitter": "^0.25.3"}}