{"version": "0.2.0", "configurations": [{"name": "Run Extension", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}", "--enable-proposed-api=undefined_publisher.auto-complete"], "outFiles": ["${workspaceFolder}/dist/**/*.js"], "preLaunchTask": "${defaultBuildTask}"}, {"name": "Debug Tree-<PERSON><PERSON>", "type": "node", "request": "launch", "args": ["/Users/<USER>/Documents/workspace/auto-complete/dist/test/ts_parse.js"], "preLaunchTask": "tsc: build - tsconfig.json", "cwd": "${workspaceFolder}", "protocol": "inspector", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Tfldf", "type": "node", "request": "launch", "args": ["/Users/<USER>/Documents/workspace/auto-complete/dist/test/TF-IDF.js"], "preLaunchTask": "tsc: build - tsconfig.json", "cwd": "${workspaceFolder}", "protocol": "inspector", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "ts_parse_1s", "type": "node", "request": "launch", "args": ["/Users/<USER>/Documents/workspace/auto-complete/dist/test/ts_parse_1.js"], "preLaunchTask": "tsc: build - tsconfig.json", "cwd": "${workspaceFolder}", "protocol": "inspector", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}]}